name: Get Schema
on:
  workflow_dispatch:
  push:
    branches:
      - develop
      - staging
      - master

jobs:
  schema:
    runs-on: [self-hosted, cicd]
    strategy:
      matrix:
        python-version: ["3.13"]
    steps:
      - name: Echo ref
        run: |
          echo ${{ github.ref }}
          echo ${{ github.event }}

      - name: Set env to develop
        if: endsWith(github.ref, '/develop')
        run: |
          echo "RUN_ENV=dev" >> $GITHUB_ENV
      - name: Set env to staging
        if: endsWith(github.ref, '/staging')
        run: |
          echo "RUN_ENV=staging" >> $GITHUB_ENV
      - name: Set env to production
        if: endsWith(github.ref, '/master')
        run: |
          echo "RUN_ENV=production" >> $GITHUB_ENV

      - name: Create subdirectory
        run: |
          echo $(pwd)
          rm -rf get_schema
          mkdir get_schema

      - name: Checkout latest code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
          path: get_schema

      - name: Get Current Time
        uses: josStorer/get-current-time@v2.1.1
        id: current-time
        with:
          format: YYYYMMDD

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          cd get_schema
          pip install $(grep -E "^(opensearch-py\[async\]|pydantic|pydantic-settings|python-dotenv|pytidylib)" services/base/requirements.txt)
          pip install -e .

      - name: Get index mappings
        run: |
          cd get_schema
          pwd
          make get_os_schema

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        env:
          F_TIME: "os_schema_${{ steps.current-time.outputs.formattedTime }}"
        with:
          name: ${{ env.F_TIME }}
          path: ./get_schema/services/base/infrastructure/database/opensearch/os_schema.json
          if-no-files-found: error

  slack-workflow-status:
    if: failure()
    name: Post Workflow Status To Slack
    needs:
      - schema
    runs-on: ubuntu-latest
    steps:
      - name: Slack Workflow Notification
        uses: Gamesight/slack-workflow-status@master
        with:
          # Required Input
          repo_token: ${{secrets.GITHUB_TOKEN}}
          slack_webhook_url: ${{secrets.SLACK_WEBHOOK_FOUNDATION_TESTING}}
          # Optional Input
          name: "Github Bot"
          include_jobs: "on-failure"
          include_commit_message: "true"
