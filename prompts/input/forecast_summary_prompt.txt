Analyze the input data and generate a environment forecast in JSON format.

The input data is a list EnvironmentForecastBucket objects, bucket OpenAPI schema:
{'$defs': {'CoordinatesModel': {'properties': {'lat': {'maximum': 90.0, 'minimum': -90.0, 'title': 'Lat', 'type': 'number'}, 'lon': {'maximum': 180.0, 'minimum': -180.0, 'title': 'Lon', 'type': 'number'}}, 'required': ['lat', 'lon'], 'title': 'CoordinatesModel', 'type': 'object'}}, 'properties': {'timestamp': {'format': 'date-time', 'title': 'Timestamp', 'type': 'string'}, 'coordinates': {'$ref': '#/$defs/CoordinatesModel'}, 'pressure_change': {'anyOf': [{'type': 'number'}, {'type': 'null'}], 'description': 'rate of change of barometric pressure in hPa/h. Stable: <= 1, Medium: < 3, High: > 3. Health impact', 'title': 'Pressure Change'}, 'tree_pollen': {'anyOf': [{'minimum': 0.0, 'type': 'number'}, {'type': 'null'}], 'description': 'in grains per cubic meter of air. Low: <= 15, Moderate: <= 90, High: > 90. Health impact', 'title': 'Tree Pollen'}, 'weed_pollen': {'anyOf': [{'minimum': 0.0, 'type': 'number'}, {'type': 'null'}], 'description': 'in grains per cubic meter of air. Low: <= 15, Moderate: <= 90, High: > 90. Health impact', 'title': 'Weed Pollen'}, 'grass_pollen': {'anyOf': [{'minimum': 0.0, 'type': 'number'}, {'type': 'null'}], 'description': 'in grains per cubic meter of air. Low: <= 15, Moderate: <= 90, High: > 90. Health impact', 'title': 'Grass Pollen'}, 'uv': {'anyOf': [{'minimum': 0.0, 'type': 'number'}, {'type': 'null'}], 'description': 'uv index. Low: <= 3, Moderate: <= 7, High: > 7. Health impact', 'title': 'Uv'}, 'aqi_gb': {'anyOf': [{'maximum': 1000.0, 'minimum': 0.0, 'type': 'number'}, {'type': 'null'}], 'description': 'air quality based on US standard. Good: <= 50, Moderate: <= 100, Unhealthy > 100. Health impact', 'title': 'Aqi Gb'}, 'precipitation': {'anyOf': [{'type': 'number'}, {'type': 'null'}], 'title': 'Precipitation'}, 'temperature': {'anyOf': [{'type': 'number'}, {'type': 'null'}], 'description': 'temperature in celsius', 'title': 'Temperature'}, 'wind': {'anyOf': [{'type': 'number'}, {'type': 'null'}], 'description': 'wind speed in km/h', 'title': 'Wind'}, 'humidity': {'anyOf': [{'maximum': 100.0, 'minimum': 0.0, 'type': 'number'}, {'type': 'null'}], 'description': 'relative humidity', 'title': 'Humidity'}, 'visibility': {'anyOf': [{'type': 'number'}, {'type': 'null'}], 'description': 'visibility in meters', 'title': 'Visibility'}}, 'required': ['timestamp', 'coordinates', 'pressure_change', 'tree_pollen', 'weed_pollen', 'grass_pollen', 'uv', 'aqi_gb', 'precipitation', 'temperature', 'wind', 'humidity', 'visibility'], 'title': 'EnvironmentForecastBucket', 'type': 'object'}

The output must strictly follow this OpenAPI schema:
{'properties': {'title': {'maxLength': 40, 'minLength': 1, 'title': 'Title', 'type': 'string'}, 'body': {'maxLength': 250, 'minLength': 1, 'title': 'Body', 'type': 'string'}}, 'required': ['title', 'body'], 'title': 'EvaluatedForecastMessage', 'type': 'object'}

- The response must be concise, informative and **not exceed the given constraints like max length **
- Do not include null values
- Focus primarily on health impacts if levels are moderate or high
- Summarize other environmental factors if relevant
- Do not suggest residents what to do
- Do not be dramatic

Example outputs:
{
"title": "Hot and sunny with strong UV",
"body": "Highs of 28-32°C with strong UV (index 8-9). Clear skies, moderate humidity, and light winds. Air quality moderate (AQI ~100)."
},
{
"title": "Mild and stable conditions",
"body": "Temperatures between 10-20°C with clear skies, low humidity, and steady pressure. No major disturbances expected today."
},
{
"title": "Cold and dry with clear skies",
"body": "Temperatures ranging from -10 to 0°C, low humidity, and stable pressure. Light winds with good visibility throughout the day."
},
{
"title": "Rainy and windy conditions",
"body": "Expect 10-15°C with high humidity, heavy rain, and wind gusts up to 40 km/h. Pressure dropping by 12hPa. Air quality moderate."
},
{
"title": "Severe storm warning",
"body": "Thunderstorms with wind gusts over 70 mph, large hail, and heavy rain expected. Air quality may worsen. Stay alert for sudden changes."
},
{
"title": "High pollen and pollution levels",
"body": "Grass pollen levels above 250 grains/m³ and AQI over 100 indicate poor air quality. Sensitive groups should take precautions."
},
{
"title": "Freezing temperatures ahead",
"body": "Temperatures from -18°C to -12°C with light winds. Possible snowfall but clear visibility. Extreme cold—dress warmly."
}

# User forecast data
{
  "buckets" : [ {
    "timestamp" : "2025-05-23T19:00:00.000+21:00",
    "coordinates" : {
      "lat" : 40.90001,
      "lon" : 74.100006
    },
    "pressure_change" : 1.900000000000091,
    "tree_pollen" : 0.0,
    "weed_pollen" : 0.0,
    "grass_pollen" : 0.0,
    "uv" : 8.0,
    "aqi_gb" : 47.0,
    "precipitation" : 1.4,
    "temperature" : 5.4,
    "wind" : 5.2,
    "humidity" : 91.0,
    "visibility" : 24140.0
  }, {
    "timestamp" : "2025-05-23T20:00:00.000+21:00",
    "coordinates" : {
      "lat" : 40.90001,
      "lon" : 74.100006
    },
    "pressure_change" : 1.900000000000091,
    "tree_pollen" : 0.0,
    "weed_pollen" : 0.0,
    "grass_pollen" : 0.0,
    "uv" : 8.0,
    "aqi_gb" : 43.0,
    "precipitation" : 0.0,
    "temperature" : 6.2,
    "wind" : 6.6,
    "humidity" : 82.0,
    "visibility" : 24140.0
  }, {
    "timestamp" : "2025-05-23T21:00:00.000+21:00",
    "coordinates" : {
      "lat" : 40.90001,
      "lon" : 74.100006
    },
    "pressure_change" : 1.900000000000091,
    "tree_pollen" : 0.0,
    "weed_pollen" : 0.0,
    "grass_pollen" : 0.0,
    "uv" : 8.0,
    "aqi_gb" : 39.0,
    "precipitation" : 0.0,
    "temperature" : 6.4,
    "wind" : 6.5,
    "humidity" : 78.0,
    "visibility" : 24140.0
  }, {
    "timestamp" : "2025-05-23T22:00:00.000+21:00",
    "coordinates" : {
      "lat" : 40.90001,
      "lon" : 74.100006
    },
    "pressure_change" : 1.900000000000091,
    "tree_pollen" : 0.0,
    "weed_pollen" : 0.0,
    "grass_pollen" : 0.0,
    "uv" : 8.0,
    "aqi_gb" : 37.0,
    "precipitation" : 0.1,
    "temperature" : 7.0,
    "wind" : 6.8,
    "humidity" : 78.0,
    "visibility" : 24140.0
  }, {
    "timestamp" : "2025-05-23T23:00:00.000+21:00",
    "coordinates" : {
      "lat" : 40.90001,
      "lon" : 74.100006
    },
    "pressure_change" : 1.900000000000091,
    "tree_pollen" : 0.0,
    "weed_pollen" : 0.0,
    "grass_pollen" : 0.0,
    "uv" : 8.0,
    "aqi_gb" : 37.0,
    "precipitation" : 0.3,
    "temperature" : 7.8,
    "wind" : 5.4,
    "humidity" : 80.0,
    "visibility" : 24140.0
  } ]
}