package database

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/opensearch-project/opensearch-go"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	"llif.org/wxcache/pkg/osquery"
)

type OpenSearch struct {
	client *opensearch.Client
}

// NewOpenSearch creates a new OpenSearch instance.
// It establishes a connection to the OpenSearch cluster. On failure, the initialiser will try to retry the connection.
func NewOpenSearch(hosts []string) (*OpenSearch, error) {
	client, err := opensearch.NewClient(opensearch.Config{
		Addresses: hosts,
	})
	if err != nil {
		return nil, err
	}

	// Assert that the cluster is available
	_, err = client.Ping()
	if err != nil {
		for i := 0; i < 5; i++ {
			slog.Info("retrying connection to OpenSearch", "count", i, "addr", hosts)
			r, err := client.Ping()
			if err == nil && r.StatusCode == 200 {
				break
			}
			time.Sleep(time.Second)
		}
		return nil, err
	}
	slog.Info("established connection to OpenSearch")

	os := &OpenSearch{
		client: client,
	}
	return os, nil
}

// Client returns the underlying OpenSearch client as a low-level API access to the database
func (os *OpenSearch) Client() *opensearch.Client {
	return os.client
}

// Add inserts a single document, represented as an interface{}, to the specified OpenSearch index.
func (os *OpenSearch) Add(ctx context.Context, index Index, data any) error {
	b, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("could not marshal data of type=%s, got err=%s", index.String(), err)
	}
	return os.add(ctx, string(index), b)
}

// AddAll allows to request a bulk insert into the OpenSearch database.
func (os *OpenSearch) AddAll(ctx context.Context, index Index, data []any) error {
	var b []byte = os.buildBulkBody("index", string(index), data)
	return os.addAll(ctx, string(index), b)
}

// Search runs a search request with the given query against the specified index.
func (os *OpenSearch) Search(ctx context.Context, index Index, query *osquery.BaseQuery) (*OpenSearchSearchBody, error) {
	var b bytes.Buffer
	if err := json.NewEncoder(&b).Encode(query.Map()); err != nil {
		return nil, err
	}
	slog.InfoContext(ctx, "fetching data from the database", "index", string(index), "query", query.Map())

	res, err := os.client.Search(
		os.client.Search.WithContext(ctx),
		os.client.Search.WithExpandWildcards("open"),
		os.client.Search.WithIndex(string(index)+"*"),
		os.client.Search.WithBody(&b),
		os.client.Search.WithSize(10_000),
	)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	var body *OpenSearchSearchBody
	err = json.NewDecoder(res.Body).Decode(&body)
	return body, err
}

// DeleteByQuery runs the provided query against the specified index with a wildcard(*) suffix .
func (os *OpenSearch) DeleteByQuery(ctx context.Context, index Index, query *osquery.BaseQuery) (*OpenSearchDeleteByQueryBody, error) {
	var b bytes.Buffer
	if err := json.NewEncoder(&b).Encode(query.Map()); err != nil {
		return nil, err
	}
	slog.InfoContext(ctx, "deleting data from the database", "index", string(index), "query", query.Map())

	r, err := os.client.DeleteByQuery(
		[]string{index.String() + "*"},
		&b,
		os.client.DeleteByQuery.WithContext(ctx),
		os.client.DeleteByQuery.WithExpandWildcards("open"),
	)
	if err != nil {
		return nil, err
	}
	defer r.Body.Close()

	var body *OpenSearchDeleteByQueryBody
	err = json.NewDecoder(r.Body).Decode(&body)
	return body, err
}

// addAll performs a single HTTP request to the OpenSearch's index with the bulk API.
// It is up to the caller to sanitize the size of the data to fit within the OpenSearch's 10MB request body limit.
func (os *OpenSearch) addAll(ctx context.Context, index string, b []byte) error {
	var (
		reader  = bytes.NewReader(b)
		request = opensearchapi.BulkRequest{
			Index:   index,
			Body:    reader,
			Refresh: "true",
		}
	)

	response, err := request.Do(ctx, os.client)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	if response.StatusCode/100 != 2 {
		body, err := io.ReadAll(response.Body)
		if err != nil {
			return err
		}

		err = fmt.Errorf(
			"could not bulk data into OpenSearch statuscode=%d index=%s responsebody=%s",
			response.StatusCode,
			index,
			string(body),
		)

		switch response.StatusCode {
		case http.StatusConflict:
			return &DocumentConflictError{
				StatusCode: http.StatusConflict,
				Err:        err,
			}
		default:
			return &DatabaseResponseError{
				StatusCode: response.StatusCode,
				Err:        err,
			}
		}
	}
	return nil
}

// buildBulkBody transforms the given slice of objects into a format accepted by the OpenSearch's bulk API.
func (os *OpenSearch) buildBulkBody(bulkType string, index string, data []any) []byte {
	var stringbuilder strings.Builder
	for _, obj := range data {
		var metadata = map[string]interface{}{
			bulkType: map[string]interface{}{
				"_index": index,
				"_id":    uuid.New().String(),
			},
		}

		strmetadata, err := json.Marshal(metadata)
		if err != nil {
			slog.Error("OpenSearch wrapper failed to marshal metadata for bulk commit", "obj", obj, "err", err)
			continue
		}
		strobj, err := json.Marshal(obj)
		if err != nil {
			slog.Error("OpenSearch wrapper failed to marshal body for bulk commit", "obj", obj, "err", err)
			continue
		}

		stringbuilder.WriteString(fmt.Sprintf("%s\n", strmetadata))
		stringbuilder.WriteString(fmt.Sprintf("%s\n", strobj))
	}
	return []byte(stringbuilder.String())
}

// add performs a single HTTP request into the given OpenSearch index. If the document already exists in the database,
// the function will return a `DocumentConflictError`.
func (os *OpenSearch) add(ctx context.Context, index string, b []byte) error {
	reader := bytes.NewReader(b)

	req := opensearchapi.IndexRequest{
		Index:   index,
		Body:    reader,
		Refresh: "true",
		Timeout: 30 * time.Second,
	}

	resp, err := req.Do(ctx, os.client)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode/100 != 2 {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return err
		}

		err = fmt.Errorf(
			"recevied status code=%d for index=%s with request body=%s and response body=%s",
			resp.StatusCode,
			index,
			string(b),
			string(body),
		)

		// Conflict error
		if resp.StatusCode == 409 {
			return &DocumentConflictError{
				StatusCode: resp.StatusCode,
				Err:        err,
			}
		}

		// Generic error
		return &DatabaseResponseError{
			StatusCode: resp.StatusCode,
			Err:        err,
		}
	}
	return nil
}
