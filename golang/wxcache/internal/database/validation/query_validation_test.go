package validation

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"log/slog"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/database"
	"llif.org/wxcache/pkg/osquery"
	"llif.org/wxcache/pkg/testutil/testsetup"
)

func TestOpenSearchQueryIsValidIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}
	// Setup
	config, err := testsetup.NewConfig()
	require.NoError(t, err)
	client, err := database.NewOpenSearch(config.Config.OpenSearchHost)
	require.NoError(t, err)
	osclient := client.Client()

	var queries = []*osquery.BaseQuery{
		osquery.Query(osquery.Bool().Filter(osquery.Range("timestamp").Gt(0).Lt(10))),
	}

	for _, query := range queries {
		slog.Info("validating query", "query", query.Map())

		var b bytes.Buffer
		err = json.NewEncoder(&b).Encode(query.Map())
		require.NoError(t, err)

		// Act
		// WARNING: The validation only works if the query's fields are present in the cluster itself.
		// If we query a field that does not exist for a given index (* means any index), then the validation fails.
		result, err := osclient.Indices.ValidateQuery(
			osclient.Indices.ValidateQuery.WithContext(context.Background()),
			osclient.Indices.ValidateQuery.WithBody(&b),
			osclient.Indices.ValidateQuery.WithIndex("*"),
		)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, result.StatusCode)

		// Assert
		bodyBytes, err := io.ReadAll(result.Body)
		require.NoError(t, err)

		var body map[string]any
		err = json.Unmarshal(bodyBytes, &body)
		require.NoError(t, err)

		require.NotEmpty(t, body)
		require.Equal(t, true, body["valid"])
	}
}
