package httpclient

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"time"

	"golang.org/x/time/rate"
)

type RateLimitExceeded struct {
	Err error
}

func (e *RateLimitExceeded) Error() string {
	return fmt.Sprintf("%s", e.Err)
}

type HttpClient struct {
	Conn    *http.Client
	limiter *rate.Limiter
}

// NewHttpClient accepts a rate.Limitar as an argument to then use this boundary to apply the rate limit
// to each request made via this HttpClient.
func NewHttpClient(limit *rate.Limiter) *HttpClient {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Transport: tr,
		Timeout:   60 * time.Second,
	}

	return &HttpClient{
		Conn:    client,
		limiter: limit,
	}
}

// Do manages the rate limit set through the initialisation of NewHttpClient() and returns RateLimitExceeded error
// if the limit has been exceeded. Or any other error, which can be returned by http.Client.
//
// Returns http.Response on a successful request.
// It is up to the caller to assert the returned http.Response's body is properly closed.
func (c *HttpClient) Do(r *http.Request) (*http.Response, error) {
	if !c.limiter.Allow() {
		return nil, &RateLimitExceeded{
			Err: fmt.Errorf("rate limit of %f requests per second exceeded", c.limiter.Limit()),
		}
	}

	res, err := c.Conn.Do(r)
	if err != nil {
		return nil, err
	}

	// Also return RateLimitExceeded if we get HTTP 429 back
	if res.StatusCode == http.StatusTooManyRequests {
		return nil, &RateLimitExceeded{
			Err: fmt.Errorf("request returned status=%d", res.StatusCode),
		}
	}
	return res, nil
}
