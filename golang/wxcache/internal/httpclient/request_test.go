package httpclient

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
)

type TestOpts struct {
	A string   `url:"a"`
	B *int     `url:"b"`
	C []string `url:"c"`
}

func TestRequestSerialiseParamsShouldPass(t *testing.T) {
	// Needs to be accessed as a pointer in the input test, therefore we need to assign here so we can do &b
	b := 1

	tests := []struct {
		input          *TestOpts
		expectedOutput string
	}{
		{
			input:          &TestOpts{A: "hello", B: &b, C: []string{"world", "test"}},
			expectedOutput: "a=hello&b=1&c=world&c=test",
		},
		{
			input:          &TestOpts{},
			expectedOutput: "",
		},
		{
			input:          nil,
			expectedOutput: "",
		},
	}

	for _, tt := range tests {
		output, err := serialiseOptsToParams(tt.input)
		require.NoError(t, err)
		require.Equal(t, tt.expectedOutput, output)
	}
}

func TestBuildRequestWithOptsShouldPass(t *testing.T) {
	var (
		host        = "llif.org"
		endpoint    = "/v1/api"
		opts        = TestOpts{A: "hello"}
		expectedUrl = "https://llif.org/v1/api?a=hello"
	)

	req, err := BuildRequest(host, endpoint, opts)
	require.NoError(t, err)

	if req.Host != host {
		t.Fatalf("expected host=%s does not match real host=%s", host, req.Host)
	}

	if req.URL.Path != endpoint {
		t.Fatalf("expected path=%s does not match real path=%s", endpoint, req.URL.Path)
	}

	// Build up the whole URL from req.URL in the format [scheme]://[host][endpoint]?[path params]
	url := fmt.Sprintf("%s://%s%s?%s", req.URL.Scheme, req.URL.Host, req.URL.Path, req.URL.RawQuery)

	if url != expectedUrl {
		t.Fatalf("expected path=%s does not match real path=%s", expectedUrl, url)
	}
}
