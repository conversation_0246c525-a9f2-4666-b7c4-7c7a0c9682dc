package database

import (
	"log/slog"
	"regexp"

	"llif.org/wxcache/internal/database"
	"llif.org/wxcache/pkg/repository"
)

// ConfigOpts holds the environmental configuration required to initialise the databse config and resources
type ConfigOpts struct {
	// A slice of available OS hosts to connect to. The service only takes into consideration the first entry of the list.
	// The other hosts will not be used.
	OpenSearchHost []string `env:"OS_HOSTS" envDefault:"http://localhost:9200"`

	// Whether or not the service is running in a containerized environment
	IsContainerized bool `env:"IS_CONTAINERIZED" envDefault:"false"`

	// The host to connect to when running locally
	OpenSearchHostLocal string `env:"OS_HOST_LOCAL"`
}

// Config contains all necessary tools to interact with the databases and repositories.
// Never set these values on your own, but rather call NewDatabaseConfigFromConfigOpts()
type Config struct {
	// Repository represents all initialised repositories available to be used
	Repository *repository.Repository

	// OpenSearch host URL to connect to
	OpenSearchHost []string
}

// NewDatabaseConfigFromConfigOpts creates a new database config with initialised connections to the the databases
func NewDatabaseConfigFromConfigOpts(l *slog.Logger, c ConfigOpts) (*Config, error) {
	// The value is represented as a string with brackets and quotes, we need the raw value of the address
	osHost := regexp.MustCompile(`\[|\]|"`).ReplaceAllString(c.OpenSearchHost[0], ``)

	if !c.IsContainerized {
		osHost = c.OpenSearchHostLocal
	}

	l.Info("setting up database connections", "opts", c)
	os, err := database.NewOpenSearch([]string{osHost})
	if err != nil {
		return nil, err
	}

	return &Config{
		OpenSearchHost: []string{osHost},
		Repository: repository.NewRepository(repository.RepositoryOpts{
			OpenSearch: os,
			Logger:     l,
		}),
	}, nil
}
