package handler_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/api/handler"
	"llif.org/wxcache/internal/api/request"
	"llif.org/wxcache/internal/service"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

var (
	forecastApiPrefix = "/api/v2/forecast"
	forecastAq        = forecastApiPrefix + "/airquality"
	forecastW         = forecastApiPrefix + "/weather"
	forecastP         = forecastApiPrefix + "/pollen"
)

func TestPostEnvironmentForecastDataIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	var (
		environmentInput = request.PostEnvironment{
			SpaceTime: []wxtypes.SpaceTime{{
				Lat:      location.Seoul.Lat,
				Long:     location.Seoul.Long,
				TimeFrom: time.Now().UTC().Add(-24 * time.Hour),
				TimeTo:   time.Now().UTC(),
			}},
		}

		e = echo.New()
	)

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc     = service.NewEnvironmentService(c)
		handler = handler.NewForecastHandler(c.Logger, svc)
	)

	assertSuccess := func(t *testing.T, route string, f echo.HandlerFunc) {
		var rb bytes.Buffer
		err := json.NewEncoder(&rb).Encode(environmentInput)
		require.NoError(t, err)

		request := httptest.NewRequest(http.MethodPost, route, &rb)
		request.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		recorder := httptest.NewRecorder()
		ctx := e.NewContext(request, recorder)

		err = f(ctx)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, recorder.Code)
		require.NotEmpty(t, recorder.Body.String())
	}

	t.Run("Air Quality", func(t *testing.T) {
		assertSuccess(t, forecastAq, handler.PostAirQuality)
	})

	t.Run("Weather", func(t *testing.T) {
		assertSuccess(t, forecastW, handler.PostWeather)
	})
	t.Run("Pollen ", func(t *testing.T) {
		assertSuccess(t, forecastP, handler.PostPollen)
	})
}

func TestPostEnvironmentForecastInvalidRequest(t *testing.T) {
	var (
		environmentInput = wxtypes.SpaceTime{
			TimeFrom: time.Now().UTC(),
			TimeTo:   time.Now().UTC().Add(24 * time.Hour),
			Lat:      location.London.Lat,
			Long:     location.London.Long,
		}

		e = echo.New()
	)

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		svc     = service.NewEnvironmentService(c)
		handler = handler.NewForecastHandler(c.Logger, svc)
	)

	assertBadRequest := func(t *testing.T, route string, f echo.HandlerFunc) {
		var rb bytes.Buffer
		err := json.NewEncoder(&rb).Encode(environmentInput)
		require.NoError(t, err)

		request := httptest.NewRequest(http.MethodPost, route, &rb)
		request.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

		recorder := httptest.NewRecorder()
		ctx := e.NewContext(request, recorder)

		err = f(ctx)
		// The handler SHOULD NOT return an error itself unless it's 500 internal server error.
		// Therefore even for bad input it returns nil, but the response is set to the appropriate code
		require.NoError(t, err)
		require.Equal(t, http.StatusBadRequest, recorder.Code)
	}

	t.Run("Air Quality", func(t *testing.T) {
		assertBadRequest(t, forecastAq, handler.PostAirQuality)
	})

	t.Run("Weather", func(t *testing.T) {
		assertBadRequest(t, forecastW, handler.PostWeather)
	})
	t.Run("Pollen", func(t *testing.T) {
		assertBadRequest(t, forecastP, handler.PostPollen)
	})
}
