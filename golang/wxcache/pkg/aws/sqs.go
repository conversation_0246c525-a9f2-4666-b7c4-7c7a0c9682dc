package llifaws

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
)

var (
	AirQualityQueue string = "air_quality_queue"
	WeatherQueue    string = "weather_queue"
	PollenQueue     string = "pollen_queue"
)

type SQSMessageBodyAttribute struct {
	Type  string `json:"Type"`
	Value string `json:"Value"`
}

// MessageBody contains fields provided in the SQS received message
type SQSMessageBody struct {
	Type              string                             `json:"Type"`
	MessageId         string                             `json:"MessageId"`
	TopicArn          string                             `json:"TopicArn"`
	Message           string                             `json:"Message"`
	Timestamp         time.Time                          `json:"Timestamp"`
	SignatureVersion  string                             `json:"SignatureVersion"`
	Signature         string                             `json:"Signature"`
	SigningCertURL    string                             `json:"SigningCertURL"`
	UnsubscribeURL    string                             `json:"UnsubscribeURL"`
	MessageAttributes map[string]SQSMessageBodyAttribute `json:"MessageAttributes"`

	// A handle which can be used to delete the message from the queue
	ReceiptHandle *string `json:"ReceiptHandle"`
}

type SQSOpts struct {
	// Specifies the AWS region in which the service is located
	AWSRegion string
	// Specifies the URL to which the session should connect to.
	// Supports both AWS and LocalStack
	AWSEndpoint string
	// Runtime environment of the application
	// Used only to determine if the connection is against the cloud or local instance
	RunEnv string
}

type SQS struct {
	endpoint string      // Cloud endpoint to which the service is instantiated against
	region   string      // Cloud region which specifies the location of the SQS service, for example us-east-1
	sqs      *sqs.Client // Instance of the currently used SQS instance
}

// Returns a new instance of the SQS client with an initialised AWS session and a custom HTTP client
func NewSQS(ctx context.Context, opts SQSOpts) *SQS {
	httpClient := &http.Client{
		Timeout: 25 * time.Second,
	}
	loadOpts := []func(*config.LoadOptions) error{
		config.WithHTTPClient(httpClient),
		config.WithRegion(opts.AWSRegion),
		config.WithBaseEndpoint(opts.AWSEndpoint),
	}
	// Connect to LocalStack if the environment is local
	if opts.RunEnv == "local" {
		loadOpts = append(loadOpts,
			config.WithCredentialsProvider(
				credentials.NewStaticCredentialsProvider("x", "x", "x"),
			))
	}

	cfg, err := config.LoadDefaultConfig(ctx, loadOpts...)
	if err != nil {
		panic(err)
	}

	return &SQS{
		endpoint: opts.AWSEndpoint,
		region:   opts.AWSRegion,
		sqs:      sqs.NewFromConfig(cfg),
	}
}

func (s *SQS) Create(ctx context.Context, queueName string) (*sqs.CreateQueueOutput, error) {
	input := &sqs.CreateQueueInput{
		QueueName: &queueName,
	}
	return s.sqs.CreateQueue(ctx, input)
}

func (s *SQS) GetQueueArn(ctx context.Context, queueUrl string) (*string, error) {
	input := &sqs.GetQueueAttributesInput{
		AttributeNames: []types.QueueAttributeName{types.QueueAttributeNameQueueArn},
		QueueUrl:       &queueUrl,
	}
	output, err := s.sqs.GetQueueAttributes(ctx, input)
	if err != nil {
		return nil, err
	}

	v, ok := output.Attributes["QueueArn"]
	if !ok {
		return nil, fmt.Errorf("response %v does not include QueueArn", output.Attributes)
	}
	return &v, nil
}

func (s *SQS) Receive(ctx context.Context, queueName string, maxMessageCount, waitTimeSeconds int) (*sqs.ReceiveMessageOutput, error) {
	outputUrl, err := s.getQueueUrl(ctx, queueName)
	if err != nil {
		return nil, err
	}

	message := &sqs.ReceiveMessageInput{
		MaxNumberOfMessages: int32(maxMessageCount),
		WaitTimeSeconds:     int32(waitTimeSeconds),
		QueueUrl:            outputUrl.QueueUrl,
	}
	return s.sqs.ReceiveMessage(ctx, message)
}

func (s *SQS) Delete(ctx context.Context, queueName string, receiptHandle string) (*sqs.DeleteMessageOutput, error) {
	outputUrl, err := s.getQueueUrl(ctx, queueName)
	if err != nil {
		return nil, err
	}

	input := &sqs.DeleteMessageInput{
		QueueUrl:      outputUrl.QueueUrl,
		ReceiptHandle: &receiptHandle,
	}
	return s.sqs.DeleteMessage(ctx, input)
}

func (s *SQS) Send(ctx context.Context, queueName string, message string) (*sqs.SendMessageOutput, error) {
	outputUrl, err := s.getQueueUrl(ctx, queueName)
	if err != nil {
		return nil, err
	}

	input := &sqs.SendMessageInput{
		DelaySeconds: int32(0),
		MessageBody:  &message,
		QueueUrl:     outputUrl.QueueUrl,
	}
	return s.sqs.SendMessage(ctx, input)
}

func (s *SQS) DeleteQueue(ctx context.Context, queueName string) error {
	queueUrl, err := s.getQueueUrl(ctx, queueName)
	if err != nil {
		return err
	}

	_, err = s.sqs.DeleteQueue(ctx, &sqs.DeleteQueueInput{
		QueueUrl: queueUrl.QueueUrl,
	})
	return err
}

func (s *SQS) getQueueUrl(ctx context.Context, queueName string) (*sqs.GetQueueUrlOutput, error) {
	input := &sqs.GetQueueUrlInput{
		QueueName: &queueName,
	}

	return s.sqs.GetQueueUrl(ctx, input)
}
