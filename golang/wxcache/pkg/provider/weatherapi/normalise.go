package weatherapi

import (
	"context"
	"log/slog"
	"time"

	"llif.org/wxcache/internal/ckey"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/aqi/v2"
	"llif.org/wxcache/pkg/wxtypes"
)

func normaliseAirQuality(ctx context.Context, data []weatherAPIAQIBucket) ([]wxtypes.AirQualityV2, error) {
	result := []wxtypes.AirQualityV2{}
	isBackfill := ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc

	for _, d := range data {
		loc, err := time.LoadLocation(d.Location.TZID)
		if err != nil {
			slog.ErrorContext(ctx, "could not get location from timezone id", "tz_id", d.Location.TZID, "err", err)
			continue
		}
		timestamp, err := time.ParseInLocation("2006-01-02 15:04", d.Timestamp, loc)
		if err != nil {
			slog.WarnContext(ctx, "error parsing timestamp field", "value", d.Timestamp, "err", err)
			continue
		}
		timestamp = timestamp.Truncate(time.Second).UTC()

		aq, err := aqi.CalculateAQI(aqi.AQIConcentration{
			O3:   &d.AirQuality.O3,
			PM25: &d.AirQuality.PM25,
			PM10: &d.AirQuality.PM10,
			CO:   &d.AirQuality.CO,
			SO2:  &d.AirQuality.SO2,
			NO2:  &d.AirQuality.NO2,
		})
		if err != nil {
			slog.ErrorContext(ctx, "could not calculate AQI", "err", err)
			continue
		}

		// Convert 0 values to null if present in the data
		var (
			ozone = convertor.ZeroToNil(d.AirQuality.O3)
			pm10  = convertor.ZeroToNil(d.AirQuality.PM10)
			pm25  = convertor.ZeroToNil(d.AirQuality.PM25)
			co    = convertor.ZeroToNil(d.AirQuality.CO)
			so2   = convertor.ZeroToNil(d.AirQuality.SO2)
			no2   = convertor.ZeroToNil(d.AirQuality.NO2)
		)

		na := wxtypes.AirQualityV2{
			Timestamp: timestamp,
			Pollutants: wxtypes.AirQualityPollutants{
				PM10: pm10,
				PM25: pm25,
				O3:   ozone,
				CO:   co,
				SO2:  so2,
				NO2:  no2,
			},
			AQI: wxtypes.AirQualityIndex{
				US: aq.USIndex,
				GB: aq.GBIndex,
				EU: aq.EUIndex,
			},
			Coordinates: wxtypes.Coordinates{
				Latitude:  d.Location.Latitude,
				Longitude: d.Location.Longitude,
			},
			Metadata: wxtypes.Metadata{
				Provider: "weatherapi",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Now().UTC(),
				Backfill:  isBackfill,
			},
		}
		if err := wxtypes.ValidateStruct(na); err != nil {
			slog.WarnContext(ctx, "could not validate AQ", "provider", "weatherapi", "err", err, "struct", na)
			continue
		}

		result = append(result, na)
	}
	return result, nil
}

func normaliseWeather(ctx context.Context, data []WeatherAPIEnvironmentResponse) ([]wxtypes.WeatherV2, error) {
	result := make([]wxtypes.WeatherV2, 0)
	isBackfill := ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc

	for _, response := range data {
		loc, err := time.LoadLocation(response.Location.TZID)
		if err != nil {
			slog.ErrorContext(ctx, "could not get location from timezone id", "tz_id", response.Location.TZID, "err", err)
			continue
		}

		for _, forecastday := range response.Forecast.Forecastday {
			for i := range forecastday.Hour {
				h := forecastday.Hour[i]

				timestamp, err := time.ParseInLocation("2006-01-02 15:04", h.Time, loc)
				if err != nil {
					slog.WarnContext(ctx, "error parsing timestamp field", "value", h.Time, "err", err)
					continue
				}
				timestamp = timestamp.Truncate(time.Second).UTC()

				var (
					humidity = float64(h.Humidity)
					uv       = int(h.UV)
				)

				nw := wxtypes.WeatherV2{
					Timestamp: timestamp,
					Temperature: wxtypes.WeatherTemperature{
						Temperature: &h.TempC,
						FeelsLike:   &h.FeelslikeC,
					},
					Wind: wxtypes.WeatherWind{
						Speed: &h.WindKph,
						Gust:  &h.GustKph,
					},
					Humidity:      &humidity,
					CloudCover:    &h.Cloud, // @TODO: figure out units from WAPI
					UV:            &uv,
					Pressure:      &h.PressureMb,
					Visiblity:     &h.VisibilityKm,
					Precipitation: &h.PrecipitationMm,
					Coordinates: wxtypes.Coordinates{
						Latitude:  response.Location.Latitude,
						Longitude: response.Location.Longitude,
					},
					Metadata: wxtypes.Metadata{
						Provider: "weatherapi",
					},
					SystemProperties: wxtypes.SystemProperties{
						CreatedAt: time.Now().UTC(),
						Backfill:  isBackfill,
					},
				}
				if err := wxtypes.ValidateStruct(nw); err != nil {
					slog.WarnContext(ctx, "could not validate struct", "struct", nw, "provider", "weatherapi", "err", err)
					continue
				}
				result = append(result, nw)
			}
		}
	}
	return result, nil
}
