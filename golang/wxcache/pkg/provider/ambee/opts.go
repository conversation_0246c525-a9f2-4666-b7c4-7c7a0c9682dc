package ambee

type AmbeePollenHistoryRequestOpts struct {
	Lat         float64 `url:"lat" validate:"required,latitude"`  // Latitude of the place to search
	Long        float64 `url:"lng" validate:"required,longitude"` // Longitude of the place to search
	From        string  `url:"from" validate:"required"`          // Starting timestamp of the period to search for in the format YYYY-MM-DD hh:mm:ss
	To          string  `url:"to" validate:"required"`            // Ending timestamp of the period to search for in the format YYYY-MM-DD hh:mm:ss
	SpeciesRisk *bool   `url:"speciesRisk"`                       // Possible values 'true' or 'false'. Defaults to 'false'. Returns sub species level risk evaluation for regions that currently support sub species data
}

type AmbeePollenForecastRequestOpts struct {
	Lat         float64 `url:"lat" validate:"required,latitude"`  // Latitude of the place to search
	Long        float64 `url:"lng" validate:"required,longitude"` // Longitude of the place to search
	SpeciesRisk *bool   `url:"speciesRisk"`                       // Possible values 'true' or 'false'. Defaults to 'false'. Returns sub species level risk evaluation for regions that currently support sub species data
}

type AmbeeAirQualityHistoryRequestOpts struct {
	Lat  float64 `url:"lat" validate:"required,latitude"`  // Latitude of the place to search
	Long float64 `url:"lng" validate:"required,longitude"` // Longitude of the place to search
	From string  `url:"from" validate:"required"`          // Starting timestamp of the period to search for in the format YYYY-MM-DD hh:mm:ss
	To   string  `url:"to" validate:"required"`            // Ending timestamp of the period to search for in the format YYYY-MM-DD hh:mm:ss
}

type AmbeeWeatherHistoryRequestOpts struct {
	Lat   float64 `url:"lat" validate:"required,latitude"`  // Latitude of the place to search
	Long  float64 `url:"lng" validate:"required,longitude"` // Longitude of the place to search
	From  string  `url:"from" validate:"required"`          // Starting timestamp of the period to search for in the format YYYY-MM-DD hh:mm:ss
	To    string  `url:"to" validate:"required"`            // Ending timestamp of the period to search for in the format YYYY-MM-DD hh:mm:ss
	Units string  `url:"units"`                             // Returns the weather parameters in SI units if 'si' is specified with this value
}
