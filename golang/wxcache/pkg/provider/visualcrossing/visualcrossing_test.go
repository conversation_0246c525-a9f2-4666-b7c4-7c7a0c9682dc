package visualcrossing

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/testutil"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestVisualCrossingReturnsAllTypesShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		vc        *VisualCrossing     = Init(c)
		ctx                           = context.Background()
		locations []location.Location = location.GetAll()
	)

	t.Run("Weather", func(t *testing.T) {
		var spacetime = make([]wxtypes.SpaceTime, 0)

		for _, loc := range locations {
			spacetime = append(spacetime, wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: testutil.GetISOTimeFromString("2024-03-07T10:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-03-08T12:00:00Z"),
			})
		}

		res, errBuckets := vc.GetWeather(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, res)
	})
}

func getVisualCrossingWeatherResponse() VisualCrossingWeatherResponse {
	now := time.Date(2023, time.August, 17, 0, 0, 0, 0, time.UTC)
	dateTimeEpoch := float64(now.Unix())

	return VisualCrossingWeatherResponse{
		Latitude:  location.London.Lat,
		Longitude: location.London.Long,
		TimeZone:  "Europe/London",
		Days: []VisualCrossingWeatherDay{
			{
				VisualCrossingWeatherBucket: VisualCrossingWeatherBucket{
					DateTime:      now.Format("2006-01-02"),
					DateTimeEpoch: dateTimeEpoch,
					Temperature:   convertor.FloatToFloatPtr(15.5),
					FeelsLike:     convertor.FloatToFloatPtr(14.0),
					Humidity:      convertor.FloatToFloatPtr(65.0),
					WindSpeed:     convertor.FloatToFloatPtr(8.5),
					WindGust:      convertor.FloatToFloatPtr(12.3),
					WindDirection: convertor.FloatToFloatPtr(180.0),
					Pressure:      convertor.FloatToFloatPtr(1013.2),
					Visibility:    convertor.FloatToFloatPtr(9.8),
					CloudCover:    convertor.FloatToFloatPtr(35.0),
					UVIndex:       convertor.FloatToFloatPtr(3.0),
					Precip:        convertor.FloatToFloatPtr(0.5),
					Conditions:    convertor.StrToStrPtr("Partly Cloudy"),
					Icon:          convertor.StrToStrPtr("partly-cloudy-day"),
				},
				Hours: []VisualCrossingWeatherBucket{
					{
						DateTime:      now.Format("15:04:05"),
						DateTimeEpoch: dateTimeEpoch,
						Temperature:   convertor.FloatToFloatPtr(16.0),
						FeelsLike:     convertor.FloatToFloatPtr(15.0),
						Humidity:      convertor.FloatToFloatPtr(60.0),
						WindSpeed:     convertor.FloatToFloatPtr(7.0),
						WindGust:      convertor.FloatToFloatPtr(10.0),
						WindDirection: convertor.FloatToFloatPtr(175.0),
						Pressure:      convertor.FloatToFloatPtr(1012.0),
						Visibility:    convertor.FloatToFloatPtr(10.0),
						CloudCover:    convertor.FloatToFloatPtr(30.0),
						UVIndex:       convertor.FloatToFloatPtr(4.0),
						Precip:        convertor.FloatToFloatPtr(0.0),
						Conditions:    convertor.StrToStrPtr("Clear"),
						Icon:          convertor.StrToStrPtr("clear-day"),
					},
					{
						DateTime:      now.Add(time.Hour).Format("15:04:05"),
						DateTimeEpoch: dateTimeEpoch + 3600,
						Temperature:   convertor.FloatToFloatPtr(17.0),
						FeelsLike:     convertor.FloatToFloatPtr(16.0),
						Humidity:      convertor.FloatToFloatPtr(58.0),
						WindSpeed:     convertor.FloatToFloatPtr(8.0),
						WindGust:      convertor.FloatToFloatPtr(11.0),
						WindDirection: convertor.FloatToFloatPtr(185.0),
						Pressure:      convertor.FloatToFloatPtr(1011.0),
						Visibility:    convertor.FloatToFloatPtr(9.5),
						CloudCover:    convertor.FloatToFloatPtr(40.0),
						UVIndex:       convertor.FloatToFloatPtr(3.0),
						Precip:        convertor.FloatToFloatPtr(0.2),
						Conditions:    convertor.StrToStrPtr("Partly Cloudy"),
						Icon:          convertor.StrToStrPtr("partly-cloudy-day"),
					},
				},
			},
		},
	}
}

func getVisualCrossingWeatherNormalisedResponse() []wxtypes.WeatherV2 {
	now := time.Date(2023, time.August, 17, 0, 0, 0, 0, time.UTC)

	// First hour data
	temperature1 := 16.0
	feelsLike1 := 15.0
	humidity1 := 60.0
	windSpeed1 := 7.0
	windGust1 := 10.0
	windDirection1 := "S"
	windDegree1 := 175.0
	uvIndex1 := 4
	pressure1 := 1012.0
	visibility1 := 10.0
	precipitation1 := 0.0
	cloudCover1 := 30

	// Second hour data
	temperature2 := 17.0
	feelsLike2 := 16.0
	humidity2 := 58.0
	windSpeed2 := 8.0
	windGust2 := 11.0
	windDirection2 := "S"
	windDegree2 := 185.0
	uvIndex2 := 3
	pressure2 := 1011.0
	visibility2 := 9.5
	precipitation2 := 0.2
	cloudCover2 := 40

	return []wxtypes.WeatherV2{
		{
			Timestamp: now,
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &temperature1,
				FeelsLike:   &feelsLike1,
			},
			Wind: wxtypes.WeatherWind{
				Speed:     &windSpeed1,
				Gust:      &windGust1,
				Degree:    &windDegree1,
				Direction: &windDirection1,
			},
			Humidity:      &humidity1,
			CloudCover:    &cloudCover1,
			UV:            &uvIndex1,
			Pressure:      &pressure1,
			Visiblity:     &visibility1,
			Precipitation: &precipitation1,
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.London.Lat,
				Longitude: location.London.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "visualcrossing",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 30, 48, 858963000, time.UTC),
				Backfill:  false,
			},
		},
		{
			Timestamp: now.Add(time.Hour),
			Temperature: wxtypes.WeatherTemperature{
				Temperature: &temperature2,
				FeelsLike:   &feelsLike2,
			},
			Wind: wxtypes.WeatherWind{
				Speed:     &windSpeed2,
				Gust:      &windGust2,
				Degree:    &windDegree2,
				Direction: &windDirection2,
			},
			Humidity:      &humidity2,
			CloudCover:    &cloudCover2,
			UV:            &uvIndex2,
			Pressure:      &pressure2,
			Visiblity:     &visibility2,
			Precipitation: &precipitation2,
			Coordinates: wxtypes.Coordinates{
				Latitude:  location.London.Lat,
				Longitude: location.London.Long,
			},
			Metadata: wxtypes.Metadata{
				Provider: "visualcrossing",
			},
			SystemProperties: wxtypes.SystemProperties{
				CreatedAt: time.Date(2023, time.November, 30, 11, 30, 48, 858963000, time.UTC),
				Backfill:  false,
			},
		},
	}
}
