package openmeteo

type OpenMeteoHourlyAirQuality struct {
	Time                []string   `json:"time"`                  // Time array containing time for each bucket, e.g. if i = 10, Time[i] is the time of measurement for PM10[i]
	PM10                []*float64 `json:"pm10"`                  // Particulate matter with diameter smaller than 10 µm (PM10) close to surface (10 meter above ground) (μg/m³)
	PM25                []*float64 `json:"pm2_5"`                 // Particulate matter with diameter smaller than 2.5 µm (PM2.5) close to surface (10 meter above ground) (μg/m³)
	CarbonMonoxide      []*float64 `json:"carbon_monoxide"`       // Atmospheric gases close to surface (10 meter above ground) (μg/m³)
	NitrogenDioxide     []*float64 `json:"nitrogen_dioxide"`      // Atmospheric gases close to surface (10 meter above ground) (μg/m³)
	SulphurDioxide      []*float64 `json:"sulphur_dioxide"`       // Atmospheric gases close to surface (10 meter above ground) (μg/m³)
	Ozone               []*float64 `json:"ozone"`                 // Atmospheric gases close to surface (10 meter above ground) (μg/m³)
	Ammonia             []*float64 `json:"ammonia"`               // Ammonia concentration. Only available for Europe. (μg/m³)
	AerosolOpticalDepth []*float64 `json:"aerosol_optical_depth"` // Aerosol optical depth at 550 nm of the entire atmosphere to indicate haze.
	Dust                []*float64 `json:"dust"`                  // Saharan dust particles close to surface level (10 meter above ground). (μg/m³)
	UVIndex             []*float64 `json:"uv_index"`              // UV index considering clouds and clear sky. See ECMWF UV Index recommendation https://confluence.ecmwf.int/display/CUSF/CAMS+global+UV+index for more information
	UVIndexClearSky     []*float64 `json:"uv_index_clear_sky"`    // UV index considering clouds and clear sky. See ECMWF UV Index recommendation https://confluence.ecmwf.int/display/CUSF/CAMS+global+UV+index for more information
	AlderPollen         []*float64 `json:"alder_pollen"`          // Pollen. Only available in Europe as provided by CAMS European Air Quality forecast. (Grains/m³)
	BirchPollen         []*float64 `json:"birch_pollen"`          // Pollen. Only available in Europe as provided by CAMS European Air Quality forecast. (Grains/m³)
	GrassPollen         []*float64 `json:"grass_pollen"`          // Pollen. Only available in Europe as provided by CAMS European Air Quality forecast. (Grains/m³)
	MugwortPollen       []*float64 `json:"mugwort_pollen"`        // Pollen. Only available in Europe as provided by CAMS European Air Quality forecast. (Grains/m³)
	OlivePollen         []*float64 `json:"olive_pollen"`          // Pollen. Only available in Europe as provided by CAMS European Air Quality forecast. (Grains/m³)
	RagweedPollen       []*float64 `json:"ragweed_pollen"`        // Pollen. Only available in Europe as provided by CAMS European Air Quality forecast. (Grains/m³)
	EuropeanAQI         []*int     `json:"european_aqi"`          // The maximum of all individual AQI indices
	EuropeanAQIPM25     []*int     `json:"european_aqi_pm2_5"`    // European Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	EuropeanAQIPM10     []*int     `json:"european_aqi_pm10"`     // European Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	EuropeanAQINO2      []*int     `json:"european_aqi_no2"`      // European Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	EuropeanAQIO3       []*int     `json:"european_aqi_o3"`       // European Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	EuropeanAQISO2      []*int     `json:"european_aqi_so2"`      // European Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	USAQI               []*int     `json:"us_aqi"`                // The maximum of all individual AQI indices
	USAQIPM25           []*int     `json:"us_aqi_pm2_5"`          // United States Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	USAQIPM10           []*int     `json:"us_aqi_pm10"`           // United States Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	USAQINO2            []*int     `json:"us_aqi_no2"`            // United States Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	USAQIO3             []*int     `json:"us_aqi_o3"`             // United States Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	USAQISO2            []*int     `json:"us_aqi_so2"`            // United States Air Quality Index (AQI) calculated for different particulate matter and gases individually.
	USAQICO             []*int     `json:"us_aqi_co"`             // United States Air Quality Index (AQI) calculated for different particulate matter and gases individually.
}

type OpenMeteoAirQualityResponse struct {
	Latitude             float64                   `json:"latitude"`
	Longitude            float64                   `json:"longitude"`
	Elevation            float64                   `json:"elevation"`
	GenerationTimeMs     float64                   `json:"generation_time_ms"`
	UtcOffsetSeconds     int                       `json:"utc_offset_seconds"`
	Timezone             string                    `json:"timezone"`
	TimezoneAbbreviation string                    `json:"timezone_abbreviation"`
	Hourly               OpenMeteoHourlyAirQuality `json:"hourly"` // Most weather variables are given as an instantaneous value for the indicated hour. Some variables like precipitation are calculated from the preceding hour as an average or sum.
}

type OpenMeteoWeatherForecastResponse struct {
	Latitude             float64                        `json:"latitude"`
	Longitude            float64                        `json:"longitude"`
	Elevation            float64                        `json:"elevation"`
	GenerationTimeMs     float64                        `json:"generation_time_ms"`
	UtcOffsetSeconds     int                            `json:"utc_offset_seconds"`
	Timezone             string                         `json:"timezone"`
	TimezoneAbbreviation string                         `json:"timezone_abbreviation"`
	Hourly               OpenMeteoHourlyWeatherForecast `json:"hourly"`
	Daily                struct {
		Time               []string   `json:"time"`                   // Time array containing time for each bucket, e.g. if i = 10, Time[i] is the time of measurement for PM10[i]
		UVIndexMax         []*float64 `json:"uv_index_max"`           // Daily maximum in UV Index starting from 0
		UVIndexClearSkyMax []*float64 `json:"uv_index_clear_sky_max"` // Daily maximum in UV Index starting from 0. Assumes cloud free conditions.
	} `json:"daily"`
}

type OpenMeteoHourlyWeatherForecast struct {
	Time                     []string   `json:"time"`                       // Time array containing time for each bucket, e.g. if i = 10, Time[i] is the time of measurement for PM10[i]
	Temperature2M            []*float64 `json:"temperature_2m"`             // Air temperature at 2 meters above ground (°C)
	RelativeHumidity2M       []*float64 `json:"relativehumidity_2m"`        // Relative humidity at 2 meters above ground (%)
	Dewpoint2M               []*float64 `json:"dewpoint_2m"`                // Dew point temperature at 2 meters above ground (°C)
	ApparentTemperature      []*float64 `json:"apparent_temperature"`       // Apparent temperature is the perceived feels-like temperature combining wind chill factor, relative humidity and solar radiation
	PressureMSL              []*float64 `json:"pressure_msl"`               // Atmospheric air pressure reduced to mean sea level (msl) or pressure at surface. Typically pressure on mean sea level is used in meteorology. Surface pressure gets lower with increasing elevation.
	SurfacePressure          []*float64 `json:"surface_pressure"`           // Atmospheric air pressure reduced to mean sea level (msl) or pressure at surface. Typically pressure on mean sea level is used in meteorology. Surface pressure gets lower with increasing elevation.
	CloudCover               []*float64 `json:"cloudcover"`                 // Total cloud cover as an area fraction (%)
	CloudCoverLow            []*float64 `json:"cloudcover_low"`             // Low level clouds and fog up to 3 km altitude (%)
	CloudCoverMid            []*float64 `json:"cloudcover_mid"`             // Mid level clouds from 3 to 8 km altitude (%)
	CloudCoverHigh           []*float64 `json:"cloudcover_high"`            // High level clouds from 8 km altitude (%)
	WindSpeed10M             []*float64 `json:"windspeed_10m"`              // Wind speed at 10 meters above ground. Wind speed on 10 meters is the standard level. (km/h)
	WindSpeed80M             []*float64 `json:"windspeed_80m"`              // Wind speed at 80 meters above ground. Wind speed on 10 meters is the standard level. (km/h)
	WindSpeed120M            []*float64 `json:"windspeed_120m"`             // Wind speed at 120  meters above ground. Wind speed on 10 meters is the standard level. (km/h)
	WindSpeed180M            []*float64 `json:"windspeed_180m"`             // Wind speed at 180 meters above ground. Wind speed on 10 meters is the standard level. (km/h)
	WindDirection10M         []*float64 `json:"winddirection_10m"`          // Wind direction at 10 meters above ground (°)
	WindDirection80M         []*float64 `json:"winddirection_80m"`          // Wind direction at 80 meters above ground (°)
	WindDirection120M        []*float64 `json:"winddirection_120m"`         // Wind direction at 120 meters above ground (°)
	WindDirection180M        []*float64 `json:"winddirection_180m"`         // Wind direction at 180 meters above ground (°)
	WindGusts10M             []*float64 `json:"wind_gusts_10m"`             // Gusts at 10 meters above ground as a maximum of the preceding hour (km/h)
	ShortwaveRadiation       []*float64 `json:"shortwave_radiation"`        // Shortwave solar radiation as average of the preceding hour. This is equal to the total global horizontal irradiation (W/m²)
	DirectRadiation          []*float64 `json:"direct_radiation"`           // Direct solar radiation as average of the preceding hour on the horizontal plane and the normal plane (perpendicular to the sun) (W/m²)
	DirectNormalIrradiance   []*float64 `json:"direct_normal_irradiance"`   // Direct solar radiation as average of the preceding hour on the horizontal plane and the normal plane (perpendicular to the sun) (W/m²)
	DiffuseRadiation         []*float64 `json:"diffuse_radiation"`          // Diffuse solar radiation as average of the preceding hour (W/m²)
	VaporPressureDeficit     []*float64 `json:"vapor_pressure_deficit"`     // Vapour Pressure Deficit (VPD) in kilopascal (kPa). For high VPD (>1.6), water transpiration of plants increases. For low VPD (<0.4), transpiration decreases (kPa)
	Cape                     []*float64 `json:"cape"`                       // Convective available potential energy. See https://en.wikipedia.org/wiki/Convective_available_potential_energy (J/kg)
	Evapotranspiration       []*float64 `json:"evapotranspiration"`         // Evapotranspration from land surface and plants that weather models assumes for this location. Available soil water is considered. 1 mm evapotranspiration per hour equals 1 liter of water per spare meter. (mm)
	Et0FAOEvapotranspiration []*float64 `json:"et0_fao_evapotranspiration"` // ET₀ Reference Evapotranspiration of a well watered grass field. Based on https://www.fao.org/3/x0490e/x0490e04.htm ET₀ is calculated from temperature, wind speed, humidity and solar radiation. Unlimited soil water is assumed. ET₀ is commonly used to estimate the required irrigation for plants. (mm)
	Precipitation            []*float64 `json:"precipitation"`              // Total precipitation (rain, showers, snow) sum of the preceding hour (mm)
	Snowfall                 []*float64 `json:"snowfall"`                   // Snowfall amount of the preceding hour in centimeters. For the water equivalent in millimeter, divide by 7. E.g. 7 cm snow = 10 mm precipitation water equivalent (cm)
	PrecipitationProbability []*float64 `json:"precipitation_probability"`  // Probability of precipitation with more than 0.1 mm of the preceding hour. Probability is based on ensemble weather models with 0.25° (~27 km) resolution. 30 different simulations are computed to better represent future weather conditions. (%)
	Rain                     []*float64 `json:"rain"`                       // Rain from large scale weather systems of the preceding hour in millimeter (mm)
	Showers                  []*float64 `json:"showers"`                    // Showers from convective precipitation in millimeters from the preceding hour (mm)
	Weathercode              []*float64 `json:"weathercode"`                // Weather condition as a numeric code. Follow WMO weather interpretation codes. (WMO code)
	SnowDepth                []*float64 `json:"snow_depth"`                 // Snow depth on the ground
	FreezingLevelHeight      []*float64 `json:"freezinglevel_height"`       // Altitude above sea level of the 0°C level
	Visibility               []*float64 `json:"visibility"`                 // Viewing distance in meters. Influenced by low clouds, humidity and aerosols. Maximum visibility is approximately 24 km.
	SoilTemperature0CM       []*float64 `json:"soil_temperature_0cm"`       // Temperature in the soil at 0 cm depths. 0 cm is the surface temperature on land or water surface temperature on water.
	SoilTemperature6CM       []*float64 `json:"soil_temperature_6cm"`       // Temperature in the soil at 6 cm depths.
	SoilTemperature18CM      []*float64 `json:"soil_temperature_18cm"`      // Temperature in the soil at 18 cm depths.
	SoilTemperature54CM      []*float64 `json:"soil_temperature_54cm"`      // Temperature in the soil at 54 cm depths.
	SoilMoisture0To1CM       []*float64 `json:"soil_moisture_0_1cm"`        // Average soil water content as volumetric mixing ratio at 0-1 cm depths.
	SoilMoisture1To3CM       []*float64 `json:"soil_moisture_1_3cm"`        // Average soil water content as volumetric mixing ratio at 1-3 cm depths.
	SoilMoisture3To9CM       []*float64 `json:"soil_moisture_3_9cm"`        // Average soil water content as volumetric mixing ratio at 3-9 cm depths.
	SoilMoisture9To27CM      []*float64 `json:"soil_moisture_9_27cm"`       // Average soil water content as volumetric mixing ratio at 9-27 cm depths.
	SoilMoisture27To81CM     []*float64 `json:"soil_moisture_27_81cm"`      // Average soil water content as volumetric mixing ratio at 27-81 cm depths.

}
type OpenMeteoHourlyWeather struct {
	Time                      []string   `json:"time"`                          // Time array containing time for each bucket, e.g. if i = 10, Time[i] is the time of measurement for PM10[i]
	Temperature2M             []*float64 `json:"temperature_2m"`                // Air temperature at 2 meters above ground (°C)
	RelativeHumidity2M        []*float64 `json:"relativehumidity_2m"`           // Relative humidity at 2 meters above ground (%)
	Dewpoint2M                []*float64 `json:"dewpoint_2m"`                   // Dew point temperature at 2 meters above ground (°C)
	ApparentTemperature       []*float64 `json:"apparent_temperature"`          // Apparent temperature is the perceived feels-like temperature combining wind chill factor, relative humidity and solar radiation
	PressureMSL               []*float64 `json:"pressure_msl"`                  // Atmospheric air pressure reduced to mean sea level (msl) or pressure at surface. Typically pressure on mean sea level is used in meteorology. Surface pressure gets lower with increasing elevation.
	SurfacePressure           []*float64 `json:"surface_pressure"`              // Atmospheric air pressure reduced to mean sea level (msl) or pressure at surface. Typically pressure on mean sea level is used in meteorology. Surface pressure gets lower with increasing elevation.
	Precipitation             []*float64 `json:"precipitation"`                 // Total precipitation (rain, showers, snow) sum of the preceding hour (mm)
	Rain                      []*float64 `json:"rain"`                          // Rain from large scale weather systems of the preceding hour in millimeter (mm)
	Snowfall                  []*float64 `json:"snowfall"`                      // Snowfall amount of the preceding hour in centimeters. For the water equivalent in millimeter, divide by 7. E.g. 7 cm snow = 10 mm precipitation water equivalent (cm)
	CloudCover                []*float64 `json:"cloudcover"`                    // Total cloud cover as an area fraction (%)
	CloudCoverLow             []*float64 `json:"cloudcover_low"`                // Low level clouds and fog up to 3 km altitude (%)
	CloudCoverMid             []*float64 `json:"cloudcover_mid"`                // Mid level clouds from 3 to 8 km altitude (%)
	CloudCoverHigh            []*float64 `json:"cloudcover_high"`               // High level clouds from 8 km altitude (%)
	ShortwaveRadiation        []*float64 `json:"shortwave_radiation"`           // Shortwave solar radiation as average of the preceding hour. This is equal to the total global horizontal irradiation (W/m²)
	DirectRadiation           []*float64 `json:"direct_radiation"`              // Direct solar radiation as average of the preceding hour on the horizontal plane and the normal plane (perpendicular to the sun) (W/m²)
	DirectNormalIrradiance    []*float64 `json:"direct_normal_irradiance"`      // Direct solar radiation as average of the preceding hour on the horizontal plane and the normal plane (perpendicular to the sun) (W/m²)
	DiffuseRadiation          []*float64 `json:"diffuse_radiation"`             // Diffuse solar radiation as average of the preceding hour (W/m²)
	SunshineDuration          []*int     `json:"sunshine_duration"`             // Number of seconds of sunshine of the preceding hour per hour calculated by direct normalized irradiance exceeding 120 W/m², following the WMO definition.
	WindSpeed10M              []*float64 `json:"windspeed_10m"`                 // Wind speed at 10 meters above ground. Wind speed on 10 meters is the standard level. (km/h)
	WindSpeed100M             []*float64 `json:"windspeed_100m"`                // Wind speed at 100 meters above ground. Wind speed on 10 meters is the standard level. (km/h)
	WindDirection10M          []*float64 `json:"winddirection_10m"`             // Wind direction at 10 meters above ground (°)
	WindDirection100M         []*float64 `json:"winddirection_100m"`            // Wind direction at 100 meters above ground (°)
	WindGusts10M              []*float64 `json:"wind_gusts_10m"`                // Gusts at 10 meters above ground as a maximum of the preceding hour (km/h)
	Et0FAOEvapotranspiration  []*float64 `json:"et0_fao_evapotranspiration"`    // ET₀ Reference Evapotranspiration of a well watered grass field. Based on https://www.fao.org/3/x0490e/x0490e04.htm ET₀ is calculated from temperature, wind speed, humidity and solar radiation. Unlimited soil water is assumed. ET₀ is commonly used to estimate the required irrigation for plants. (mm)
	Weathercode               []*float64 `json:"weathercode"`                   // Weather condition as a numeric code. Follow WMO weather interpretation codes. (WMO code)
	SnowDepth                 []*float64 `json:"snow_depth"`                    // Snow depth on the ground
	VaporPressureDeficit      []*float64 `json:"vapor_pressure_deficit"`        // Vapour Pressure Deficit (VPD) in kilopascal (kPa). For high VPD (>1.6), water transpiration of plants increases. For low VPD (<0.4), transpiration decreases (kPa)
	SoilTemperature0To7CM     []*float64 `json:"soil_temperature_0_to_7cm"`     // Temperature in the soil at 0 cm to 7cm depths. 0 cm is the surface temperature on land or water surface temperature on water.
	SoilTemperature7To28CM    []*float64 `json:"soil_temperature_7_to_28cm"`    // Temperature in the soil at 6 cm to 28cm depths.
	SoilTemperature28To100CM  []*float64 `json:"soil_temperature_28_to_100cm"`  // Temperature in the soil at 28 cm to 100cm depths.
	SoilTemperature100To255CM []*float64 `json:"soil_temperature_100_to_255cm"` // Temperature in the soil at 100 cm to 255 cm depths.
	SoilMoisture0To7CM        []*float64 `json:"soil_moisture_0_to_7cm"`        // Average soil water content as volumetric mixing ratio at 0-7 cm depths.
	SoilMoisture7To28CM       []*float64 `json:"soil_moisture_7_to_28cm"`       // Average soil water content as volumetric mixing ratio at 7-28 cm depths.
	SoilMoisture28To100CM     []*float64 `json:"soil_moisture_28_to_100cm"`     // Average soil water content as volumetric mixing ratio at 28-100 cm depths.
	SoilMoisture100To255CM    []*float64 `json:"soil_moisture_100_to_255cm"`    // Average soil water content as volumetric mixing ratio at 100-255 cm depths.
}

type OpenMeteoWeatherResponse struct {
	Latitude             float64                `json:"latitude"`
	Longitude            float64                `json:"longitude"`
	Elevation            float64                `json:"elevation"`
	GenerationTimeMs     float64                `json:"generation_time_ms"`
	UtcOffsetSeconds     int                    `json:"utc_offset_seconds"`
	Timezone             string                 `json:"timezone"`
	TimezoneAbbreviation string                 `json:"timezone_abbreviation"`
	Hourly               OpenMeteoHourlyWeather `json:"hourly"`
}

type OpenMeteoErrorResponse struct {
	Error  bool   `json:"error"`
	Reason string `json:"reason"`
}
