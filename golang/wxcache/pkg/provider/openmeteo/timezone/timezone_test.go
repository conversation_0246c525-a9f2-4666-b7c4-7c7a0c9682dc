package timezone_test

import (
	"testing"
	"time"

	"llif.org/wxcache/pkg/provider/openmeteo/timezone"
)

func TestTimezoneShouldBeRecognisedFromOffset(t *testing.T) {
	tests := []struct {
		timestamp            string
		expectedLocationName string
	}{
		{"2024-03-19T23:00:00-11:00", "Pacific/Niue"},
		{"2024-03-19T23:00:00-10:00", "Pacific/Rarotonga"},
		{"2024-03-19T23:00:00-09:00", "America/Anchorage"},
		{"2024-03-19T23:00:00-08:00", "America/Los_Angeles"},
		{"2024-03-19T23:00:00-07:00", "America/Denver"},
		{"2024-03-19T23:00:00-06:00", "America/Chicago"},
		{"2024-03-19T23:00:00-05:00", "America/New_York"},
		{"2024-03-19T23:00:00-04:00", "America/Barbados"},
		{"2024-03-19T23:00:00-03:00", "America/Sao_Paulo"},
		{"2024-03-19T23:00:00-02:00", "America/Nuuk"},
		{"2024-03-19T23:00:00-01:00", "America/Scoresbysund"},
		{"2024-03-19T15:00:00Z", "GMT"},
		{"2024-03-19T09:00:00+01:00", "Europe/Berlin"},
		{"2024-03-19T09:00:00+02:00", "Africa/Cairo"},
		{"2024-03-19T09:00:00+03:00", "Europe/Moscow"},
		{"2024-03-19T09:00:00+04:00", "Asia/Baku"},
		{"2024-03-19T09:00:00+05:00", "Antarctica/Mawson"},
		{"2024-03-19T09:00:00+06:00", "Asia/Bishkek"},
		{"2024-03-19T09:00:00+07:00", "Asia/Bangkok"},
		{"2024-03-19T09:00:00+08:00", "Asia/Singapore"},
		{"2024-03-19T09:00:00+09:00", "Asia/Tokyo"},
		{"2024-03-19T09:00:00+10:00", "Asia/Ust-Nera"},
		{"2024-03-19T09:00:00+11:00", "Australia/Sydney"},
		{"2024-03-19T09:00:00+13:00", "Pacific/Auckland"},
	}

	for _, test := range tests {
		timestamp, err := time.Parse(time.RFC3339, test.timestamp)
		if err != nil {
			t.Errorf("Error parsing timestamp: %v", err)
			continue
		}

		_, offset := timestamp.Zone()
		actualLocationName, err := timezone.GetLocationFromTimeOffset(offset)
		if err != nil {
			t.Errorf("Error getting location from offset: %v", err)
		}

		if actualLocationName != test.expectedLocationName {
			t.Errorf("For timestamp %s, expected location name %s, but got %s", test.timestamp, test.expectedLocationName, actualLocationName)
		}
	}
}
