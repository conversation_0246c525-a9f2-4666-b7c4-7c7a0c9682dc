server {
    server_name $API_DOMAIN;

    client_max_body_size 8G;
    root /var/www/html;
    index index.html;

    listen 443 ssl;
    http2 on;
    listen 443 quic reuseport;
    resolver 127.0.0.11 valid=10s;  # Refreshes every 10 seconds
    
    location / {
        return 404;
    }

    location /file/ {
        set $upstream file_service:8001;
        proxy_pass http://$upstream;
        proxy_http_version 1.1;
    }

    location /data/ {
        set $upstream data_service:8003;
        proxy_pass http://$upstream;
        proxy_http_version 1.1;
    }

    location /user/ {
        set $upstream user_service:8004;
        proxy_pass http://$upstream;
        proxy_http_version 1.1;
    }

    location /mobile/ {
        set $upstream mobile_service:8005;
        proxy_pass http://$upstream;
        proxy_http_version 1.1;
    }

    ssl_certificate /etc/letsencrypt/live/$API_DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$API_DOMAIN/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}
