from dataclasses import dataclass


@dataclass
class AmazonShoppingLabels:
    LABEL_ORDER_ID = "Order ID"
    LABEL_ORDER_DATE = "Order Date"
    LABEL_CATEGORY = "Category"
    LABEL_ASIN_ISBN = "ASIN/ISBN"
    LABEL_UNSPSC_CODE = "UNSPSC Code"
    LABEL_RELEASE_DATE = "Release Date"
    LABEL_CONDITION = "Condition"
    LABEL_SELLER = "Seller"
    LABEL_LIST_PRICE_PER_UNIT = "List Price Per Unit"
    LABEL_PURCHASE_PRICE_PER_UNIT = "Purchase Price Per Unit"
    LABEL_QUANTITY = "Quantity"
    LABEL_PAYMENT_INSTRUMENT_TYPE = "Payment Instrument Type"
    LABEL_PURCHASE_ORDER_NUMBER = "Purchase Order Number"
    LABEL_PO_LINE_NUMBER = "PO Line Number"
    LABEL_ORDERING_CUSTOMER_EMAIL = "Ordering Customer Email"
    LABEL_SHIPMENT_DATE = "Shipment Date"
    LABEL_SHIPPING_ADDRESS_NAME = "Shipping Address Name"
    LABEL_SHIPPING_ADDRESS_STREET_1 = "Shipping Address Street 1"
    LABEL_SHIPPING_ADDRESS_STREET_2 = "Shipping Address Street 2"
    LABEL_SHIPPING_ADDRESS_CITY = "Shipping Address City"
    LABEL_SHIPPING_ADDRESS_STATE = "Shipping Address State"
    LABEL_SHIPPING_ADDRESS_ZIP = "Shipping Address Zip"
    LABEL_ORDER_STATUS = "Order Status"
    LABEL_CARRIER_NAME_AND_TRACKING_NUMBER = "Carrier Name & Tracking Number"
    LABEL_ITEM_SUBTOTAL_TAX = "Item Subtotal Tax"
    LABEL_ITEM_SUBTOTAL = "Item Subtotal"
    LABEL_ITEM_TOTAL = "Item Total"
    LABEL_CURRENCY = "Currency"

    # IR labels
    LABEL_CARRIER_NAME = "carrier_name"
    LABEL_CARRIER_TRACKING_NUMBER = "carrier_tracking_number"
