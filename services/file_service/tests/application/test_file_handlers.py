from pathlib import Path
from tempfile import TemporaryDirectory
from types import GeneratorType

import pytest

from services.file_service.application.file_handlers import (
    delete_directory_contents,
    find_all_file_paths_by_ext,
    find_all_file_paths_in_root_by_ext,
    find_file_names_matching_regex_format,
    find_file_paths_matching_regex_format,
)


@pytest.fixture()
def _file_path():
    return Path(__file__)


def test_find_all_file_paths_by_ext(_file_path):
    file_path_gen = find_all_file_paths_by_ext(search_path=_file_path.parent.parent, ext=r".py")
    assert isinstance(file_path_gen, GeneratorType)
    file_paths = list(file_path_gen)
    assert list(file_paths)
    assert __file__ in file_paths


def test_find_all_file_paths_in_root_by_ext(_file_path):
    file_path_gen = find_all_file_paths_in_root_by_ext(search_path=_file_path.parent, ext=r".py")
    assert isinstance(file_path_gen, GeneratorType)
    file_paths = list(file_path_gen)
    assert list(file_paths)
    assert __file__ in file_paths


def test_find_file_paths_matching_regex_format(_file_path):
    file_path_gen = find_file_paths_matching_regex_format(search_path=_file_path.parent, regex_format=r"test_.*")
    assert isinstance(file_path_gen, GeneratorType)
    file_paths = list(file_path_gen)
    assert list(file_paths)
    assert __file__ in file_paths


def test_find_file_names_matching_regex_format(_file_path):
    file_names_gen = find_file_names_matching_regex_format(search_path=_file_path.parent, regex_format=r"test_.*")
    assert isinstance(file_names_gen, GeneratorType)
    file_names = list(file_names_gen)
    assert list(file_names)
    assert _file_path.name in file_names


def _ensure_new_file(path: Path):
    assert not path.is_file()
    path.touch()
    assert path.is_file()
    return path


def _ensure_new_dir(path: Path):
    """also creates parent directories if needed"""
    path.mkdir(parents=True, exist_ok=False)
    assert path.is_dir()
    return path


# it seems that just using the "fs" fixture from the pyfakefs pytest plugin mocks all filesystem I/O :)
def test_delete_directory_contents(fs):  # pylint:disable=unused-argument,invalid-name
    with TemporaryDirectory() as tempdir:
        tempdir_path = Path(tempdir)
        assert tempdir_path.is_dir()

        # are we starting with a clean state?
        assert len(list(tempdir_path.iterdir())) == 0  # check just the top level

        # put 1 file at top level
        #     1 folder at top level and 1 file inside it to second level
        level_1_file_path = _ensure_new_file(tempdir_path / "level1file.txt")
        level_1_folder_path = _ensure_new_dir(tempdir_path / "level1folder")
        _ensure_new_file(level_1_folder_path / "level2file.txt")

        # check there are only files WE've created
        assert len(list(tempdir_path.iterdir())) == 2  # check just the top level

        # run the deletion
        delete_directory_contents(tempdir_path)

        # The main tempdir should NOT be deleted, ...
        assert tempdir_path.is_dir()

        # ... ONLY the rest should be deleted:
        assert not level_1_file_path.exists()
        assert not level_1_folder_path.exists()  # should imply level_2_file too

        # check the directory is really empty
        assert len(list(tempdir_path.iterdir())) == 0  # check just the top level
