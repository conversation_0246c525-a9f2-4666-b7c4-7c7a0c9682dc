from enum import StrEnum
from typing import Type

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document
from services.base.type_resolver import TypeResolver


class ExportableType(StrEnum):
    # Exercise Collection
    Exercise = DataType.Exercise
    Cardio = DataType.Cardio
    Strength = DataType.Strength

    # Content Collection
    Audio = DataType.Audio
    Interactive = DataType.Interactive
    Image = DataType.Image
    Text = DataType.Text
    Video = DataType.Video
    Content = DataType.Content

    # Body Metric Collection
    BodyMetric = DataType.BodyMetric
    BloodPressure = DataType.BloodPressure
    BloodGlucose = DataType.BloodGlucose

    # Nutrition Collection
    Drink = DataType.Drink
    Food = DataType.Food
    Supplement = DataType.Supplement

    # Other
    CoreEvent = DataType.CoreEvent
    SleepV3 = DataType.SleepV3
    Emotion = DataType.Emotion
    Note = DataType.Note
    Stress = DataType.Stress
    Symptom = DataType.Symptom
    Medication = DataType.Medication

    # Documents
    Plan = DataType.Plan
    UseCase = DataType.UseCase
    EventTemplate = DataType.EventTemplate
    GroupTemplate = DataType.GroupTemplate
    ExtensionRun = DataType.ExtensionRun

    # Legacy
    AirQuality = DataType.AirQuality
    Pollen = DataType.Pollen
    UserLogs = DataType.UserLogs
    Weather = DataType.Weather
    HeartRate = DataType.HeartRate
    RestingHeartRate = DataType.RestingHeartRate
    Sleep = DataType.Sleep
    Steps = DataType.Steps
    DiaryEvents = DataType.DiaryEvents
    Location = DataType.Location
    ShoppingActivity = DataType.ShoppingActivity

    def to_domain_model(self) -> Type[Document]:
        return TypeResolver.get_document(type_id=self)
