from typing import List

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.event_models.takeout_export_scheduled_event_model import TakeoutExportScheduledModel
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.constants.messaging import MessageTopics
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.message_queue.message_handler_base import UseCaseHandlerBase
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.file_service.application.use_cases.export.process_data_export_use_case import ProcessDataExportUseCase
from services.file_service.dependency_bootstrapper import (
    get_async_message_broker_transient,
    get_export_repository_transient,
    get_extension_detail_repo_transient,
    get_extension_result_repo_transient,
    get_extension_run_repo_transient,
    get_object_storage_service_transient,
    get_search_service_transient,
)
from settings.app_config import settings
from settings.app_secrets import secrets


class TakeoutExportScheduledHandler(UseCaseHandlerBase):
    def __init__(
        self,
        search_service: DocumentSearchService,
        export_repo: ExportTaskRepository,
        object_storage_service: ObjectStorageService,
        message_broker: AsyncMessageBrokerClient,
        extension_run_repo: ExtensionRunRepository,
        extension_result_repo: ExtensionResultRepository,
        extension_detail_repo: ExtensionDetailRepository,
    ):
        self._search_service = search_service
        self._export_repo = export_repo
        self._message_broker = message_broker
        self._object_storage_service = object_storage_service
        self._extension_run_repo = extension_run_repo
        self._extension_result_repo = extension_result_repo
        self._extension_detail_repo = extension_detail_repo

    @classmethod
    def listen_to(cls) -> List[MessageTopics]:
        return [MessageTopics.TOPIC_TAKEOUT_EXPORT_SCHEDULED]

    @staticmethod
    async def initialize_and_execute(*args, **kwargs):
        search_service = get_search_service_transient()
        export_repo = get_export_repository_transient()
        object_storage_service = get_object_storage_service_transient()
        message_broker = get_async_message_broker_transient()
        extension_run_repo = get_extension_run_repo_transient()
        extension_result_repo = get_extension_result_repo_transient()
        extension_detail_repository = get_extension_detail_repo_transient()
        TelemetryInstrumentor.initialize(service_name="file_service_worker", settings=settings, secrets=secrets)

        output = await TakeoutExportScheduledHandler(
            search_service=search_service,
            export_repo=export_repo,
            object_storage_service=object_storage_service,
            message_broker=message_broker,
            extension_run_repo=extension_run_repo,
            extension_result_repo=extension_result_repo,
            extension_detail_repo=extension_detail_repository,
        ).execute(*args, **kwargs)

        await search_service.close()
        await object_storage_service.close()
        return output

    async def execute(self, message_body: dict, *args, **kwargs):
        await super()._do_execute_async(
            triggering_event=TakeoutExportScheduledModel(**message_body),
            use_case=ProcessDataExportUseCase(
                search_service=self._search_service,
                object_storage_service=self._object_storage_service,
                message_broker_client=self._message_broker,
                export_repo=self._export_repo,
                extension_run_repo=self._extension_run_repo,
                extension_result_repo=self._extension_result_repo,
                extension_detail_repo=self._extension_detail_repo,
            ),
        )

    def publish(self, triggering_event: TakeoutExportScheduledModel) -> None:
        return None
