import logging
import os
import re
import shutil
from pathlib import Path
from types import GeneratorType


def delete_directory_contents(dir_path: str) -> None:
    """Deletes directory/folder contents,
    but NOT the directory/folder itself!
    raises: uses Path.unlink() and shutil.rmtree() which can throw their own exceptions

    Especially useful when you re-use temporary directories:
    e.g. tempfile.TemporaryDirectory.cleanup() removes ALSO the directory ITSELF!
    - it's because it relies on the shutil.rmtree() which works this way
    """

    # @todo create a better solution.
    try:
        for file_or_dir in Path(dir_path).iterdir():
            # tempfile.TemporaryDirectory.cleanup() removes ALSO the directory ITSELF!
            # 1st level files must be deleted "manually" with unlink,
            # since using shutil.rmtree on the dir_path would remove the dir itself!
            if file_or_dir.is_file():
                Path.unlink(file_or_dir)
            else:
                shutil.rmtree(file_or_dir)
    except FileNotFoundError:
        logging.warning("File not found in expected folder: %s", dir_path)


def find_all_file_paths_by_ext(search_path: str, ext: str) -> GeneratorType:
    """Recursively searches for all files in root search_path folder and subfolders with specified extension
    Returns: list of full pathnames of the found files
    """

    for root, dirs, files in os.walk(search_path):
        for file in files:
            if file.endswith(ext):
                yield os.path.join(root, file)


def find_all_file_paths_in_root_by_ext(search_path: str, ext: str) -> GeneratorType:
    """Searches for all files in root search_path folder with specified extension"""

    for root, dirs, files in os.walk(search_path):
        for file in files:
            if file.endswith(ext):
                yield os.path.join(root, file)
        break


def find_file_names_matching_regex_format(search_path: str, regex_format: str) -> GeneratorType:
    """Returns all file NAMES in search_path matching the regex_format
    Please note, that it really does return ONLY fileNAMES (not paths),
    to get relative PATHS (subdirectories) use find_file_paths_matching_regex_format() instead"""
    matcher = re.compile(regex_format)
    for dirpath, dirs, files in os.walk(search_path):
        for file in files:
            if matcher.match(file):
                yield file


def find_file_paths_matching_regex_format(search_path: str, regex_format: str) -> GeneratorType:
    """Returns all filepaths relative to root(search_path) matching the regex_format"""
    matcher = re.compile(regex_format)
    for dirpath, dirs, files in os.walk(search_path):
        for file in files:
            if matcher.match(file):
                yield os.path.join(dirpath, file)
