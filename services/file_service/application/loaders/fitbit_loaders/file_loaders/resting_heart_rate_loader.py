import logging
import os
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, <PERSON><PERSON>
from uuid import UUID
from zoneinfo import ZoneInfo

import ij<PERSON>
from opensearchpy import OpenSearch

from services.base.application.utils.metadata import create_metadata
from services.base.application.utils.time import get_datetime_difference
from services.base.domain.constants.document_labels import Document<PERSON>abels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC, SECONDS_IN_HOUR
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import DataIntegrity, Organization
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.resting_heart_rate import RestingHeartRate, RestingHeartRateDetail
from services.file_service.application.converters import partial_timestamp_parser
from services.file_service.application.file_handlers import find_file_paths_matching_regex_format
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.application.loaders.fitbit_loaders.fitbit_constants import (
    FITBIT_PHYSICAL_ACTIVITIES,
    FITBIT_ROOT_PATH,
)
from services.file_service.application.loaders.fitbit_loaders.utils import get_person_folder
from settings.app_constants import MESSAGE_NO_FILES_FOUND, MESSAGE_UNABLE_TO_LOAD


class RestingHeartRateLoader(FileLoaderBase):
    """Loader for Fit Bit export file"""

    KEY_TIME = "dateTime"
    KEY_VALUE = "value"
    KEY_ERROR = "error"
    _source = "resting_heart_rate"
    PROVIDER = Provider.FITBIT
    # length of created bucket in seconds
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_paths = []
        fitbit_physical_activities_path = os.path.join(
            get_person_folder(os.path.join(data_dir_path, FITBIT_ROOT_PATH)), FITBIT_PHYSICAL_ACTIVITIES
        )
        self.search_path = os.path.join(data_dir_path, fitbit_physical_activities_path)

    def load_files_data(self) -> None:
        # Example Heart Rate file: heart_rate-2018-08-24.json
        regex_format = r"^resting_heart_rate.*json$"
        self.file_paths = list(find_file_paths_matching_regex_format(self.search_path, regex_format))
        if not self.file_paths:
            logging.exception("%s! Folder: %s", MESSAGE_NO_FILES_FOUND, self.search_path)
        self.process_data()

    def process_data(self) -> None:
        for file_path in self.file_paths:
            self._process_resting_heart_rate_files(file_path)

    def _parse_data_to_entry(self, entry_list: List[Dict[str, Any]]) -> RestingHeartRate:
        """Parses data to output heart rate entry"""
        starting_entry = entry_list[0]
        ending_entry = entry_list[-1]
        starting_datetime = self._parse_tz_datetime(starting_entry[DocumentLabels.TIMESTAMP])
        ending_datetime = self._parse_tz_datetime(ending_entry[DocumentLabels.TIMESTAMP])
        output_rhr_detail, bpm_avg, bpm_min, bpm_max = self._parse_rhr_values(entry_list)

        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            organization=Organization.FITBIT,
            data_integrity=DataIntegrity.MEDIUM,
        )

        output_resting_heart_rate = RestingHeartRate(
            timestamp=starting_datetime,
            bpm_avg=bpm_avg,
            bpm_min=bpm_min,
            bpm_max=bpm_max,
            duration=int((ending_datetime - starting_datetime).total_seconds()),
            rhr_detail=output_rhr_detail,
            end_time=ending_datetime,
            metadata=metadata,
        )
        return output_resting_heart_rate

    def _process_resting_heart_rate_files(self, file_path: str):
        try:
            json_entries = ijson.items(open(os.path.normpath(file_path), encoding="utf8"), "item")
            to_be_committed_entries: List[str] = []
            # Defines list of entries to be parsed into single database entry
            bulk_entries: List[Dict[str, Any]] = []
            cached_json_entry: dict = {}
            json_entry: dict
            for json_entry in json_entries:
                try:
                    data_entry = json_entry.copy()
                    if not data_entry.get(self.KEY_VALUE)[self.KEY_VALUE]:
                        continue
                    data_entry[DocumentLabels.TIMESTAMP] = self._parse_tz_datetime(
                        partial_timestamp_parser(data_entry.get(self.KEY_TIME))
                    )
                    data_entry[DocumentLabels.USER_UUID] = str(self.user_uuid)
                    del data_entry[self.KEY_TIME]

                    # Copies and appends the starting entry
                    if not cached_json_entry:
                        cached_json_entry = data_entry

                    # Go through next entries
                    time_difference = get_datetime_difference(
                        cached_json_entry[DocumentLabels.TIMESTAMP],
                        data_entry[DocumentLabels.TIMESTAMP],
                    )
                    if time_difference < self.LOAD_AGGREGATION_INTERVAL:
                        bulk_entries.append(cached_json_entry)
                        continue

                    output_resting_heart_rate: RestingHeartRate = self._parse_data_to_entry(bulk_entries)
                    to_be_committed_entries.append(output_resting_heart_rate.model_dump_json(by_alias=True))

                    del bulk_entries[:]
                    cached_json_entry = data_entry
                    bulk_entries.append(cached_json_entry)
                except Exception as error:  # pylint:disable=broad-except
                    logging.exception("Error processing entry: %s, %s", json_entry, str(error))
                    del bulk_entries[:]
                self._commit_if_limit_reached(to_be_committed_entries)
            if bulk_entries:
                output_heart_rate: RestingHeartRate = self._parse_data_to_entry(bulk_entries)
                to_be_committed_entries.append(output_heart_rate.model_dump_json(by_alias=True))
            self._commit(entries=to_be_committed_entries)
        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, file_path)
        except Exception as error:  # pylint:disable=broad-except
            logging.exception("Error processing entries: %s", repr(error))

    def _parse_rhr_values(self, entry_list: List[Dict[str, Any]]) -> Tuple[
        List[RestingHeartRateDetail],
        float,
        float,
        float,
    ]:
        output_list: List[RestingHeartRateDetail] = []
        value_list: List[float] = []
        for entry in entry_list:
            try:
                value = float(entry[self.KEY_VALUE][self.KEY_VALUE])
                error = float(entry[self.KEY_VALUE].get(self.KEY_ERROR))
                timestamp = entry[DocumentLabels.TIMESTAMP]

                value_list.append(value)
                output_list.append(
                    RestingHeartRateDetail(
                        value=value,
                        confidence=error,
                        timestamp=timestamp,
                    )
                )
            except (KeyError, ValueError) as error:
                logging.warning("Could not process RHR detail entry %s, %s", entry, error)

        if not value_list:
            raise ValueError("Could not process any data for this bucket.")

        bpm_avg = sum(value_list) / len(value_list)
        bpm_min = min(value_list)
        bpm_max = max(value_list)

        return output_list, bpm_avg, bpm_min, bpm_max
