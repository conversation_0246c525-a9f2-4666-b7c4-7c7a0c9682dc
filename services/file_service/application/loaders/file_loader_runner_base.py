import logging
from typing import List
from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.provider import SupportedDataProviders
from services.base.infrastructure.database.opensearch.wrappers.client import get_default_os_client
from services.base.infrastructure.database.sql_alchemy.db_state_manager import get_db_session
from services.base.infrastructure.database.sql_alchemy.models.upload_state_entity import UploadStateEntity

from .file_loader_run_info import FileLoaderRunInfo


class FileLoadersRunnerBase:
    provider: SupportedDataProviders = None

    def __init__(self, user_uuid: UUID, data_dir_path: str, fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC):
        if not isinstance(self.provider, SupportedDataProviders):
            raise NotImplementedError(f"provider property [{self.provider}] is not a valid SupportedDataProviders")

        self.user_uuid: UUID = user_uuid
        self.fallback_timezone = fallback_timezone
        self.data_dir_path: str = data_dir_path
        self.client = get_default_os_client()
        logging.info("Running file loader runner: %s for user with uuid: %s", type(self), self.user_uuid)

    def run_loaders(self, loader_run_infos: List[FileLoaderRunInfo]):
        """runs each loader for each defined index or sets of defined indexes"""

        loaders_total_count = len(loader_run_infos)
        loaders_finished_count = 0

        # indicate that loading has started with 1%
        self._update_loading_progress(1)

        for run_info in loader_run_infos:
            loader = run_info.loader  # CLASS NOT INSTANCE!

            for data_type in run_info.data_types:
                logging.info(
                    "Running file loader: %s index_info: %s uuid: %s",
                    loader.__name__,  # class, type(loader) would return abc.ABCMeta !
                    repr(data_type),
                    self.user_uuid,
                )

                try:
                    # single index per loader run
                    if not isinstance(data_type, (str, dict, DataType)):
                        raise ValueError(
                            "unexpected type of indexes in FileLoaderRunInfo instance"
                            + f"\n    index info: {repr(data_type)}"
                            + f"\n    index info type: {type(data_type)}"
                        )

                    loader(
                        user_uuid=self.user_uuid,
                        data_type=data_type,
                        client=self.client,
                        data_dir_path=self.data_dir_path,
                        fallback_timezone=self.fallback_timezone,
                    ).load_files_data()

                    logging.info("Finished: file loader: %s uuid: %s", loader.__name__, self.user_uuid)

                except Exception as e:  # pylint: disable=broad-except
                    logging.exception(
                        "Loader %s failed to load to indexes: %s with exception: %s",
                        loader.__name__,  # class, type(loader) would return abc.ABCMeta !
                        data_type,
                        repr(e),
                    )

            # single loader finished => update loading_progress
            loaders_finished_count += 1
            if loaders_finished_count == loaders_total_count:
                self._update_loading_progress(100)
            else:
                self._update_loading_progress(int((100 / loaders_total_count) * loaders_finished_count))

    def _update_loading_progress(self, percentage):
        with get_db_session() as db_session:
            UploadStateEntity.update_loading_progress_to_db(
                db_session,
                user_uuid=self.user_uuid,
                provider=self.provider,
                loading_progress=percentage,
            )
