from datetime import time, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID
from zoneinfo import ZoneInfo

import numpy as np
from opensearchpy import OpenSearch

from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.utils.metadata import create_metadata
from services.base.application.utils.time import get_datetime_difference
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC, SECONDS_IN_HOUR
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import DataIntegrity, Organization, Service
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.heart_rate import HeartRate, HeartRateBpmDetail
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.shared import BaseDataModel
from services.file_service.application.converters import datetime_to_iso_converter
from services.file_service.application.loaders.google_loaders.file_loaders.constants.daily_activity import (
    KEY_AVERAGE_HEART_RATE,
)


class HeartRateInputModel(BaseDataModel):
    start_time: str
    end_time: str
    avg_heart_rate: float


class HeartRateLoader(LoaderBase):
    _source = "heart_rate"
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)
    PROVIDER = Provider.GOOGLE

    def __init__(
        self,
        user_uuid: UUID,
        data_type: DataType,
        entities: List[HeartRateInputModel],
        file_date: str,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.entities = entities
        self.file_date = file_date

    def process_data(self) -> None:
        data = (entity for entity in self.entities)
        bulk_entries: List[Dict[str, Any]] = []
        to_be_comitted_entries: List[str] = []
        cached_entry: Optional[Dict] = None

        for entity in data:
            if not entity.avg_heart_rate or np.isnan(entity.avg_heart_rate):
                continue

            row_timestamp = self._parse_tz_datetime(
                datetime_to_iso_converter(
                    time=entity.start_time,
                    date=self.file_date,
                )
            )

            end_timestamp = self._parse_tz_datetime(
                datetime_to_iso_converter(
                    time=entity.end_time,
                    date=self.file_date,
                )
            )

            data_entry = entity.model_dump()
            data_entry[DocumentLabels.USER_UUID] = self.user_uuid
            data_entry[DocumentLabels.TIMESTAMP] = row_timestamp
            data_entry[DocumentLabels.END_TIME] = end_timestamp

            cached_entry = cached_entry if cached_entry else data_entry

            time_difference = get_datetime_difference(
                cached_entry[DocumentLabels.TIMESTAMP],
                data_entry[DocumentLabels.TIMESTAMP],
            )

            if time_difference < self.LOAD_AGGREGATION_INTERVAL:
                bulk_entries.append(data_entry)
                continue

            output_heart_rate: HeartRate = self._parse_data_to_entry(bulk_entries)
            to_be_comitted_entries.append(output_heart_rate.model_dump_json(by_alias=True))
            del bulk_entries[:]
            cached_entry = data_entry
            bulk_entries.append(cached_entry)
            self._commit_if_limit_reached(to_be_comitted_entries)
        if bulk_entries:
            output_heart_rate: HeartRate = self._parse_data_to_entry(bulk_entries)
            to_be_comitted_entries.append(output_heart_rate.model_dump_json(by_alias=True))
        self._commit(entries=to_be_comitted_entries)

    def _parse_data_to_entry(self, entries: List[Dict[str, Any]]) -> HeartRate:
        starting_entry = entries[0]
        ending_entry = entries[-1]
        starting_datetime = starting_entry[DocumentLabels.TIMESTAMP]
        ending_datetime = ending_entry[DocumentLabels.END_TIME]
        output_bpm_detail, bpm_avg, bpm_min, bpm_max = self._parse_bpm_values(entries)
        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            organization=Organization.GOOGLE,
            data_integrity=DataIntegrity.MEDIUM,
            service=Service.GOOGLE_FIT,
        )

        if ending_datetime.time() == time(hour=0, minute=0, second=0):
            ending_datetime += timedelta(days=1)

        output_heart_rate = HeartRate(
            timestamp=starting_datetime,
            bpm_avg=bpm_avg,
            bpm_min=bpm_min,
            bpm_max=bpm_max,
            bpm_detail=output_bpm_detail,
            duration=int((ending_datetime - starting_datetime).total_seconds()),
            end_time=ending_datetime,
            metadata=metadata,
        )
        return output_heart_rate

    def _parse_bpm_values(self, entries: List[Dict[str, Any]]) -> Tuple[List[HeartRateBpmDetail], float, float, float]:
        result: List[HeartRateBpmDetail] = []
        values: List[float] = []
        for entry in entries:
            timestamp = entry.get(DocumentLabels.TIMESTAMP)
            value = entry.get(KEY_AVERAGE_HEART_RATE)

            values.append(value)
            result.append(
                HeartRateBpmDetail(
                    value=value,
                    timestamp=timestamp,
                )
            )

        bpm_avg = sum(values) / len(values)
        bpm_min = min(values)
        bpm_max = max(values)
        return result, bpm_avg, bpm_min, bpm_max
