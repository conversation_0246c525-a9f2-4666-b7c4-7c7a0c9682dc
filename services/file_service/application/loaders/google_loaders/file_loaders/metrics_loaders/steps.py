import logging
from datetime import datetime, time, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID
from zoneinfo import ZoneInfo

import numpy as np
from opensearchpy import OpenSearch

from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.utils.metadata import create_metadata
from services.base.application.utils.time import get_datetime_difference
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC, SECONDS_IN_HOUR
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import DataIntegrity, Organization, Service
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.steps import Steps, StepsDetail
from services.file_service.application.converters import datetime_to_iso_converter
from services.file_service.application.loaders.google_loaders.file_loaders.constants.daily_activity import (
    KEY_STEP_COUNT,
)


class StepsInputModel(BaseDataModel):
    start_time: str
    end_time: str
    step_count: float


class StepsLoader(LoaderBase):
    _source = "steps"
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)
    PROVIDER = Provider.GOOGLE

    def __init__(
        self,
        user_uuid: UUID,
        data_type: DataType,
        entities: List[StepsInputModel],
        file_date: str,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.entities = entities
        self.file_date = file_date

    def process_data(self) -> None:
        data = (entity for entity in self.entities)
        bulk_entries: List[Dict[str, Any]] = []
        to_be_committed_entries: List[str] = []
        cached_entry: Optional[Dict] = None

        for entity in data:
            if not entity.step_count or np.isnan(entity.step_count):
                continue

            row_timestamp = self._parse_tz_datetime(
                datetime_to_iso_converter(
                    time=entity.start_time,
                    date=self.file_date,
                )
            )
            end_timestamp = self._parse_tz_datetime(
                datetime_to_iso_converter(
                    time=entity.end_time,
                    date=self.file_date,
                )
            )
            data_entry = entity.model_dump()
            data_entry[DocumentLabels.TIMESTAMP] = row_timestamp
            data_entry[DocumentLabels.END_TIME] = end_timestamp
            data_entry[DocumentLabels.USER_UUID] = self.user_uuid

            cached_entry = cached_entry if cached_entry else data_entry

            time_difference = get_datetime_difference(
                cached_entry[DocumentLabels.TIMESTAMP],
                data_entry[DocumentLabels.TIMESTAMP],
            )

            if time_difference < self.LOAD_AGGREGATION_INTERVAL:
                bulk_entries.append(data_entry)
                continue

            output_steps: Steps = self._parse_data_to_entry(bulk_entries)
            to_be_committed_entries.append(output_steps.model_dump_json(by_alias=True))
            del bulk_entries[:]
            cached_entry = data_entry
            bulk_entries.append(cached_entry)
            self._commit_if_limit_reached(to_be_committed_entries)
        if bulk_entries:
            output_steps: Steps = self._parse_data_to_entry(bulk_entries)
            to_be_committed_entries.append(output_steps.model_dump_json(by_alias=True))
        self._commit(entries=to_be_committed_entries)

    def _parse_data_to_entry(self, entries: List[Dict[str, Any]]) -> Steps:
        step_details, total_steps = self._parse_steps_detail(entries)
        timestamp = step_details[0].timestamp
        end_time = step_details[-1].end_time
        if end_time.time() == time(hour=0, minute=0, second=0):
            end_time += timedelta(days=1)

        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            organization=Organization.GOOGLE,
            data_integrity=DataIntegrity.MEDIUM,
            service=Service.GOOGLE_FIT,
        )

        return Steps(
            timestamp=timestamp,
            end_time=end_time,
            duration=int((end_time - timestamp).total_seconds()),
            steps=total_steps,
            step_details=step_details,
            metadata=metadata,
        )

    def _parse_steps_detail(self, entries: List[Dict[str, Any]]) -> Tuple[List[StepsDetail], int]:
        result: List[StepsDetail] = []
        for entry in entries:
            timestamp: datetime = entry.get(DocumentLabels.TIMESTAMP)
            end_time: datetime = entry.get(DocumentLabels.END_TIME)
            steps = entry.get(KEY_STEP_COUNT)
            steps = int(steps) if steps else None

            if not steps:
                logging.warning(f"_parse_steps_detail() skipping entry. Steps are None for entry:{entry}")
                continue

            duration = end_time - timestamp
            if duration < timedelta():
                # check whether its midnight
                if end_time.hour == 0 and end_time.date() == timestamp.date():
                    end_time += timedelta(days=1)
                    duration = end_time - timestamp

            result.append(
                StepsDetail(
                    steps=steps,
                    timestamp=timestamp,
                    end_time=end_time,
                    duration=int(duration.total_seconds()),
                )
            )

        return result, sum(v.steps for v in result)
