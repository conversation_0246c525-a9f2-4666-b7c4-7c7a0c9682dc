# -*- coding: utf-8 -*-
import json
import logging
import os
from uuid import UUI<PERSON>
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.facebook_loaders.facebook_constants import (
    FACEBOOK_INFORMATION_USED_FOR_RECOMMENDATIONS_PATH,
)
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD


class InformationForRecommendationLoader(FileLoaderBase):
    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_paths = []
        self.file_names = [
            "facebook_watch_topics_for_recommendations.json",
            "news_feed_topics_for_recommendations.json",
            "news_topics_for_recommendations.json",
        ]
        self.search_path = os.path.join(data_dir_path, FACEBOOK_INFORMATION_USED_FOR_RECOMMENDATIONS_PATH)

    def load_files_data(self) -> None:
        for filename in self.file_names:
            self.file_paths.append(os.path.join(self.search_path, filename))

        return self.process_data()

    def process_data(self) -> None:
        entries = []
        for file_path in self.file_paths:
            try:
                with open(file_path, encoding="utf8") as json_file:
                    data = json.load(json_file)
                    data[DocumentLabels.USER_UUID] = str(self.user_uuid)
                    entries.append(json.dumps(data, default=json_serializer))
                    self._commit_if_limit_reached(entries)

            except FileNotFoundError:
                logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, file_path)

        self._commit(entries=entries)
