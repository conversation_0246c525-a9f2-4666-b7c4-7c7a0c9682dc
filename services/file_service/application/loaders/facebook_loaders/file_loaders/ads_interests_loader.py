# -*- coding: utf-8 -*-
import json
import logging
import os
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.facebook_loaders.facebook_constants import FACEBOOK_ADS_AND_BUSINESS
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD


class AdsInterestsLoader(FileLoaderBase):
    """Loader for Facebook export file"""

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_path = ""
        self.search_path = os.path.join(data_dir_path, FACEBOOK_ADS_AND_BUSINESS)

    def load_files_data(self) -> None:
        self.file_path = os.path.join(self.search_path, "ads_interests.json")
        return self.process_data()

    def process_data(self) -> None:
        return self._process_interests_files(self.file_path)

    def _process_interests_files(self, file_path: str) -> None:
        entries = []
        try:
            with open(file_path, encoding="utf8") as json_file:
                data: dict = json.load(json_file)
                data[DocumentLabels.USER_UUID] = str(self.user_uuid)

                entries.append(json.dumps(data, default=json_serializer))

            self._commit(entries=entries)

        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, self.file_path)
