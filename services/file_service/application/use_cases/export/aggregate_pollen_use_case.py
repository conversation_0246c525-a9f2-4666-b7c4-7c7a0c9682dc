import logging
from dataclasses import dataclass
from typing import List, Optional, Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import AsyncOpenSearch
from pydantic import Field

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.boundaries.space_time_coordinates import SpaceTimeCoordinates
from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.constants.document_labels import (
    DocumentLabels,
)
from services.base.domain.schemas.pollen import Pollen, PollenFields
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimeIntervalModel
from services.base.infrastructure.database.opensearch.query_methods.utils import get_fields_and_aggregator_as_tuple
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client
from services.file_service.application.use_cases.export.search_environment_use_case import SearchEnvironmentUseCase


@dataclass(frozen=True)
class AggregationPollenFields:
    field_tree_count = f"{PollenFields.TREE}.{PollenFields.SPECIES_COUNT}"
    field_weed_count = f"{PollenFields.WEED}.{PollenFields.SPECIES_COUNT}"
    field_grass_count = f"{PollenFields.GRASS}.{PollenFields.SPECIES_COUNT}"
    field_coordinates = f"{PollenFields.COORDINATES}"

    request_fields = get_fields_and_aggregator_as_tuple(
        [
            field_tree_count,
            field_grass_count,
            field_weed_count,
            (field_coordinates, "geo_centroid"),
        ]
    )


class AggregatePollenUseCaseOutputBoundaryItem(TimeIntervalModel):
    weed: Optional[RoundedFloat] = Field(alias=PollenFields.WEED)
    grass: Optional[RoundedFloat] = Field(alias=PollenFields.GRASS)
    tree: Optional[RoundedFloat] = Field(alias=PollenFields.TREE)
    coordinates: Optional[CoordinatesModel] = Field(alias=DocumentLabels.COORDINATES)
    user_coordinates: CoordinatesModel = Field(alias=DocumentLabels.USER_COORDINATES)


class AggregatePollenUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[AggregatePollenUseCaseOutputBoundaryItem]


class AggregatePollenUseCase(AsyncUseCaseBase):
    async def execute_async(
        self,
        user_id: UUID,
        space_time_input: Sequence[SpaceTimeCoordinates],
        timezone: ZoneInfo,
        aggregation_interval: str,
        client: AsyncOpenSearch = get_async_default_os_client(),
    ) -> AggregatePollenUseCaseOutputBoundary:
        space_time_and_results = await SearchEnvironmentUseCase.execute_async(
            client=client,
            domain_type=Pollen,
            requested_fields=AggregationPollenFields.request_fields,
            space_time_data=space_time_input,
            aggregation_interval=aggregation_interval,
            timezone=timezone,
        )
        results: List[AggregatePollenUseCaseOutputBoundaryItem] = []
        for space_time, result in space_time_and_results:
            try:
                raw_data = result[0]
                data = AggregatePollenUseCaseOutputBoundaryItem(
                    timestamp=raw_data[DocumentLabels.TIMESTAMP],
                    duration=space_time.duration,
                    end_time=space_time.end_time,
                    coordinates=raw_data[AggregationPollenFields.field_coordinates],
                    tree=raw_data.get(AggregationPollenFields.field_tree_count),
                    weed=raw_data.get(AggregationPollenFields.field_weed_count),
                    grass=raw_data.get(AggregationPollenFields.field_grass_count),
                    user_coordinates=CoordinatesModel(latitude=space_time.latitude, longitude=space_time.longitude),
                )
                results.append(data)
            except Exception as error:
                logging.error(f"cannot validate aggregated pollen {result}, error={error}")

        if not results:
            logging.info(f"Found no pollen documents for: {space_time_input}")

        return AggregatePollenUseCaseOutputBoundary(results=results)
