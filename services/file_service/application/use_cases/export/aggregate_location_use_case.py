import logging
from datetime import datetime
from typing import List
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import AsyncOpenSearch

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.utils.time import TimeUtils
from services.base.domain.constants.document_labels import (
    DocumentLabels,
)
from services.base.domain.schemas.location import Location, LocationFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimeIntervalModel
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_time_aggregated_fields_async,
)
from services.base.infrastructure.database.opensearch.query_methods.results_filters import (
    filter_results_from_aggregation,
)
from services.base.infrastructure.database.opensearch.query_methods.utils import get_fields_and_aggregator_as_tuple
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client


class AggregateLocationUseCaseOutputBoundaryItem(TimeIntervalModel):
    coordinates: CoordinatesModel


class AggregateLocationUseCaseOutputBoundary(BaseDataModel):
    results: List[AggregateLocationUseCaseOutputBoundaryItem]


class AggregateLocationUseCase(AsyncUseCaseBase):
    async def execute_async(
        self,
        user_id: UUID,
        aggregation_interval: str,
        timezone: ZoneInfo,
        time_gte: datetime | None = None,
        time_lte: datetime | None = None,
        client: AsyncOpenSearch = get_async_default_os_client(),
    ) -> AggregateLocationUseCaseOutputBoundary:
        requested_fields = get_fields_and_aggregator_as_tuple([(LocationFields.AVERAGE_COORDINATES, "geo_centroid")])

        boolean_query_builder = BooleanQueryBuilder()
        boolean_query_builder.add_query(CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_id))
        if any([time_gte, time_lte]):
            boolean_query_builder.add_query(CommonLeafQueries.timestamp_range_query(lte=time_lte, gte=time_gte))

        query = Query(type_queries=[TypeQuery(domain_types=[Location], query=boolean_query_builder.build_and_query())])

        results = await get_time_aggregated_fields_async(
            requested_fields_and_agg=requested_fields,
            interval=aggregation_interval,
            timezone=timezone,
            time_gte=time_gte,
            time_lte=time_lte,
            query=query,
        )

        filtered_results = filter_results_from_aggregation(
            in_results=results, requested_fields_and_agg=requested_fields
        )
        output_results = []
        for result in filtered_results:
            try:
                latitude = result.get(LocationFields.AVERAGE_COORDINATES, {}).get(DocumentLabels.LATITUDE, None)
                longitude = result.get(LocationFields.AVERAGE_COORDINATES, {}).get(DocumentLabels.LONGITUDE, None)
                if not (latitude and longitude):
                    continue
                coordinates = CoordinatesModel(
                    latitude=latitude,
                    longitude=longitude,
                )

                timestamp = datetime.fromisoformat(result[DocumentLabels.TIMESTAMP])
                timedelta = TimeUtils.get_relativedelta_from_aggregation_interval(
                    aggregation_interval=aggregation_interval
                )
                end_time = timestamp + timedelta
                duration = int((end_time - timestamp).total_seconds())
                output_results.append(
                    AggregateLocationUseCaseOutputBoundaryItem(
                        coordinates=coordinates,
                        timestamp=timestamp,
                        end_time=end_time,
                        duration=duration,
                    )
                )
            except Exception as error:
                logging.exception(f"Can't process location entry: {result}, err: {error}")
        return AggregateLocationUseCaseOutputBoundary(results=output_results)
