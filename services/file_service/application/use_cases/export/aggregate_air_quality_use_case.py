import logging
from dataclasses import dataclass
from typing import List, Optional, Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import AsyncOpenSearch
from pydantic import Field

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.boundaries.space_time_coordinates import SpaceTimeCoordinates
from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.constants.document_labels import (
    DocumentLabels,
)
from services.base.domain.schemas.air_quality import AirQuality, AirQualityFields
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimeIntervalModel
from services.base.infrastructure.database.opensearch.query_methods.utils import get_fields_and_aggregator_as_tuple
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client
from services.file_service.application.use_cases.export.search_environment_use_case import SearchEnvironmentUseCase


@dataclass(frozen=True)
class AggregationAirQualityFields:
    field_pm10 = f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.PM10}"
    field_pm25 = f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.PM25}"
    field_co = f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.CO}"
    field_no2 = f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.NO2}"
    field_o3 = f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.O3}"
    field_so2 = f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.SO2}"
    field_aqi_eu = f"{AirQualityFields.AQI}.{AirQualityFields.EU}"
    field_aqi_gb = f"{AirQualityFields.AQI}.{AirQualityFields.GB}"
    field_aqi_us = f"{AirQualityFields.AQI}.{AirQualityFields.US}"
    field_coordinates = f"{AirQualityFields.COORDINATES}"

    request_fields = get_fields_and_aggregator_as_tuple(
        [
            field_pm10,
            field_pm25,
            field_co,
            field_no2,
            field_o3,
            field_so2,
            field_aqi_eu,
            field_aqi_gb,
            field_aqi_us,
            (field_coordinates, "geo_centroid"),
        ]
    )


class AggregateAirQualityUseCaseOutputBoundaryItem(TimeIntervalModel):
    pm10: Optional[RoundedFloat] = Field(alias=AirQualityFields.PM10)
    pm25: Optional[RoundedFloat] = Field(alias=AirQualityFields.PM25)
    no2: Optional[RoundedFloat] = Field(alias=AirQualityFields.NO2)
    so2: Optional[RoundedFloat] = Field(alias=AirQualityFields.SO2)
    co: Optional[RoundedFloat] = Field(alias=AirQualityFields.CO)
    o3: Optional[RoundedFloat] = Field(alias=AirQualityFields.O3)
    aqi_eu: Optional[RoundedFloat] = Field(alias=AggregationAirQualityFields.field_aqi_eu)
    aqi_us: Optional[RoundedFloat] = Field(alias=AggregationAirQualityFields.field_aqi_us)
    aqi_gb: Optional[RoundedFloat] = Field(alias=AggregationAirQualityFields.field_aqi_gb)
    coordinates: CoordinatesModel = Field(alias=DocumentLabels.COORDINATES)
    user_coordinates: CoordinatesModel = Field(alias=DocumentLabels.USER_COORDINATES)


class AggregateAirQualityUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[AggregateAirQualityUseCaseOutputBoundaryItem]


class AggregateAirQualityUseCase(AsyncUseCaseBase):
    async def execute_async(
        self,
        user_id: UUID,
        space_time_input: Sequence[SpaceTimeCoordinates],
        timezone: ZoneInfo,
        aggregation_interval: str,
        client: AsyncOpenSearch = get_async_default_os_client(),
    ) -> AggregateAirQualityUseCaseOutputBoundary:
        space_time_and_results = await SearchEnvironmentUseCase.execute_async(
            client=client,
            domain_type=AirQuality,
            requested_fields=AggregationAirQualityFields.request_fields,
            space_time_data=space_time_input,
            aggregation_interval=aggregation_interval,
            timezone=timezone,
        )
        results: List[AggregateAirQualityUseCaseOutputBoundaryItem] = []
        for space_time, result in space_time_and_results:
            try:
                raw_data = result[0]
                data = AggregateAirQualityUseCaseOutputBoundaryItem(
                    timestamp=raw_data[DocumentLabels.TIMESTAMP],
                    duration=space_time.duration,
                    end_time=space_time.end_time,
                    pm10=raw_data.get(AggregationAirQualityFields.field_pm10),
                    pm25=raw_data.get(AggregationAirQualityFields.field_pm25),
                    co=raw_data.get(AggregationAirQualityFields.field_co),
                    no2=raw_data.get(AggregationAirQualityFields.field_no2),
                    so2=raw_data.get(AggregationAirQualityFields.field_so2),
                    o3=raw_data.get(AggregationAirQualityFields.field_o3),
                    aqi_eu=raw_data.get(AggregationAirQualityFields.field_aqi_eu),
                    aqi_gb=raw_data.get(AggregationAirQualityFields.field_aqi_gb),
                    aqi_us=raw_data.get(AggregationAirQualityFields.field_aqi_us),
                    coordinates=raw_data[AggregationAirQualityFields.field_coordinates],
                    user_coordinates=CoordinatesModel(latitude=space_time.latitude, longitude=space_time.longitude),
                )
                results.append(data)
            except Exception as error:
                logging.error(f"cannot validate aggregated air quality {result}, error={error}")

        if not results:
            logging.info(f"Found no air quality documents for: {space_time_input}")

        return AggregateAirQualityUseCaseOutputBoundary(results=results)
