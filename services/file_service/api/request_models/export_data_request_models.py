from enum import StrEnum
from typing import List

from fastapi import Depends, Query
from pydantic import Field

from services.base.application.database.models.filter_types import TimestampRangeFilter
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.shared import BaseDataModel
from services.file_service.api.utils.get_range_input import get_date_range_input
from services.file_service.application.enums.exportable_data_type import ExportableType


class EnvironmentAggregationInterval(StrEnum):
    ONE_HOUR = "1h"
    FOUR_HOUR = "4h"
    ONE_DAY = "1d"


class DataExportRequestInput(BaseDataModel):
    range_filter: TimestampRangeFilter | None = None
    organizations: List[Organization] = Field(min_length=1)
    data_types: List[ExportableType] = Field(min_length=1)
    export_csv: bool = Field(description="Defines if export creates csv files along JSON output")
    environment_aggregation_interval: EnvironmentAggregationInterval = Field(
        description="Defines the aggregation interval used for environment queries"
    )


def get_data_export_request_input(
    range_filter: TimestampRangeFilter = Depends(get_date_range_input),
    organizations: List[Organization] = Query(default=[organization for organization in Organization], min_length=1),
    data_types: List[ExportableType] = Query(
        default=[exportable_type for exportable_type in ExportableType], min_length=1
    ),
    export_csv: bool = Query(default=True),
    environment_aggregation_interval: EnvironmentAggregationInterval = Query(
        default=EnvironmentAggregationInterval.ONE_HOUR
    ),
) -> DataExportRequestInput:
    return DataExportRequestInput(
        range_filter=range_filter,
        organizations=organizations,
        data_types=data_types,
        export_csv=export_csv,
        environment_aggregation_interval=environment_aggregation_interval,
    )
