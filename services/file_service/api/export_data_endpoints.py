from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.message_response import SingleMessageResponse
from services.base.domain.annotated_types import AssetId
from services.file_service.api.constants import DataExportServicePrefixes, ExportDataEndpointRoutes
from services.file_service.api.request_models.export_data_request_models import (
    DataExportRequestInput,
    get_data_export_request_input,
)
from services.file_service.api.response_models.fetch_export_url_response import FetchExportUrlResponse
from services.file_service.application.use_cases.export.fetch_export_url_use_case import FetchExportUrlUseCase
from services.file_service.application.use_cases.export.schedule_data_export_use_case import ScheduleDataExportUseCase

export_data_router = APIRouter(
    prefix=f"{DataExportServicePrefixes.VERSION1_PREFIX}{DataExportServicePrefixes.DATA_EXPORT_PREFIX}",
    tags=["export"],
    responses={404: {"description": "Not found"}},
)


@export_data_router.post(ExportDataEndpointRoutes.SCHEDULE_DATA_EXPORT, response_model=SingleMessageResponse)
async def schedule_data_export_endpoint(
    current_uuid: UUID = Depends(get_current_uuid),
    schedule_use_case: ScheduleDataExportUseCase = Injected(ScheduleDataExportUseCase),
    request_input: DataExportRequestInput = Depends(get_data_export_request_input),
):
    """Exports user data to object storage. Returns task id that tracks progress of the export workflow"""
    export_name = await schedule_use_case.execute_async(
        data_types=request_input.data_types,
        user_id=current_uuid,
        range_filter=request_input.range_filter,
        organizations=request_input.organizations,
        export_csv=request_input.export_csv,
        environment_aggregation_interval=request_input.environment_aggregation_interval,
        system_run=False,
    )

    return SingleMessageResponse(message=str(export_name))


@export_data_router.get(ExportDataEndpointRoutes.DATA_EXPORT_URL)
async def fetch_asset_url(
    user_uuid: UUID = Depends(get_current_uuid),
    export_ids: list[AssetId] = Query(...),
    fetch_assets_use_case: FetchExportUrlUseCase = Injected(FetchExportUrlUseCase),
) -> FetchExportUrlResponse:
    output = await fetch_assets_use_case.execute_async(user_uuid=user_uuid, asset_ids=export_ids)
    return FetchExportUrlResponse.map(model=output)
