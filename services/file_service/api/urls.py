from dataclasses import dataclass

from services.file_service.api.constants import ExportDataEndpointRoutes, FileEndpointRoutes
from services.file_service.api.export_data_endpoints import export_data_router
from services.file_service.api.file_endpoints import file_router


@dataclass(frozen=True)
class FileEndpointUrls:
    DELETE_ALL_DATA = f"{file_router.prefix}{FileEndpointRoutes.DELETE_ALL_DATA}"


@dataclass(frozen=True)
class DataExportEndpointUrls:
    SCHEDULE = f"{export_data_router.prefix}{ExportDataEndpointRoutes.SCHEDULE_DATA_EXPORT}"
    URL = f"{export_data_router.prefix}{ExportDataEndpointRoutes.DATA_EXPORT_URL}"
