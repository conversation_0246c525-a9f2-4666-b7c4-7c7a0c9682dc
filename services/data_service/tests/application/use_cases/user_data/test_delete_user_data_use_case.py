import asyncio
from typing import Sequence, cast
from uuid import UUID

import pytest

from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.data_service.application.use_cases.user_data.delete_user_data_use_case import DeleteUserDataUseCase


class TestDeleteUserDataUseCase:
    @pytest.fixture(scope="class")
    async def user(self) -> MemberUser:
        return MemberUserBuilder().with_uuid(UUID("e437adcc-8687-4fcd-9362-ee5842ce8d66")).build()

    @pytest.fixture
    async def events_v3(self, event_repo: EventRepository, user: MemberUser) -> Sequence[Event]:
        events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_all(n=50)
        return await event_repo.insert(events=events, force_strong_consistency=True)

    @pytest.fixture
    async def event_templates(self, template_repo: TemplateRepository, user: MemberUser) -> Sequence[EventTemplate]:
        templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_all(n=10)
        return cast(
            Sequence[EventTemplate], await template_repo.insert(templates=templates, force_strong_consistency=True)
        )

    async def test_delete_user_data_deletes_all_v3_types(
        self,
        user: MemberUser,
        events_v3: Sequence[Event],
        event_templates: Sequence[EventTemplate],
        event_repo: EventRepository,
        template_repo: TemplateRepository,
        delete_user_data_use_case: DeleteUserDataUseCase,
    ):

        # Assert data exists
        assert len(events_v3) == len(await event_repo.search_by_id(ids_and_types=[(e.id, type(e)) for e in events_v3]))
        assert len(event_templates) == len(await template_repo.search_by_id(ids=[t.id for t in event_templates]))

        await delete_user_data_use_case.execute_async(user_uuid=user.user_uuid)

        # Prevent race conditions for the usecase to finish executing,
        # alternatively add a flag to the usecase, which we would not expose to the API, to wait for the execution
        await asyncio.sleep(2)

        # Assert data is gone
        assert not await event_repo.search_by_id(ids_and_types=[(e.id, type(e)) for e in events_v3])
        assert not await template_repo.search_by_id(ids=[t.id for t in event_templates])
