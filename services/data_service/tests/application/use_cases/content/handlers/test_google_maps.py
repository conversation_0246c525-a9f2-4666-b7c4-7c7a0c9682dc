import pytest
from httpx import AsyncClient

from services.base.application.exceptions import NoContentException
from services.data_service.application.use_cases.content.handlers.google_maps import GoogleMapsHandler


class TestGoogleMapsHandler:
    @pytest.mark.parametrize(
        ("url", "expected"),
        [
            # Valid URLs - Standard Cases
            ("https://maps.app.goo.gl/abc123", True),
            ("https://maps.google.com/place/Location", True),
            ("https://www.google.com/maps/place/Location", True),
            ("https://google.com/maps/place/Location", True),
            # Valid URLs - Edge Cases
            ("https://maps.google.com/place/Location/", True),  # Trailing slash
            ("https://www.maps.google.com/place/Location", True),  # www subdomain
            ("https://MAPS.Google.COM/place/Location", True),  # Mixed case
            ("https://maps.google.com/Place/Location", False),  # Case sensitive path
            # Valid URLs - Query Parameters
            ("https://maps.google.com/place/Location?param=value", True),
            ("https://maps.google.com/place/Location#fragment", True),
            # Invalid URLs - Missing or Wrong Segments
            ("https://maps.google.com/maps", False),
            ("https://maps.google.com/maps/", False),
            ("https://google.com/search", False),
            ("https://maps.google.com/places/Location", False),  # Wrong segment
            # Invalid URLs - Wrong Domains
            ("https://example.com/maps", False),
            ("https://maps.example.com/place", False),
            ("https://fake.maps.google.com/place/Location", False),
            ("https://google.maps.com/place/Location", False),
        ],
    )
    async def test_can_handle(self, url: str, expected: bool):
        """Test URL validation for different Google Maps formats."""
        handler = GoogleMapsHandler(client=AsyncClient())
        result = await handler.can_handle(url)
        assert result == expected

    @pytest.mark.parametrize(
        ("url", "expected_location"),
        [
            # Basic location names
            (
                "https://www.google.com/maps/place/Central+Park/@40.7825547,-73.9655834,1570m/data=!3m2!1e3!4b1!4m6!3m5!1s0x89c2589a018531e3:0xb9df1f7387a94119!8m2!3d40.7825547!4d-73.9655834!16zL20vMDljN3Y?entry=ttu",
                "Central Park",
            ),
            # With coordinates and zoom levels
            (
                "https://www.google.com/maps/place/Times+Square/@40.757602,-73.9856617,51a,35y,39.6t/data=!3m1!1e3!4m6!3m5!1s0x89c25855c6480299:0x55194ec5a1ae072e!8m2!3d40.7579747!4d-73.9855426!16zL20vMDdxZHI",
                "Times Square",
            ),
            # With special characters
            (
                "https://www.google.com/maps/place/Caf%C3%A9+du+Monde/@29.9573771,-90.0614167,17z/data=!3m1!4b1",
                "Café du Monde",
            ),
        ],
    )
    def test_extract_location_from_url(self, url: str, expected_location: str):
        """Test location extraction from various URL formats."""
        handler = GoogleMapsHandler(client=AsyncClient())
        result = handler._extract_location_from_url(url)
        assert result == expected_location

    @pytest.mark.parametrize(
        ("url", "error_message"),
        [
            ("https://maps.google.com/search/restaurant", "Could not extract location from URL"),
            ("https://maps.google.com/place/", "Empty location name"),
            ("https://maps.google.com/place/@40.7829,-73.9654/data=!3m1!4b1", "URL contains only coordinates"),
            ("https://maps.google.com/place//", "Empty location name"),
            ("https://maps.google.com/place/%20", "Empty location name"),
        ],
    )
    def test_extract_location_from_invalid_url_raises_exception(self, url: str, error_message: str):
        """Test that invalid URLs raise appropriate exceptions with correct messages."""
        handler = GoogleMapsHandler(client=AsyncClient())
        with pytest.raises(NoContentException, match=error_message):
            handler._extract_location_from_url(url)
