from datetime import datetime, timezone

import pytest

from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.data_service.application.use_cases.event_correlation.event_correlation_schemas import (
    CorrelationVariableDocuments,
    SingleCorrelationVariableDocument,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    CorrelationTemporalOptions,
)


class TestGetPairs:

    def test_get_pairs_before(self, event_correlation_use_case):
        # Test case for 'before' temporal type
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc), value=11.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc), value=12.0
                ),
            ],
        )
        independent_docs = CorrelationVariableDocuments(
            field_name="indep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 9, 30, 0, tzinfo=timezone.utc), value=20.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 30, 0, tzinfo=timezone.utc), value=21.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc), value=22.0
                ),
            ],
        )
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="before",
        )
        expected_result = [(10.0, [20.0]), (11.0, [21.0]), (12.0, [22.0])]
        result = event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)
        assert result == expected_result

    def test_get_pairs_before_multiple_matches(self, event_correlation_use_case):
        # Test case for 'before' temporal type and multiple matches
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc), value=11.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc), value=12.0
                ),
            ],
        )
        independent_docs = CorrelationVariableDocuments(
            field_name="indep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 9, 30, 0, tzinfo=timezone.utc), value=20.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 9, 40, 0, tzinfo=timezone.utc), value=10.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 9, 50, 0, tzinfo=timezone.utc), value=15.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 30, 0, tzinfo=timezone.utc), value=21.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc), value=22.0
                ),
            ],
        )
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="before",
        )
        expected_result = [(10.0, [20.0, 10.0, 15.0]), (11.0, [21.0]), (12.0, [22.0])]
        result = event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)
        assert result == expected_result

    def test_get_pairs_after(self, event_correlation_use_case):
        # Test case for 'after' temporal type
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc), value=11.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc), value=12.0
                ),
            ],
        )
        independent_docs = CorrelationVariableDocuments(
            field_name="indep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 9, 30, 0, tzinfo=timezone.utc), value=20.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 30, 0, tzinfo=timezone.utc), value=21.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc), value=22.0
                ),
            ],
        )
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="after",
        )
        expected_result = [(10.0, [21.0]), (11.0, [22.0]), (12.0, [])]
        result = event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)
        assert result == expected_result

    def test_get_pairs_closest(self, event_correlation_use_case):
        # Test case for 'closest' temporal type
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc), value=11.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc), value=12.0
                ),
            ],
        )
        independent_docs = CorrelationVariableDocuments(
            field_name="indep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 9, 30, 0, tzinfo=timezone.utc), value=20.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 20, 0, tzinfo=timezone.utc), value=21.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc), value=22.0
                ),
            ],
        )
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="closest",
        )
        expected_result = [(10.0, [21.0]), (11.0, [22.0]), (12.0, [22.0])]
        result = event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)
        assert result == expected_result

    def test_get_pairs_empty_independent(self, event_correlation_use_case):
        # Test case where independent_docs is empty
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                ),
            ],
        )
        independent_docs = CorrelationVariableDocuments(field_name="indep", values=[])
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="before",
        )
        with pytest.raises(ValueError):
            event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)

    def test_get_pairs_different_intervals(self, event_correlation_use_case):
        # Test with a different time interval (e.g., 1 day)
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 3, 10, 0, 0, tzinfo=timezone.utc), value=11.0
                ),
            ],
        )
        independent_docs = CorrelationVariableDocuments(
            field_name="indep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc), value=20.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc), value=21.0
                ),
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 3, 12, 0, 0, tzinfo=timezone.utc), value=22.0
                ),
            ],
        )
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="after",
        )
        expected_result = [(10.0, [21.0]), (11.0, [])]
        result = event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)
        assert result == expected_result

    def test_get_pairs_no_match(self, event_correlation_use_case):
        dependent_docs = CorrelationVariableDocuments(
            field_name="dep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc), value=10.0
                )
            ],
        )
        independent_docs = CorrelationVariableDocuments(
            field_name="indep",
            values=[
                SingleCorrelationVariableDocument(
                    timestamp=datetime(2024, 1, 2, 12, 0, 0, tzinfo=timezone.utc), value=20.0
                )
            ],
        )
        temporal_options = CorrelationTemporalOptions(
            time_input=TimeInput(
                interval="1h",
                time_gte=datetime(2024, 1, 2, 10, 0, 0, tzinfo=timezone.utc),
                time_lte=datetime(2024, 1, 2, 11, 30, 0, tzinfo=timezone.utc),
            ),
            type="before",
        )
        with pytest.raises(NoContentException):
            event_correlation_use_case._get_pairs(dependent_docs, independent_docs, temporal_options)
