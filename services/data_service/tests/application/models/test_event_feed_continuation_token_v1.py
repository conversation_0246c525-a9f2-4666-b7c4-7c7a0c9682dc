from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.location import Location, LocationFields
from services.base.domain.schemas.query.boolean_query import And<PERSON><PERSON><PERSON>, NotQ<PERSON><PERSON>, OrQuery
from services.base.domain.schemas.query.leaf_query import (
    Exists<PERSON><PERSON>y,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Radius<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>uery,
    ValuesQuery,
)
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.data_service.application.models.event_feed_continuation_token import EventFeedContinuationToken
from services.data_service.v1.api.continuation_token_marshaller import ContinuationTokenMarshaller


class TestEventFeedContinuationToken:
    def test_leaf_queries_are_marshalled_correctly(self):
        values_query = ValuesQuery(field_name=LocationFields.TIMESTAMP, values=["test"])
        multi_match_query = PatternQuery(field_names=[LocationFields.TIMESTAMP], pattern="test")
        range_query = RangeQuery(field_name=DocumentLabels.TIMESTAMP, lte=5)
        radius_query = RadiusQuery(
            field_name=LocationFields.AVERAGE_COORDINATES, radius="10km", latitude=10, longitude=10
        )
        exists_query = ExistsQuery(field_name=LocationFields.WAYPOINT_DETAILS)
        and_query = AndQuery(queries=[values_query, multi_match_query, radius_query, exists_query, range_query])
        query = Query(type_queries=[TypeQuery(domain_types=[Location], query=and_query)])

        token = EventFeedContinuationToken(token=PrimitiveTypesGenerator.generate_random_string(), query=query)

        # Act
        encoded_token = ContinuationTokenMarshaller.encode_event_feed_continuation_token(token)
        decoded_token = ContinuationTokenMarshaller.decode_event_feed_continuation_token(encoded_token)

        assert decoded_token == token

    def test_boolean_queries_are_marshalled_correctly(self):
        values_query = ValuesQuery(field_name=LocationFields.TIMESTAMP, values=["test"])
        and_query = AndQuery(queries=[values_query])
        or_query = OrQuery(queries=[and_query])
        not_query = NotQuery(queries=[or_query])
        query = Query(type_queries=[TypeQuery(domain_types=[Location], query=not_query)])

        token = EventFeedContinuationToken(token=PrimitiveTypesGenerator.generate_random_string(), query=query)

        encoded_token = ContinuationTokenMarshaller.encode_event_feed_continuation_token(token)
        decoded_token = ContinuationTokenMarshaller.decode_event_feed_continuation_token(encoded_token)

        assert decoded_token == token
