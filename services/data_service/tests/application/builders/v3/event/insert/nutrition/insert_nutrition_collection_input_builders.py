from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory, DrinkIdentifier
from services.base.domain.schemas.events.nutrition.food import FoodCategory, FoodIdentifier
from services.base.domain.schemas.events.nutrition.supplement import SupplementCategory, SupplementIdentifier
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import (
    InsertDrinkInput,
    InsertFoodInput,
    InsertSupplementInput,
)
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertDrinkInputBuilder(InsertEventBuilderBase, DrinkIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertDrinkInput:
        return InsertDrinkInput(
            type=DataType.Drink,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=DrinkCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10, allow_none=True),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            template_id=self._template_id,
            assets=EventInputAssetBuilder().build_n(),
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=VolumeUnit),
            consumed_amount=100,
            calories=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=1000, allow_none=True),
            brand=None,
            flavor=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
        )


class InsertFoodInputBuilder(InsertEventBuilderBase, FoodIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertFoodInput:
        return InsertFoodInput(
            type=DataType.Food,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=FoodCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10, allow_none=True),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            template_id=self._template_id,
            assets=EventInputAssetBuilder().build_n(),
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            consumed_amount=100,
            calories=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=1000, allow_none=True),
            brand=None,
            flavor=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
        )


class InsertSupplementInputBuilder(InsertEventBuilderBase, SupplementIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertSupplementInput:
        return InsertSupplementInput(
            type=DataType.Supplement,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=SupplementCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10, allow_none=True),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            template_id=self._template_id,
            assets=EventInputAssetBuilder().build_n(),
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            consumed_amount=100,
            calories=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=1000, allow_none=True),
            brand=None,
            flavor=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
        )
