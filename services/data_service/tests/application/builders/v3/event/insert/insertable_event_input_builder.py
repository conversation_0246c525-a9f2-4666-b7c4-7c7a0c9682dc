from __future__ import annotations

from datetime import datetime
from typing import TYPE_CHECKING, Self, Sequence
from uuid import UUID

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator

if TYPE_CHECKING:
    from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs


class InsertableEventInputBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None
        self._template_id: UUID | None = None
        self._type_id: str | None = None
        self._build_groups: bool = True

    def build(self) -> InsertEventInputs:
        from services.data_service.type_resolver import TypeResolver

        type_id = self._type_id or PrimitiveTypesGenerator.get_random_type_id()
        builder = TypeResolver.get_insert_event_input_builder(type_id=type_id)
        return (
            builder()
            .with_timestamp(timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime())
            .with_end_time(self._end_time)
            .with_template_id(self._template_id)
            .build()
        )

    def build_all(self, n: int | None = None) -> Sequence[InsertEventInputs]:
        from services.data_service.type_resolver import TypeResolver

        out = []
        for builder_type in TypeResolver.INSERT_EVENT_INPUT_BUILDERS:
            if not self._build_groups and builder_type in TypeResolver.INSERT_GROUP_INPUT_BUILDERS:
                continue
            out.extend(
                [
                    builder_type()
                    .with_timestamp(self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime())
                    .with_end_time(self._end_time)
                    .with_template_id(self._template_id)
                    .build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=2))
                ]
            )
        return out

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_template_id(self, template_id: UUID | None) -> Self:
        self._template_id = template_id
        return self

    def with_type_id(self, type_id: str) -> Self:
        self._type_id = type_id
        return self

    def should_build_groups(self, should_build: bool) -> Self:
        self._build_groups = should_build
        return self
