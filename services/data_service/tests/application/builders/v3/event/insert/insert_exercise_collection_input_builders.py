from datetime import timedelta

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthIdentifier
from services.data_service.application.use_cases.events.models.exercise.insert_exercise_inputs import (
    InsertCardioInput,
    InsertExerciseInput,
    InsertStrengthInput,
)
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertExerciseInputBuilder(InsertEventBuilderBase, ExerciseIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertExerciseInput:
        return InsertExerciseInput(
            type=DataType.Exercise,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ExerciseCategory),
            template_id=self._template_id,
        )

    def build_series(self, n: int | None = None, max_time_delta: timedelta | None = None) -> list[InsertExerciseInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]


class InsertCardioInputBuilder(InsertEventBuilderBase, CardioIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertCardioInput:
        return InsertCardioInput(
            type=DataType.Cardio,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            distance=PrimitiveTypesGenerator.generate_random_float(max_value=100_000),
            elevation=PrimitiveTypesGenerator.generate_random_float(min_value=-500, max_value=8848),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=CardioCategory),
            template_id=self._template_id,
        )

    def build_series(self, n: int | None = None, max_time_delta: timedelta | None = None) -> list[InsertCardioInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]


class InsertStrengthInputBuilder(InsertEventBuilderBase, StrengthIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertStrengthInput:
        return InsertStrengthInput(
            type=DataType.Strength,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            count=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=100),
            weight=PrimitiveTypesGenerator.generate_random_float(max_value=500),
            rating=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=StrengthCategory),
            template_id=self._template_id,
        )

    def build_series(self, n: int | None = None, max_time_delta: timedelta | None = None) -> list[InsertStrengthInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
