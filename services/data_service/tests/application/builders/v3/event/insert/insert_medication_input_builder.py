from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.medication.medication import (
    Administration,
    MedicationCategory,
    MedicationDetails,
    MedicationIdentifier,
    SingleDoseInformation,
    WeightUnit,
)
from services.data_service.application.use_cases.events.models.insert_medication_input import InsertMedicationInput
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertMedicationInputBuilder(InsertEventBuilderBase, MedicationIdentifier):

    def __init__(self):
        super().__init__()

    def build(self) -> InsertMedicationInput:
        return InsertMedicationInput(
            type=DataType.Medication,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=MedicationCategory),
            template_id=self._template_id,
            medication_details=MedicationDetails(
                brand=PrimitiveTypesGenerator.generate_random_string(max_length=32),
                generic_name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
                rx_cuid=PrimitiveTypesGenerator.generate_random_string(max_length=32),
                administration=PrimitiveTypesGenerator.generate_random_enum(enum_type=Administration),
            ),
            single_dose_information=SingleDoseInformation(
                amount=PrimitiveTypesGenerator.generate_random_float(max_value=100),
                amount_unit=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
                items_quantity=PrimitiveTypesGenerator.generate_random_float(max_value=100),
            ),
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=100),
            consume_unit=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
        )
