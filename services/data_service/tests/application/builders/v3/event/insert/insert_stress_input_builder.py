from datetime import timedelta

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.feeling.stress import StressCategory, StressIdentifier, StressValueLimits
from services.data_service.application.use_cases.events.models.feeling.insert_feeling_inputs import InsertStressInput
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertStressInputBuilder(InsertEventBuilderBase, StressIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertStressInput:
        return InsertStressInput(
            type=DataType.Stress,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            note=PrimitiveTypesGenerator.generate_random_string(min_length=1, max_length=8),
            rating=PrimitiveTypesGenerator.generate_random_int(
                min_value=StressValueLimits.STRESS_MINIMUM_VALUE,
                max_value=StressValueLimits.STRESS_MAXIMUM_VALUE,
            ),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=StressCategory),
            template_id=self._template_id,
        )

    def build_series(self, n: int | None = None, max_time_delta: timedelta | None = None) -> list[InsertStressInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
