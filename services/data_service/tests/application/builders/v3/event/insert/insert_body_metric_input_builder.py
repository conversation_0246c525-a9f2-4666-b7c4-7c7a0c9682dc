from datetime import timedelta

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetricCategory, BodyMetricIdentifier
from services.base.domain.schemas.events.event import EventValueLimits
from services.data_service.application.use_cases.events.models.body_metric.insert_body_metric_inputs import (
    InsertBodyMetricInput,
)
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertBodyMetricInputBuilder(InsertEventBuilderBase, BodyMetricIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertBodyMetricInput:
        return InsertBodyMetricInput(
            type=DataType.BodyMetric,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            note=PrimitiveTypesGenerator.generate_random_string(max_length=EventValueLimits.MAX_NOTE_LENGTH),
            value=PrimitiveTypesGenerator.generate_random_float(),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=BodyMetricCategory),
            template_id=self._template_id,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> list[InsertBodyMetricInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
