from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.note import NoteCategory, NoteIdentifier
from services.data_service.application.use_cases.events.models.insert_note_input import InsertNoteInput
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertNoteInputBuilder(InsertEventBuilderBase, NoteIdentifier):
    def build(self) -> InsertNoteInput:
        return InsertNoteInput(
            type=DataType.Note,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            assets=EventInputAssetBuilder().build_n(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=NoteCategory),
            template_id=self._template_id,
        )

    def build_n(self, n: int | None = None) -> list[InsertNoteInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
