import random
from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.insert_group_inputs import InsertGroupInputs
from services.data_service.tests.application.builders.v3.event.insert.insertable_event_input_builder import (
    InsertableEventInputBuilder,
)
from services.data_service.type_resolver import TypeResolver


class InsertableGroupInputBuilder:
    def __init__(self):
        self._events: list[InsertEventInputs] = []

    def build(self) -> InsertGroupInputs:
        events = self._events or [
            InsertableEventInputBuilder().build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=2))
        ]
        builder = random.choice(TypeResolver.INSERT_GROUP_INPUT_BUILDERS)
        return builder().with_events(events=events).build()

    def build_all(self, n: int | None = None) -> Sequence[InsertGroupInputs]:
        out = []
        for builder_type in TypeResolver.INSERT_GROUP_INPUT_BUILDERS:
            out.extend(
                [
                    builder_type().build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=2))
                ]
            )
        return out

    def build_recursively(self, n: int | None = None) -> Sequence[InsertGroupInputs]:
        n = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=4) if n is None else n
        if n == 0:
            return self.build_all()
        else:
            return [self.with_events(self.build_recursively(n=n - 1)).build()]

    def with_events(self, events: Sequence[InsertEventInputs]) -> Self:
        self._events = events
        return self
