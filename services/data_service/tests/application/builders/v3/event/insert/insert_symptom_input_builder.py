from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.symptom import SymptomCategory, SymptomIdentifier
from services.data_service.application.use_cases.events.models.insert_symptom_input import InsertSymptomInput
from services.data_service.tests.application.builders.v3.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class InsertSymptomInputBuilder(InsertEventBuilderBase, SymptomIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertSymptomInput:
        return InsertSymptomInput(
            type=DataType.Symptom,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=SymptomCategory),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=EventInputAssetBuilder().build_n(),
            body_parts=[
                PrimitiveTypesGenerator.generate_random_enum(enum_type=BodyParts)
                for _ in range(PrimitiveTypesGenerator.generate_random_int(0, 3))
            ],
            template_id=self._template_id,
        )
