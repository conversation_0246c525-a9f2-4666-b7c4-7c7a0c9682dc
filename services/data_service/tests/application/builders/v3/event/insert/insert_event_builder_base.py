from abc import ABC, abstractmethod
from datetime import datetime
from typing import Self
from uuid import UUID

from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.data_service.application.use_cases.events.models.insert_event_input import InsertEventInput


class InsertEventBuilderBase(BuilderBase, TypeIdentifier, ABC):

    def __init__(self):
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None
        self._template_id: UUID | None = None

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_template_id(self, template_id: UUID | None) -> Self:
        self._template_id = template_id
        return self

    @abstractmethod
    def build(self) -> InsertEventInput:
        raise NotImplementedError("Each builder must implement the build method.")
