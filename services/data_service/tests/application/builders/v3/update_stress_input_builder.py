import random
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.feeling.stress import StressCategory, StressIdentifier, StressValueLimits
from services.data_service.application.use_cases.events.models.feeling.update_feeling_inputs import UpdateStressInput
from services.data_service.tests.application.builders.v3.update_event_input_builder_base import (
    UpdateEventInputBuilderBase,
)


class UpdateStressInputBuilder(UpdateEventInputBuilderBase, StressIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> UpdateStressInput:
        if not self._timestamp and random.choice([True, False]):
            time_interval = CustomModelsGenerator.generate_random_time_interval()
            self._timestamp = time_interval.timestamp
            self._end_time = time_interval.end_time

        return UpdateStressInput(
            id=self._id or uuid4(),
            type=DataType.Stress,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            rating=PrimitiveTypesGenerator.generate_random_int(
                min_value=StressValueLimits.STRESS_MINIMUM_VALUE,
                max_value=StressValueLimits.STRESS_MAXIMUM_VALUE,
            ),
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=StressCategory),
            plan_extension=None,
        )
