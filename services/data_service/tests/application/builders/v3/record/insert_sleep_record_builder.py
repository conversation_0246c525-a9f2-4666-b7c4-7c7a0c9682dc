from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.records.sleep_record import SleepRecordIdentifier
from services.data_service.application.use_cases.records.models.insert_sleep_record_input import InsertSleepRecordInput
from services.data_service.tests.application.builders.v3.record.insert_record_builder_base import (
    InsertRecordBuilderBase,
)


class InsertSleepRecordInputBuilder(InsertRecordBuilderBase, SleepRecordIdentifier):
    def __init__(self):
        super().__init__()
        self._stage: SleepStage | None = None

    def build(self) -> InsertSleepRecordInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return InsertSleepRecordInput(
            type=DataType.SleepRecord,
            timestamp=timestamp,
            end_time=self._end_time or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp),
            stage=self._stage or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepStage),
        )

    def with_stage(self, stage: SleepStage) -> Self:
        self._stage = stage
        return self

    def build_n(self, n: int | None = None) -> Sequence[InsertSleepRecordInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))]
