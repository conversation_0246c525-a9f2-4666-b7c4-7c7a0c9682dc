from typing import Callable, <PERSON><PERSON>

import pytest

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.value_limits.diary_events import DiaryEventsValueLimit
from services.base.tests.domain.builders.diary_event_builder import DiaryEventBuilder
from services.data_service.tests.api.common_calls import _delete_user_documents
from services.data_service.tests.api.utils.test_utils import TestUtils


class TestUpdateEndpoint:
    @pytest.fixture
    async def diary_event_and_user(
        self, depr_event_repository: DeprEventRepository, user_factory: Callable[[], MemberUser]
    ) -> <PERSON><PERSON>[DiaryEvents, MemberUser]:
        user: MemberUser = await user_factory()
        diary_event = DiaryEventBuilder().with_user_uuid(user.user_uuid).build()
        inserted_diary_event = (await depr_event_repository.insert(models=[diary_event]))[0]
        yield inserted_diary_event, user

        # Teardown
        await _delete_user_documents(
            user_uuid=user.user_uuid, data_schema=DiaryEvents, event_repo=depr_event_repository
        )

    async def test_update_diary_event_by_id_should_pass(self, diary_event_and_user: Tuple[DiaryEvents, MemberUser]):
        diary_event, user = diary_event_and_user
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        updated_name = PrimitiveTypesGenerator.generate_random_string(max_length=DiaryEventsValueLimit.MAX_LENGTH)
        updated_explanation = PrimitiveTypesGenerator.generate_random_string(
            max_length=DiaryEventsValueLimit.MAX_LENGTH
        )
        updated_diary_event = diary_event.model_copy(
            update={DiaryEventsFields.NAME: updated_name, DiaryEventsFields.EXPLANATION: updated_explanation}
        )

        # Act
        response = await TestUtils.call_update(
            data_type=DataType.DiaryEvents, updated_doc=updated_diary_event, headers=headers
        )

        # Assert
        response_json = response.json()
        updated_at: str = response_json[DocumentLabels.SYSTEM_PROPERTIES].pop(DocumentLabels.UPDATED_AT)
        assert TestUtils.is_date_within_one_minute(updated_at)
        assert TestUtils.to_assertable_dict(model=updated_diary_event) == response_json
