from typing import Callable

import pytest
from starlette.testclient import TestClient

from services.base.api.authentication.token_handling import generate_access_token
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.data_service.constants import (
    API_FILTER_INTERVAL,
    API_FILTER_TIME_GTE,
    API_FILTER_TIME_LTE,
    RE_FETCH_MOST_RECENT,
)
from services.data_service.tests.api.snapshot_impl import SnapshotTest
from settings.app_constants import DEMO1_UUID


@pytest.fixture
def snapshot(request) -> SnapshotTest:
    return SnapshotTest(request)


def get_path_operation(method: str, test_client: TestClient) -> Callable:
    return {
        "GET": test_client.get,
        "POST": test_client.post,
        "PATCH": test_client.patch,
        "DELETE": test_client.delete,
    }[method]


@pytest.fixture
def data_service_common_query_params():
    return {
        API_FILTER_TIME_GTE: "2019-08-17T06:43:30-04:00",
        API_FILTER_TIME_LTE: "2021-08-20T10:43:30-04:00",
        API_FILTER_INTERVAL: "1y",
        RE_FETCH_MOST_RECENT: "true",
    }


@pytest.fixture
def data_service_common_query_params_detail():
    return {
        API_FILTER_TIME_GTE: "2019-08-17T06:43:30-04:00",
        API_FILTER_TIME_LTE: "2021-08-20T10:43:30-04:00",
        RE_FETCH_MOST_RECENT: "true",
    }


@pytest.fixture(scope="function")
def seed_user_header() -> dict:
    user: MemberUser = MemberUserBuilder().with_uuid(DEMO1_UUID).build()
    return {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
