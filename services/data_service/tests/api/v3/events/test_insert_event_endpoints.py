import json
import random
from typing import Awaitable, Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.domain.enums.metadata_v3 import In<PERSON><PERSON><PERSON><PERSON><PERSON>, Origin, SourceOS
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.type_resolver import TypeResolver
from services.data_service.api.models.output.events.event_api_output_v3 import EventV3APIOutput, GroupV3APIOutput
from services.data_service.api.models.request.event.insert_event_api_request_input import InsertEventAPIRequestInput
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.application.builders.event_metadata_input_builder import EventMetadataInputBuilder
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.models.insert_event_group_input import InsertEventGroupInput
from services.data_service.application.use_cases.events.models.shared import EventMetadataInput
from services.data_service.tests.api.common_rpc_calls import (
    _call_post_endpoint,
)
from services.data_service.tests.api.v3.events.event_assert_helpers import EventAssertHelpers
from services.data_service.tests.application.builders.v3.event.insert.insertable_event_input_builder import (
    InsertableEventInputBuilder,
)
from services.data_service.tests.application.builders.v3.event.insert.insertable_group_input_builder import (
    InsertableGroupInputBuilder,
)


class TestInsertEventEndpoints:
    @pytest.fixture
    async def user_with_events(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()
        random_input_origin = random.choice([Origin(o.value) for o in InsertableOrigin])
        events = (
            EventBuilder().with_owner_id(owner_id=user.user_uuid).with_origin(origin=random_input_origin).build_all()
        )

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in inserted_events])

    async def test_insert_simple_events_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        input_data = InsertableEventInputBuilder().should_build_groups(should_build=False).build_all()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(
            documents=input_data,
            metadata=input_metadata,
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        assert len(docs) == len(input_data)

        EventAssertHelpers.assert_events(returned_events=docs, expected_events=input_data)
        EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=docs)
        for inserted_event, expected_event in zip(docs, input_data):
            assert expected_event.assets
            assert inserted_event.asset_references
            await EventAssertHelpers.validate_assets_content(
                output_assets=inserted_event.asset_references, input_assets=expected_event.assets, headers=headers
            )
        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(d.id, TypeResolver.get_event(d.type)) for d in docs])

    @pytest.fixture
    async def user_with_template(
        self, template_repo: TemplateRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[EventTemplate, MemberUser]:
        user: MemberUser = await user_factory()
        template = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build()

        inserted_template = (await template_repo.insert(templates=[template], force_strong_consistency=True))[0]
        assert isinstance(inserted_template, EventTemplate)

        yield inserted_template, user

        # Teardown
        await template_repo.delete_by_id(ids=[inserted_template.id])

    async def test_insert_event_with_template_passes(self, user_with_template: tuple[EventTemplate, MemberUser]):
        event_template, user = user_with_template
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_data = (
            InsertableEventInputBuilder()
            .with_template_id(template_id=event_template.id)
            .with_type_id(type_id=event_template.document.type_id())
            .build()
        )
        request_input = InsertEventAPIRequestInput(
            documents=[input_data],
            metadata=EventMetadataInputBuilder().build(),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        assert len(payload.documents) == 1
        inserted_event = payload.documents[0]
        assert inserted_event.template_id == event_template.id

    async def test_insert_event_endpoint_already_exists_raises(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        request_input = InsertEventAPIRequestInput(
            documents=existing_events,
            metadata=EventMetadataInput(
                origin=InsertableOrigin(existing_events[0].metadata.origin.value),
                source_service=existing_events[0].metadata.source_service,
                source_os=SourceOS.UNKNOWN,
                origin_device=None,
            ),
        )

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_insert_group_event_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        def _collect_nested_group_inputs(group_input: InsertEventGroupInput) -> list[InsertEventGroupInput]:
            docs = [group_input]
            for event in group_input.events:
                if isinstance(event, InsertEventGroupInput):
                    docs.extend(_collect_nested_group_inputs(event))
            return docs

        # Arrange
        user, headers = await user_headers_factory()
        input_data = InsertableGroupInputBuilder().build_all()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(documents=input_data, metadata=input_metadata)

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs

        inserted_group_list = [e for e in docs if isinstance(e, GroupV3APIOutput)]
        input_groups = []
        for group_input in input_data:
            input_groups.extend(_collect_nested_group_inputs(group_input))
        assert len(inserted_group_list) == len(input_groups)

        for inserted_group, expected_group in zip(inserted_group_list, input_groups):
            EventAssertHelpers.assert_events(returned_events=[inserted_group], expected_events=[expected_group])
            EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=[inserted_group])
            self._assert_events_in_group(
                documents=docs, inserted_group=inserted_group, expected_events=expected_group.events
            )

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(d.id, TypeResolver.get_event(d.type)) for d in docs])

    async def test_insert_recursive_nested_group_endpoint_passes(
        self,
        user_headers_factory,
        event_repo: EventRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        input_data = InsertableGroupInputBuilder().build_recursively()
        input_metadata = EventMetadataInputBuilder().build()
        request_input = InsertEventAPIRequestInput(documents=input_data, metadata=input_metadata)

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        # Assert any top level non group events
        non_group_events = [d for d in docs if (isinstance(d, Event) and not d.group_id)]
        EventAssertHelpers.assert_events(
            returned_events=non_group_events,
            # TODO: isinstance check
            expected_events=[e for e in input_data if not isinstance(e, InsertEventGroupInput)],
        )
        EventAssertHelpers.assert_metadata(input_metadata=input_metadata, output_events=non_group_events)

        async def _assert_groups_recursively(
            returned_documents: Sequence[EventV3APIOutput],
            insert_input: Sequence[InsertEventGroupInput],
        ):
            returned_doc_ids = [d.id for d in returned_documents]
            top_level_returned_groups = [
                e
                for e in returned_documents
                if isinstance(e, GroupV3APIOutput) and (not e.group_id or e.group_id not in returned_doc_ids)
            ]
            assert len(top_level_returned_groups) == len(insert_input)

            if not top_level_returned_groups:
                return

            inner_expected_groups = []
            for inserted_group, expected_group in zip(top_level_returned_groups, insert_input):
                await EventAssertHelpers.validate_assets_content(
                    output_assets=inserted_group.asset_references, input_assets=expected_group.assets, headers=headers
                )
                EventAssertHelpers.assert_events(returned_events=[inserted_group], expected_events=[expected_group])
                # TODO: isinstance check
                if expected_group.events:
                    self._assert_events_in_group(
                        documents=docs, inserted_group=inserted_group, expected_events=expected_group.events
                    )

                inner_expected_groups.extend(
                    # TODO: isinstance check
                    [e for e in expected_group.events if isinstance(e, InsertEventGroupInput)]
                )

            await _assert_groups_recursively(
                returned_documents=[
                    d for d in returned_documents if d.id not in [g.id for g in top_level_returned_groups]
                ],
                insert_input=inner_expected_groups,
            )

        await _assert_groups_recursively(
            # TODO: isinstance
            returned_documents=docs,
            insert_input=[e for e in input_data if isinstance(e, InsertEventGroupInput)],
        )

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(d.id, TypeResolver.get_event(d.type)) for d in docs])

    @staticmethod
    def _assert_events_in_group(
        documents: Sequence[EventV3APIOutput],
        inserted_group: GroupV3APIOutput,
        expected_events: InsertEventInputs,
    ):
        """Asserts that the events within a group match the expected events."""
        submission_id = inserted_group.submission_id
        inserted_events = [e for e in documents if e.group_id == inserted_group.id]
        for e in inserted_events:
            assert e.submission_id == submission_id
        EventAssertHelpers.assert_events(returned_events=inserted_events, expected_events=expected_events)
