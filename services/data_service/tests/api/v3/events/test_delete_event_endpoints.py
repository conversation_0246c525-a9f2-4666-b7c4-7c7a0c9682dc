import json
import random
from asyncio import TaskGroup
from typing import Awaitable, Callable, Sequence
from uuid import uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse
from services.base.application.assets import Assets
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.core_event import CoreEvent
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.exercise.exercise import Exercise
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.group_builder import GroupBuilder
from services.data_service.api.models.request.event.delete_event_api_request_input import DeleteEventAPIRequestInput
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.events.models.delete_event_input_boundary import DeleteEventInput
from services.data_service.tests.api.common_rpc_calls import _call_delete_endpoint
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class TestDeleteEventEndpoints:
    @pytest.fixture
    async def user_with_events(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()
        events = (
            EventBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_origin(origin=PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build_n()
        )

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in events])

    @pytest.fixture
    async def user_group_grouped_events(
        self, user_factory: Callable[[], Awaitable[MemberUser]], event_repo: EventRepository
    ) -> tuple[MemberUser, CoreEvent, Sequence[Event]]:
        user: MemberUser = await user_factory()
        group = GroupBuilder().with_owner_id(user.user_uuid).build()
        inserted_group = (await event_repo.insert(events=[group], force_strong_consistency=True))[0]
        events_linked_to_group = EventBuilder().with_owner_id(user.user_uuid).with_group_id(group.id).build_n()
        inserted_grouped_events = await event_repo.insert(events=events_linked_to_group, force_strong_consistency=True)

        yield user, inserted_group, inserted_grouped_events

        # Teardown
        await event_repo.delete_by_id(
            ids_and_types=[(e.id, type(e)) for e in [inserted_group, *events_linked_to_group]]
        )

    async def test_delete_event_different_owner_raises_forbidden(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        input_events = [DeleteEventInput.map(model=e) for e in existing_events]
        request_input = DeleteEventAPIRequestInput(documents=input_events)

        # Act
        response = await _call_delete_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_403_FORBIDDEN, response.json()

    async def test_delete_event_not_found_raises_bad_request(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_events = [DeleteEventInput.map(model=e) for e in existing_events]
        # change single id
        input_events[0].id = uuid4()
        request_input = DeleteEventAPIRequestInput(documents=input_events)

        # Act
        response = await _call_delete_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()

    async def test_delete_event_endpoint_passes(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_events = [DeleteEventInput.map(model=e) for e in existing_events]
        request_input = DeleteEventAPIRequestInput(documents=input_events)

        # Act
        response = await _call_delete_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsIdsResponse(**response.json())
        assert sorted(payload.document_ids) == sorted([event.id for event in existing_events])

        assert not await event_repo.search_by_id(ids_and_types=[(e.id, type(e)) for e in existing_events])

    async def test_delete_group_and_its_events_should_return_correct_number_of_ids(
        self, user_group_grouped_events: tuple[MemberUser, CoreEvent, Sequence[Event]], event_repo: EventRepository
    ):
        # Arrange
        user, group, grouped_events = user_group_grouped_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_events = [DeleteEventInput.map(model=e) for e in [group, *grouped_events]]
        request_input = DeleteEventAPIRequestInput(documents=input_events)

        # Act
        response = await _call_delete_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsIdsResponse(**response.json())
        assert sorted(payload.document_ids) == sorted([event.id for event in [group, *grouped_events]])
        assert not await event_repo.search_by_id(ids_and_types=[(e.id, type(e)) for e in [group, *grouped_events]])

    @pytest.fixture
    async def user_with_recursive_nested_groups(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> tuple[list[CoreEvent], list[Exercise], MemberUser]:
        user: MemberUser = await user_factory()

        async def create_linked_groups_and_events(
            event_repo: EventRepository,
            user: MemberUser,
            iterations_left: int,
        ) -> tuple[list[CoreEvent], list[Exercise]]:
            # Create group events at this layer
            group_events = GroupBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
            inserted_groups = await event_repo.insert(events=group_events, force_strong_consistency=True)
            next_layer_groups = []
            next_layer_events = []

            # Recursively create the next layer of groups and events
            if iterations_left > 1:
                next_layer_groups, next_layer_events = await create_linked_groups_and_events(
                    event_repo=event_repo, user=user, iterations_left=iterations_left - 1
                )

            linked_events = []
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=2)):
                group_id = random.choice(inserted_groups).id
                linked_events.extend(
                    EventBuilder().with_group_id(group_id=group_id).with_owner_id(user.user_uuid).build_all()
                )
            inserted_events = await event_repo.insert(events=linked_events, force_strong_consistency=True)

            # Combine results of this layer with the next layers
            all_groups = [*inserted_groups, *next_layer_groups]
            all_events = [*inserted_events, *next_layer_events]

            return all_groups, all_events

        iterations_left = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=4)

        inserted_groups, inserted_events = await create_linked_groups_and_events(
            event_repo=event_repo, user=user, iterations_left=iterations_left
        )

        yield inserted_groups, inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in [*inserted_events, *inserted_groups]])

    async def test_delete_recursive_group_endpoint_passes(
        self,
        user_with_recursive_nested_groups,
        event_repo: EventRepository,
    ):
        # Arrange
        existing_groups, linked_events, user = user_with_recursive_nested_groups
        expected_deleted_ids = sorted([group.id for group in existing_groups] + [event.id for event in linked_events])

        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_groups = [DeleteEventInput.map(model=g) for g in existing_groups]
        request_input = DeleteEventAPIRequestInput(documents=input_groups)
        found_events = await event_repo.search_by_id(
            ids_and_types=[(e.id, type(e)) for e in [*existing_groups, *linked_events]]
        )
        found_ids = sorted([e.id for e in found_events])
        assert len(found_ids) == len(expected_deleted_ids)
        assert found_ids == expected_deleted_ids

        # Act
        response = await _call_delete_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsIdsResponse(**response.json())
        deleted_ids = payload.document_ids

        assert len(deleted_ids) == len(
            expected_deleted_ids
        ), f"group events: {len(existing_groups)}, linked_events: {len(linked_events)}"
        assert sorted(payload.document_ids) == expected_deleted_ids
        assert not await event_repo.search_by_id(
            ids_and_types=[(e.id, type(e)) for e in [*existing_groups, *linked_events]]
        )

    @pytest.fixture
    async def user_with_events_and_assets(
        self,
        event_repo: EventRepository,
        user_factory: Callable[[], Awaitable[MemberUser]],
        asset_service: AssetService,
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()

        events_with_assets = []
        for n in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=3)):
            input_assets = EventInputAssetBuilder().build_n()
            events_with_assets.append(
                EventBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_origin(origin=PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
                .with_asset_references(
                    asset_references=await asset_service.store_input_assets(
                        owner_id=user.user_uuid, assets=input_assets
                    )
                )
                .build()
            )

        inserted_events = await event_repo.insert(events=events_with_assets, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in inserted_events])

    async def test_delete_event_assets_get_deleted_passes(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        event_repo: EventRepository,
        object_storage_service: ObjectStorageService,
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        asset_ids = [a.asset_id for e in existing_events if e.asset_references for a in e.asset_references]
        input_events = [DeleteEventInput.map(model=e) for e in existing_events]
        request_input = DeleteEventAPIRequestInput(documents=input_events)

        # Act
        response = await _call_delete_endpoint(
            request_url=EventEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsIdsResponse(**response.json())

        assert sorted(payload.document_ids) == sorted([event.id for event in existing_events])
        assert not await event_repo.search_by_id(ids_and_types=[(e.id, type(e)) for e in existing_events])

        container_name = Assets.generate_user_storage_container_name(user_uuid=user.user_uuid)

        async with TaskGroup() as group:
            for asset_id in asset_ids:
                asset_path = Assets.generate_asset_path(asset_id=asset_id)
                group.create_task(
                    object_storage_service.does_asset_exist(
                        container_name=container_name,
                        object_name=asset_path,
                    )
                )

        results = [t.result() for t in group._tasks]
        assert not any((r for r in results))
