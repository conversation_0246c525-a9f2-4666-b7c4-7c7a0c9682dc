import json
from uuid import uuid4

from starlette import status

from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.payload.exercise_payload_builders import ExercisePayloadBuilder
from services.base.tests.domain.builders.template.payload.note_payload_builder import NotePayloadBuilder
from services.data_service.api.models.response.template.template_api_output import (
    EventTemplateAPIOutput,
    TemplateAPIOutput,
)
from services.data_service.api.urls import TemplateEndpointUrls
from services.data_service.tests.api.builders.insert_event_template_request_input_builder import (
    InsertEventTemplateInputBoundaryItem,
    InsertEventTemplateRequestInputBuilder,
)
from services.data_service.tests.api.builders.search_templates_request_input_builder import (
    SearchTemplatesRequestInputBuilder,
)
from services.data_service.tests.api.builders.update_event_template_request_input_builder import (
    UpdateEventTemplateRequestInputBuilder,
    UpdateEventTemplateRequestInputItemBuilder,
)
from services.data_service.tests.api.common_rpc_calls import (
    _call_get_endpoint,
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.tests.api.utils.test_utils import TestUtils


class TestEventTemplate:
    async def test_search_templates_endpoint_should_pass(self, template_repo: TemplateRepository, user_headers_factory):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates = (
            EventTemplateBuilder().with_archived_at(True).with_owner_id(owner_id=user.user_uuid).build_all()
        )
        not_archived_template = EventTemplateBuilder().with_owner_id(user.user_uuid).with_archived_at(False).build()

        inserted_templates = await template_repo.insert(
            templates=list(event_templates) + [not_archived_template], force_strong_consistency=True
        )

        request_builder = SearchTemplatesRequestInputBuilder().with_limit(len(inserted_templates))

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_params_as_dict(),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        fetched_templates = payload.documents
        assert len(fetched_templates) == len(inserted_templates)
        sorted_inserted_templates = sorted(
            inserted_templates, key=lambda t: (t.system_properties.created_at, t.id), reverse=True
        )

        # Expecting all EventTemplates
        for template, expected_template in zip(fetched_templates, sorted_inserted_templates):
            assert isinstance(template, EventTemplateAPIOutput)
            assert isinstance(expected_template, EventTemplate)
            assert template.id == expected_template.id
            assert template.name == expected_template.name
            assert template.tags == expected_template.tags
            assert template.document_name == expected_template.document_name
            assert template.document.type == expected_template.document.type
            assert template.document.category == expected_template.document.category
            assert template.document.duration == expected_template.document.duration
            assert template.system_properties.created_at == expected_template.system_properties.created_at
            assert template.system_properties.updated_at == expected_template.system_properties.updated_at
            assert template.system_properties.deleted_at == expected_template.system_properties.deleted_at

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_search_templates_endpoint_no_templates_should_return_204(
        self,
        user_headers_factory,
    ):
        # Arrange
        user, headers = await user_headers_factory()

        request_builder = SearchTemplatesRequestInputBuilder()

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.SEARCH,
            json=request_builder.build_body_as_dict(),
            query_params=request_builder.build_params_as_dict(),
            headers=headers,
            retry=False,
        )
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT, response.content

    async def test_insert_event_template_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        request_input = InsertEventTemplateRequestInputBuilder().build_all()

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        inserted_templates = payload.documents
        input_templates = request_input.documents

        assert len(inserted_templates) == len(input_templates)

        for inserted_template, expected_template in zip(inserted_templates, input_templates):
            assert isinstance(inserted_template, EventTemplateAPIOutput)
            assert isinstance(expected_template, InsertEventTemplateInputBoundaryItem)
            assert inserted_template.name == expected_template.name
            assert inserted_template.tags == expected_template.tags
            assert inserted_template.document_name == expected_template.document.name
            assert inserted_template.document.type == expected_template.document.type
            assert inserted_template.document.category == expected_template.document.category
            assert inserted_template.document.tags == expected_template.document.tags
            assert inserted_template.document.duration == expected_template.document.duration

            assert inserted_template.system_properties.created_at
            assert TestUtils.is_date_within_one_minute(inserted_template.system_properties.created_at)
            assert inserted_template.system_properties.updated_at is None
            assert inserted_template.system_properties.deleted_at is None

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_insert_template_endpoint_should_raise_template_already_exists(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        request_input = InsertEventTemplateRequestInputBuilder().build()

        # Act
        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
        )

        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        doc_ids = [d.id for d in payload.documents]

        # Ensure the search index is initialized
        _ = await _call_get_endpoint(request_url=TemplateEndpointUrls.BASE, headers=headers)

        response = await _call_post_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

        # Teardown
        await template_repo.delete_by_id(doc_ids)

    async def test_update_template_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates = (
            EventTemplateBuilder()
            .with_archived_at(archived_at=False)
            .with_owner_id(owner_id=user.user_uuid)
            .build_all()
        )

        inserted_templates = await template_repo.insert(templates=event_templates, force_strong_consistency=True)
        updated_templates = []
        for inserted_template in inserted_templates:
            if isinstance(inserted_template, EventTemplate):
                updated_templates.append(
                    UpdateEventTemplateRequestInputItemBuilder()
                    .with_document(document=inserted_template.document)
                    .with_id(inserted_template.id)
                    .build()
                )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=updated_templates).build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        updated_templates = payload.documents

        assert len(updated_templates) == len(updated_templates)

        for updated_template, expected_template in zip(
            sorted(updated_templates, key=lambda p: p.id), sorted(updated_templates, key=lambda p: p.id)
        ):
            assert isinstance(updated_template, EventTemplateAPIOutput)
            assert isinstance(expected_template, EventTemplateAPIOutput)
            assert updated_template.name == expected_template.name
            assert updated_template.tags == expected_template.tags
            assert updated_template.document_name == expected_template.document_name
            assert updated_template.document.type == expected_template.document.type
            assert updated_template.document.category == expected_template.document.category
            assert updated_template.document.tags == expected_template.document.tags
            assert updated_template.document.duration == expected_template.document.duration
            assert updated_template.system_properties.created_at
            assert updated_template.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_template.system_properties.updated_at)
            assert updated_template.system_properties.deleted_at is None

        # Teardown
        await template_repo.delete_by_id([t.id for t in updated_templates])

    async def test_update_template_endpoint_update_document_name_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates = (
            EventTemplateBuilder()
            .with_archived_at(archived_at=False)
            .with_owner_id(owner_id=user.user_uuid)
            .build_all()
        )

        inserted_templates = await template_repo.insert(templates=event_templates, force_strong_consistency=True)
        updated_templates = []
        for inserted_template in inserted_templates:
            assert isinstance(inserted_template, EventTemplate)
            new_document_name = PrimitiveTypesGenerator.generate_random_string(min_length=1, max_length=8)
            inserted_template.document_name = new_document_name
            inserted_template.document.name = new_document_name
            updated_templates.append(
                UpdateEventTemplateRequestInputItemBuilder()
                .with_document(document=inserted_template.document)
                .with_id(inserted_template.id)
                .build()
            )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=updated_templates).build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content
        payload = CommonDocumentsResponse[TemplateAPIOutput](**response.json())
        updated_templates = payload.documents

        assert len(updated_templates) == len(updated_templates)

        for updated_template, expected_template in zip(
            sorted(updated_templates, key=lambda p: p.id), sorted(updated_templates, key=lambda p: p.id)
        ):
            assert isinstance(updated_template, EventTemplateAPIOutput)
            assert isinstance(expected_template, EventTemplateAPIOutput)
            assert updated_template.name == expected_template.name
            assert updated_template.tags == expected_template.tags
            assert updated_template.document_name == expected_template.document_name
            assert updated_template.document.type == expected_template.document.type
            assert updated_template.document.category == expected_template.document.category
            assert updated_template.document.tags == expected_template.document.tags
            assert updated_template.document.duration == expected_template.document.duration
            assert updated_template.system_properties.created_at
            assert updated_template.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_template.system_properties.updated_at)
            assert updated_template.system_properties.deleted_at is None

        # Teardown
        await template_repo.delete_by_id([t.id for t in updated_templates])

    async def test_update_template_endpoint_update_document_type_should_raise(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_exercise_template = (
            EventTemplateBuilder()
            .with_document(ExercisePayloadBuilder().build())
            .with_owner_id(owner_id=user.user_uuid)
            .build()
        )
        event_note_template = (
            EventTemplateBuilder()
            .with_document(NotePayloadBuilder().build())
            .with_owner_id(owner_id=user.user_uuid)
            .build()
        )

        inserted_templates = await template_repo.insert(
            templates=[event_exercise_template], force_strong_consistency=True
        )

        updated_templates = []

        for inserted_template in inserted_templates:
            assert isinstance(inserted_template, EventTemplate)
            updated_templates.append(
                UpdateEventTemplateRequestInputItemBuilder()
                .with_document(document=event_note_template.document)
                .with_id(inserted_template.id)
                .build()
            )

        request_input = UpdateEventTemplateRequestInputBuilder().with_values(values=updated_templates).build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_update_templates_endpoint_doc_not_found_should_raise(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()

        request_input = UpdateEventTemplateRequestInputBuilder().build()

        # Act
        response = await _call_patch_endpoint(
            request_url=TemplateEndpointUrls.BASE,
            headers=headers,
            json=json.loads(request_input.model_dump_json()),
            retry=False,
        )
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

    async def test_archive_templates_endpoint_should_pass(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        event_templates = list(EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_all())
        not_archived_template = EventTemplateBuilder().with_owner_id(user.user_uuid).with_archived_at(False).build()

        inserted_templates = await template_repo.insert(
            templates=event_templates + [not_archived_template], force_strong_consistency=True
        )

        # Arrange
        doc_ids = [t.id for t in inserted_templates if not t.archived_at]
        request_url = join_as_url(base_url=TemplateEndpointUrls.ARCHIVE, query_params={"template_ids": doc_ids})

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.content

        documents = await template_repo.search_by_id(ids=doc_ids)
        assert all([bool(d.archived_at) for d in documents])

        # Teardown
        await template_repo.delete_by_id([t.id for t in inserted_templates])

    async def test_archive_templates_endpoint_doc_not_found_should_raise(
        self,
        user_headers_factory,
        template_repo: TemplateRepository,
    ):
        # Arrange
        user, headers = await user_headers_factory()
        request_url = join_as_url(base_url=TemplateEndpointUrls.ARCHIVE, query_params={"template_ids": [uuid4()]})
        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content
