import json
from typing import Awaitable, Callable

import pytest
from starlette import status

from services.base.domain.schemas.member_user.member_user import MemberUser
from services.data_service.api.urls import LookupEndpointURLs
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary
from services.data_service.tests.api.common_rpc_calls import _call_post_endpoint


class TestContentLookupEndpoints:
    @pytest.mark.integration
    @pytest.mark.parametrize(
        "input",
        [
            ContentLookupInputBoundary(url="https://stackoverflow.com"),
            ContentLookupInputBoundary(url="https://www.google.com/"),
            ContentLookupInputBoundary(url="https://github.com/"),
            ContentLookupInputBoundary(url="https://x.com/elonmusk/status/1519480761749016577"),
            ContentLookupInputBoundary(url="https://pypi.org/project/metadata-parser/"),
        ],
    )
    async def test_content_lookup_endpoint_returns_200(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        input: ContentLookupInputBoundary,
    ):
        _, headers = await user_headers_factory()

        response = await _call_post_endpoint(
            request_url=LookupEndpointURLs.BASE,
            headers=headers,
            json=json.loads(input.model_dump_json(by_alias=True)),
            retry=False,
        )

        assert response
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.json()}"

    @pytest.mark.parametrize(
        "input",
        [
            ContentLookupInputBoundary(url="http://localhost:8888"),
            ContentLookupInputBoundary(url="https://x.com/invalid/url"),
            ContentLookupInputBoundary(url="https://twitter.com/just_username"),
            ContentLookupInputBoundary(url="https://x.com/username/status/123456789"),  # Non existant post
        ],
    )
    async def test_content_lookup_endpoint_returns_204(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        input: ContentLookupInputBoundary,
    ):
        _, headers = await user_headers_factory()

        response = await _call_post_endpoint(
            request_url=LookupEndpointURLs.BASE,
            headers=headers,
            json=json.loads(input.model_dump_json(by_alias=True)),
            retry=False,
        )

        assert response
        assert response.status_code == status.HTTP_204_NO_CONTENT
