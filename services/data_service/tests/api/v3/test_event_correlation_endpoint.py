import random
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, AsyncGenerator, <PERSON>waitable, Callable, Sequence

import pytest
from starlette import status

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.shared import CoordinatesModel
from services.base.domain.schemas.steps import Steps
from services.base.tests.domain.builders.core_event_builder import CoreEventBuilder
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.heart_rate_builder import HeartRateBuilder
from services.base.tests.domain.builders.location_builder import LocationBuilder
from services.base.tests.domain.builders.steps_builder import StepsBuilder
from services.base.tests.domain.builders.symptom_builder import SymptomBuilder
from services.data_service.api.models.output.event_correlation_api_output import EventCorrelationAPIOutput
from services.data_service.api.urls import AnalyseEndpointUrls
from services.data_service.tests.api.common_calls import _delete_user_documents
from services.data_service.tests.api.common_rpc_calls import _call_post_endpoint


class TestEventComparisonEndpoint:

    @pytest.fixture
    async def user_with_symptoms_triggers(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[Event], Sequence[Event], dict], Any]:
        user, headers = await user_headers_factory()
        start = PrimitiveTypesGenerator.generate_random_aware_datetime()
        end = start + timedelta(days=300)

        symptoms = []
        triggers = []
        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=500, max_value=1000)):
            if random.choice([True, False]):
                continue
            ts = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=start, lte=end)
            et = PrimitiveTypesGenerator.generate_random_aware_datetime(
                allow_none=True,
                gte=ts,
                lte=ts + PrimitiveTypesGenerator.generate_random_timedelta(max_timedelta=timedelta(hours=1)),
            )
            symptoms.append(
                SymptomBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_name(name="headache")
                .with_timestamp(timestamp=ts)
                .with_end_time(end_time=et)
                .build()
            )

            ts = PrimitiveTypesGenerator.generate_random_aware_datetime(
                gte=ts - timedelta(hours=12), lte=ts + timedelta(hours=12)
            )
            triggers.append(
                CoreEventBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_name(name="trigger")
                .with_timestamp(timestamp=ts)
                .build()
            )
        inserted_symptoms = await event_repo.insert(events=symptoms, force_strong_consistency=True)
        inserted_triggers = await event_repo.insert(events=triggers, force_strong_consistency=True)

        yield inserted_symptoms, inserted_triggers, headers

        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in [*inserted_symptoms, *inserted_triggers]])

    async def test_correlate_event_binary_before_or_after_passes(self, user_with_symptoms_triggers):
        # Arrange
        inserted_symptoms, inserted_triggers, headers = user_with_symptoms_triggers
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([symptom.timestamp for symptom in inserted_symptoms]).isoformat()
        time_lte = max([symptom.timestamp for symptom in inserted_symptoms]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "field_name": None,
                    "aggregation_method": None,
                    "query": {
                        "types": [DataType.Symptom],
                        "query": None,
                    },
                },
                "independent": {
                    "field_name": None,
                    "aggregation_method": None,
                    "query": {
                        "types": [DataType.CoreEvent],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": random.choice(("before", "after")),
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "heat_map"
        assert output.data

    async def test_correlate_event_binary_closest_passes(self, user_with_symptoms_triggers):
        # Arrange
        inserted_symptoms, inserted_triggers, headers = user_with_symptoms_triggers
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([symptom.timestamp for symptom in inserted_symptoms]).isoformat()
        time_lte = max([symptom.timestamp for symptom in inserted_symptoms]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "field_name": None,
                    "aggregation_method": None,
                    "query": {
                        "types": [DataType.Symptom],
                        "query": None,
                    },
                },
                "independent": {
                    "field_name": None,
                    "aggregation_method": None,
                    "query": {
                        "types": [DataType.CoreEvent],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "closest",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "heat_map"
        assert output.data

    async def test_correlate_event_discrete_before_or_after_passes(self, user_with_symptoms_triggers):
        # Arrange
        inserted_symptoms, inserted_triggers, headers = user_with_symptoms_triggers
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([symptom.timestamp for symptom in inserted_symptoms]).isoformat()
        time_lte = max([symptom.timestamp for symptom in inserted_symptoms]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": None,
                    "field_name": "rating",
                    "query": {
                        "types": [DataType.Symptom],
                        "query": None,
                    },
                },
                "independent": {
                    "aggregation_method": None,
                    "field_name": "rating",
                    "query": {
                        "types": [DataType.CoreEvent],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": random.choice(("before", "after")),
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "heat_map"
        assert output.data

    async def test_correlate_event_discrete_closest_passes(self, user_with_symptoms_triggers):
        # Arrange
        inserted_symptoms, inserted_triggers, headers = user_with_symptoms_triggers
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([symptom.timestamp for symptom in inserted_symptoms]).isoformat()
        time_lte = max([symptom.timestamp for symptom in inserted_symptoms]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": None,
                    "field_name": "rating",
                    "query": {
                        "types": [DataType.Symptom],
                        "query": None,
                    },
                },
                "independent": {
                    "aggregation_method": None,
                    "field_name": "rating",
                    "query": {
                        "types": [DataType.CoreEvent],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "closest",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "heat_map"
        assert output.data

    @pytest.fixture
    async def user_with_heart_rate_steps(
        self,
        depr_event_repository: DeprEventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[HeartRate], Sequence[Steps], dict], Any]:
        user, headers = await user_headers_factory()
        start = PrimitiveTypesGenerator.generate_random_aware_datetime()
        end = start + timedelta(days=300)

        hrs = []
        steps = []
        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=300, max_value=500)):
            ts = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=start, lte=end)

            hrs.append(HeartRateBuilder().with_user_uuid(user_uuid=user.user_uuid).with_timestamp(timestamp=ts).build())

            ts = PrimitiveTypesGenerator.generate_random_aware_datetime(
                gte=ts - timedelta(hours=12), lte=ts + timedelta(hours=12)
            )
            steps.append(StepsBuilder().with_user_uuid(user_uuid=user.user_uuid).with_timestamp(timestamp=ts).build())
        inserted_hrs = await depr_event_repository.insert(models=hrs, force_strong_consistency=True)
        inserted_steps = await depr_event_repository.insert(models=steps, force_strong_consistency=True)

        yield inserted_hrs, inserted_steps, headers

        await _delete_user_documents(event_repo=depr_event_repository, user_uuid=user.user_uuid, data_schema=HeartRate)
        await _delete_user_documents(event_repo=depr_event_repository, user_uuid=user.user_uuid, data_schema=Steps)

    @pytest.fixture
    async def user_with_more_emotion_than_steps(
        self,
        depr_event_repository: DeprEventRepository,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[Event], Sequence[Steps], dict], Any]:
        user, headers = await user_headers_factory()
        start = PrimitiveTypesGenerator.generate_random_aware_datetime()
        end = start + timedelta(days=300)
        emotion = [
            EmotionBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_timestamp(timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(gte=start, lte=end))
            .build()
            for _ in range(300)
        ]

        steps = [
            StepsBuilder()
            .with_user_uuid(user_uuid=user.user_uuid)
            .with_timestamp(
                timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(
                    gte=start, lte=end - timedelta(days=100)
                )
            )
            .build()
            for _ in range(300)
        ]

        inserted_steps = await depr_event_repository.insert(models=steps, force_strong_consistency=True)
        inserted_emotions = await event_repo.insert(events=emotion, force_strong_consistency=True)

        yield inserted_emotions, inserted_steps, headers

        await event_repo.delete_by_id(ids_and_types=[(e.id, type(e)) for e in [*inserted_emotions]])
        await _delete_user_documents(event_repo=depr_event_repository, user_uuid=user.user_uuid, data_schema=Steps)

    async def test_correlate_event_more_raw_data_days_then_aggregated_days_passes(
        self, user_with_more_emotion_than_steps
    ):
        # Arrange
        inserted_emotion, inserted_steps, headers = user_with_more_emotion_than_steps
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([hr.timestamp for hr in inserted_emotion]).isoformat()
        time_lte = max([hr.timestamp for hr in inserted_emotion]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": None,
                    "field_name": None,
                    "query": {
                        "types": [DataType.Emotion],
                        "query": None,
                    },
                },
                "independent": {
                    "aggregation_method": "sum",
                    "field_name": "steps",
                    "query": {
                        "types": [DataType.Steps],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "1d",
                    },
                    "type": "before",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "box_plot"
        assert output.data

    async def test_correlate_event_continuous_before_or_after_passes(self, user_with_heart_rate_steps):
        # Arrange
        inserted_hrs, inserted_steps, headers = user_with_heart_rate_steps
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([hr.timestamp for hr in inserted_hrs]).isoformat()
        time_lte = max([hr.timestamp for hr in inserted_hrs]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": None,
                    "field_name": "bpm_avg",
                    "query": {
                        "types": [DataType.HeartRate],
                        "query": None,
                    },
                },
                "independent": {
                    "aggregation_method": None,
                    "field_name": "steps",
                    "query": {
                        "types": [DataType.Steps],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "before",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "scatter_plot"
        assert output.data

    async def test_correlate_event_continuous_before_or_after_with_aggregation_passes(self, user_with_heart_rate_steps):
        # Arrange
        inserted_hrs, inserted_steps, headers = user_with_heart_rate_steps
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([hr.timestamp for hr in inserted_hrs]).isoformat()
        time_lte = max([hr.timestamp for hr in inserted_hrs]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": "avg",
                    "field_name": "bpm_avg",
                    "query": {
                        "types": [DataType.HeartRate],
                        "query": None,
                    },
                },
                "independent": {
                    "aggregation_method": "sum",
                    "field_name": "steps",
                    "query": {
                        "types": [DataType.Steps],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "before",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "scatter_plot"
        assert output.data

    async def test_correlate_event_continuous_closest_passes(self, user_with_heart_rate_steps):
        # Arrange
        inserted_hrs, inserted_steps, headers = user_with_heart_rate_steps
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([hr.timestamp for hr in inserted_hrs]).isoformat()
        time_lte = max([hr.timestamp for hr in inserted_hrs]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": None,
                    "field_name": "bpm_avg",
                    "query": {
                        "types": [DataType.HeartRate],
                        "query": None,
                    },
                },
                "independent": {
                    "aggregation_method": None,
                    "field_name": "steps",
                    "query": {
                        "types": [DataType.Steps],
                        "query": None,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "closest",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.suggested_visualisation == "scatter_plot"
        assert output.data

    @pytest.fixture
    async def user_with_location(
        self,
        depr_event_repository: DeprEventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[Location], dict], Any]:
        user, headers = await user_headers_factory()
        locations = [
            LocationBuilder()
            .with_coordinates(CoordinatesModel(latitude=49.5938, longitude=17.2509))
            .with_user_uuid(user_uuid=user.user_uuid)
            .build()
            for _ in range(20)
        ]
        inserted_locations = await depr_event_repository.insert(models=locations, force_strong_consistency=True)
        yield inserted_locations, headers

        # Teardown
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=Location, event_repo=depr_event_repository)

    async def test_correlate_event_continuous_aq_before_passes(self, user_with_location):
        # Arrange
        inserted_locations, headers = user_with_location
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([location.timestamp for location in inserted_locations]).isoformat()
        time_lte = max([location.timestamp for location in inserted_locations]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": "avg",
                    "field_name": "pollutants.pm10",
                    "query": {
                        "domain_type": DataType.AirQuality,
                    },
                },
                "independent": {
                    "aggregation_method": "avg",
                    "field_name": "pollutants.pm25",
                    "query": {
                        "domain_type": DataType.AirQuality,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "before",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.data

    async def test_correlate_event_continuous_weather_before_passes(self, user_with_location):
        # Arrange
        inserted_locations, headers = user_with_location
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([location.timestamp for location in inserted_locations]).isoformat()
        time_lte = max([location.timestamp for location in inserted_locations]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": "avg",
                    "field_name": "temperature.temperature",
                    "query": {
                        "domain_type": DataType.Weather,
                    },
                },
                "independent": {
                    "aggregation_method": "avg",
                    "field_name": "temperature.feels_like",
                    "query": {
                        "domain_type": DataType.Weather,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "before",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.data

    async def test_correlate_event_continuous_pollen_before_passes(self, user_with_location):
        # Arrange
        inserted_locations, headers = user_with_location
        request_url = AnalyseEndpointUrls.CORRELATE_EVENT
        time_gte = min([location.timestamp for location in inserted_locations]).isoformat()
        time_lte = max([location.timestamp for location in inserted_locations]).isoformat()

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            json={
                "dependent": {
                    "aggregation_method": "avg",
                    "field_name": "tree.count",
                    "query": {
                        "domain_type": DataType.Pollen,
                    },
                },
                "independent": {
                    "aggregation_method": "avg",
                    "field_name": "grass.count",
                    "query": {
                        "domain_type": DataType.Pollen,
                    },
                },
                "temporal_options": {
                    "time_input": {
                        "time_gte": time_gte,
                        "time_lte": time_lte,
                        "interval": "6h",
                    },
                    "type": "before",
                },
            },
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Got response {response.content}"
        output = EventCorrelationAPIOutput(**response.json())
        assert output
        assert output.data
