import pytest
from starlette import status

from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint
from services.data_service.v02.api.urls import MeasureEndpointUrls


@pytest.mark.parametrize(DocumentLabels.ORGANIZATION, [Organization.GOOGLE, Organization.LLIF])
async def test_aggregate_heart_rate_endpoint_sample_data_fetch_should_pass(
    snapshot,
    data_service_common_query_params,
    seed_user_header,
    organization: Organization,
):
    data_service_common_query_params.update({DocumentLabels.ORGANIZATION: organization.value})
    request_url = join_as_url(MeasureEndpointUrls.HEART_RATE, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    snapshot.assert_match(response.json())


@pytest.mark.parametrize(DocumentLabels.ORGANIZATION, [Organization.FITBIT])
async def test_aggregate_resting_heart_rate_endpoint_sample_data_fetch_should_pass(
    snapshot,
    data_service_common_query_params,
    seed_user_header,
    organization: Organization,
):
    data_service_common_query_params.update({DocumentLabels.ORGANIZATION: organization.value})
    request_url = join_as_url(MeasureEndpointUrls.RESTING_HEART_RATE, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK, f"got response {response.content}"
    snapshot.assert_match(response.json())
