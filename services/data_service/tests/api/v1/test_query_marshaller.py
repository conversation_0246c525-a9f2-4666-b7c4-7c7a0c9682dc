from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsFields
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.data_service.v1.api.query_marshaller import QueryMarshaller


class TestQueryMarshaller:

    def test_query_serialization(self):
        values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["test"])
        type_query = TypeQuery(query=values_query, domain_types=[DiaryEvents])
        query = Query(type_queries=[type_query])

        result = QueryMarshaller.serialize(query)
        expected_dict = {
            "type_queries": [
                {
                    "query": {
                        "type": "values",
                        "field_name": "name",
                        "values": ["test"],
                    },
                    "types": ["DiaryEvents"],
                }
            ]
        }

        assert result == expected_dict

    def test_query_deserialization(self):
        query_as_dict = {
            "type_queries": [
                {
                    "query": {
                        "type": "values",
                        "field_name": "name",
                        "values": ["test"],
                    },
                    "types": ["DiaryEvents"],
                }
            ]
        }

        result = QueryMarshaller.deserialize(query_as_dict)
        type_query = TypeQuery(
            query=ValuesQuery(field_name=DiaryEventsFields.NAME, values=["test"]), domain_types=[DiaryEvents]
        )
        expected_query = Query(type_queries=[type_query])

        assert result == expected_query
