from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsFields
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.data_service.application.models.event_feed_continuation_token import EventFeedContinuationToken
from services.data_service.v1.api.continuation_token_marshaller import ContinuationTokenMarshaller


class TestContinuationTokenMarshaller:

    def test_encoding_and_decoding_of_event_feed_continuation_token(self):
        values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=["test"])
        query = Query(type_queries=[TypeQuery(query=values_query, domain_types=[DiaryEvents])])
        continuation_token = EventFeedContinuationToken(
            token=PrimitiveTypesGenerator.generate_random_string(), query=query
        )

        encoded_token = ContinuationTokenMarshaller.encode_event_feed_continuation_token(token=continuation_token)
        decoded_token = ContinuationTokenMarshaller.decode_event_feed_continuation_token(encoded_token)

        assert decoded_token == continuation_token
