import json
from datetime import datetime, timezone
from typing import List, <PERSON><PERSON>
from uuid import uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.repository.extension_subscriptions_repository import ExtensionSubscriptionsRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail
from services.base.domain.schemas.extensions.extension_provider import ExtensionProvider
from services.base.domain.schemas.extensions.extension_subscriptions import ExtensionSubscriptions
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.data_service.tests.api.common_rpc_calls import (
    _call_get_endpoint,
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.v1.api.models.output.extensions.extension_detail_and_schema_api_output import (
    ExtensionDetailAndSchemaAPIOutput,
)
from services.data_service.v1.api.models.request.extensions.subscribe_extension_request_input import (
    SubscribeExtensionRequestInput,
)
from services.data_service.v1.api.models.response.extensions.list_extension_details_response import (
    ListExtensionDetailsResponse,
)
from services.data_service.v1.api.models.response.extensions.subscribe_extension_response import (
    SubscribeExtensionResponse,
)
from services.data_service.v1.api.models.response.extensions.unsubscribe_extension_response import (
    UnsubscribeExtensionResponse,
)
from services.data_service.v1.api.urls import ExtensionDirectoryEndpointUrls
from services.serverless.apps.trend_insights.app.models.trend_insights_models import (
    TrendInsightsInput,
    TrendInsightsSeriesOutput,
)
from settings.extension_constants import (
    LLIF_EXTENSION_PROVIDER_UUID,
    TREND_INSIGHTS_EXTENSION_DESCRIPTION,
    TREND_INSIGHTS_EXTENSION_DOMAIN,
    TREND_INSIGHTS_EXTENSION_ID,
    TREND_INSIGHTS_EXTENSION_NAME,
)


class TestExtensionDirectoryEndpoints:
    async def test_get_extension_details_should_include_seeded_trend_insights_should_pass(self):
        # Arrange
        request_url = join_as_url(
            base_url=ExtensionDirectoryEndpointUrls.BASE,
            query_params={"extension_id": TREND_INSIGHTS_EXTENSION_ID},
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = ExtensionDetailAndSchemaAPIOutput(**response.json())

        assert response_model.input_schema == TrendInsightsInput.model_json_schema()
        assert response_model.output_schema == TrendInsightsSeriesOutput.model_json_schema()

        assert response_model.extension.extension_id == TREND_INSIGHTS_EXTENSION_ID
        assert response_model.extension.provider_id == LLIF_EXTENSION_PROVIDER_UUID
        assert response_model.extension.description == TREND_INSIGHTS_EXTENSION_DESCRIPTION
        assert response_model.extension.name == TREND_INSIGHTS_EXTENSION_NAME
        assert response_model.extension.domain == TREND_INSIGHTS_EXTENSION_DOMAIN
        assert response_model.extension.version == "1.0.0"
        assert response_model.extension.tags == ["health", "statistics", "trends", "analytic"]

    @pytest.fixture(scope="class")
    async def seed_list_extensions(
        self,
        extension_detail_repository: ExtensionDetailRepository,
        extension_provider_repository: ExtensionProviderRepository,
    ):
        # Setup
        # Create provider
        provider_id = uuid4()
        provider = ExtensionProvider(
            provider_id=provider_id,
            name="test",
            domain="test.org",
            description="test",
        )

        # Create Extension
        version = "1.0.0"
        extension_id = uuid4()
        providers = await extension_provider_repository.upsert(extension_providers=[provider])
        extensions = await extension_detail_repository.upsert(
            extension_details=[
                ExtensionDetail(
                    provider_id=provider_id,
                    extension_id=extension_id,
                    name="test",
                    domain="test.org",
                    tags=["test"],
                    description="test",
                    version=version,
                )
            ]
        )

        yield extensions
        # Teardown
        await extension_detail_repository.delete(extension_details=extensions)
        await extension_provider_repository.delete(extension_providers=providers)

    async def test_list_extensions_details_should_include_added_should_pass(
        self, seed_list_extensions: List[ExtensionDetail]
    ):
        # Arrange
        request_url = join_as_url(base_url=ExtensionDirectoryEndpointUrls.LIST, query_params=None)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=uuid4())}"}

        # Act
        response = await _call_get_endpoint(request_url=request_url, headers=headers)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = ListExtensionDetailsResponse(**response.json())

        for seed_extension in seed_list_extensions:
            assert [
                ExtensionDetail.map(model=output_extension) == seed_extension
                for output_extension in response_model.items
            ]

    @pytest.fixture(scope="class")
    async def seed_data_to_subscribe(
        self,
        member_user_repository: MemberUserRepository,
        extension_provider_repository: ExtensionProviderRepository,
        extension_detail_repository: ExtensionDetailRepository,
    ):
        # Setup
        # Create provider
        provider_id = uuid4()
        provider = ExtensionProvider(
            provider_id=provider_id,
            name="test",
            domain="test.org",
            description="test",
        )

        # Create Extension
        version = "1.0.0"
        extension_id = uuid4()
        providers = await extension_provider_repository.upsert(extension_providers=[provider])
        extensions = await extension_detail_repository.upsert(
            extension_details=[
                ExtensionDetail(
                    provider_id=provider_id,
                    extension_id=extension_id,
                    name="test",
                    domain="test.org",
                    tags=["test"],
                    description="test",
                    version=version,
                )
            ]
        )
        # Create user
        user_uuid = uuid4()
        now = datetime.now(timezone.utc)
        user = await member_user_repository.insert_or_update(
            user=MemberUser(user_uuid=user_uuid, created_at=now, last_logged_at=now)
        )

        yield user, extensions[0]
        # Teardown
        await extension_detail_repository.delete(extension_details=extensions)
        await extension_provider_repository.delete(extension_providers=providers)
        await member_user_repository.delete(user=user)

    async def test_subscribe_user_to_extension_should_pass(
        self,
        extension_detail_repository: ExtensionDetailRepository,
        extension_subscriptions_repository: ExtensionSubscriptionsRepository,
        seed_data_to_subscribe: Tuple[MemberUser, ExtensionDetail],
    ):
        # Arrange
        seed_user = seed_data_to_subscribe[0]
        seed_extension = seed_data_to_subscribe[1]

        request_url = join_as_url(base_url=ExtensionDirectoryEndpointUrls.SUBSCRIBE, query_params=None)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=seed_user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=request_url,
            headers=headers,
            json=json.loads(
                SubscribeExtensionRequestInput(extension_id=seed_extension.extension_id).model_dump_json(by_alias=True)
            ),
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = SubscribeExtensionResponse(**response.json())
        assert response_model.extension_id == seed_extension.extension_id

        subscription = await extension_subscriptions_repository.get_user_subscribed_extension(
            extension_id=seed_extension.extension_id, user_id=seed_user.user_uuid
        )
        assert subscription.extension_id == seed_extension.extension_id
        assert subscription.user_id == seed_user.user_uuid

    @pytest.fixture(scope="class")
    async def seed_data_to_unsubscribe(
        self,
        member_user_repository: MemberUserRepository,
        extension_detail_repository: ExtensionDetailRepository,
        extension_provider_repository: ExtensionProviderRepository,
        extension_subscriptions_repository: ExtensionSubscriptionsRepository,
    ):
        # Setup
        # Create provider
        provider_id = uuid4()
        provider = ExtensionProvider(
            provider_id=provider_id,
            name="test",
            domain="test.org",
            description="test",
        )

        # Create Extension
        version = "1.0.0"
        extension_id = uuid4()
        providers = await extension_provider_repository.upsert(extension_providers=[provider])
        extensions = await extension_detail_repository.upsert(
            extension_details=[
                ExtensionDetail(
                    provider_id=provider_id,
                    extension_id=extension_id,
                    name="test",
                    domain="test.org",
                    tags=["test"],
                    description="test",
                    version=version,
                )
            ]
        )
        # Create user
        user_uuid = uuid4()
        now = datetime.now(timezone.utc)
        user = await member_user_repository.insert_or_update(
            user=MemberUser(user_uuid=user_uuid, created_at=now, last_logged_at=now)
        )

        # Subscribe user
        subscriptions = await extension_subscriptions_repository.upsert(
            extension_subscriptions=[ExtensionSubscriptions(extension_id=extension_id, user_id=user_uuid)]
        )

        yield user, subscriptions[0]
        # Teardown
        await extension_detail_repository.delete(extension_details=extensions)
        await extension_provider_repository.delete(extension_providers=providers)
        await member_user_repository.delete(user=user)

    async def test_unsubscribe_user_from_extension_should_pass(
        self,
        extension_subscriptions_repository: ExtensionSubscriptionsRepository,
        seed_data_to_unsubscribe: Tuple[MemberUser, ExtensionSubscriptions],
    ):
        # Arrange
        seed_user = seed_data_to_unsubscribe[0]
        seed_subscription = seed_data_to_unsubscribe[1]

        request_url = join_as_url(
            base_url=ExtensionDirectoryEndpointUrls.UNSUBSCRIBE,
            query_params={"extension_id": seed_subscription.extension_id},
        )
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=seed_user.user_uuid)}"}

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None)

        # Assert
        assert response.status_code == status.HTTP_200_OK
        response_model = UnsubscribeExtensionResponse(**response.json())
        assert response_model.extension_id == seed_subscription.extension_id

        subscription = await extension_subscriptions_repository.get_user_subscribed_extension(
            extension_id=seed_subscription.extension_id, user_id=seed_user.user_uuid
        )
        assert subscription.extension_id == seed_subscription.extension_id
        assert subscription.user_id == seed_user.user_uuid
        assert subscription.unsubscribed_at == response_model.unsubscribed_at
