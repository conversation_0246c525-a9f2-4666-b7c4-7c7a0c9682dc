from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.templates.template_payloads import TemplatePayloads
from services.data_service.api.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertGroupTemplateInputBoundaryItem,
)
from services.data_service.tests.api.builders.insert_event_template_request_input_builder import (
    InsertEventTemplateRequestInputItemBuilder,
)


class InsertGroupTemplateRequestInputBuilder:
    def build(self) -> InsertTemplateAPIRequestInput:
        return InsertTemplateAPIRequestInput(documents=InsertGroupTemplateRequestInputItemBuilder().build_n())


class InsertGroupTemplateRequestInputItemBuilder:
    def __init__(self):
        self._name: str | None = None
        self._templates: list[TemplatePayloads] | None = None

    def build(self) -> InsertGroupTemplateInputBoundaryItem:
        return InsertGroupTemplateInputBoundaryItem(
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            templates=self._templates or InsertEventTemplateRequestInputItemBuilder().build_n(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
        )

    def build_n(self, n: int | None = None) -> Sequence[InsertGroupTemplateInputBoundaryItem]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_templates(self, templates: list[TemplatePayloads]) -> Self:
        self._templates = templates
        return self
