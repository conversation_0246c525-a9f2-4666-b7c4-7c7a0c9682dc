from __future__ import annotations

from typing import List, Self
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase
from services.base.domain.schemas.templates.template_payloads import TemplatePayloads
from services.base.tests.domain.builders.template.payload.template_payload_builder import Template<PERSON>ayloadBuilder
from services.data_service.api.models.request.template.update_template_request_input import (
    UpdateTemplateAPIRequestInput,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateEventTemplateInputBoundaryItem,
    UpdateTemplateInputBoundaryItem,
)


class UpdateEventTemplateRequestInputBuilder:
    def __init__(self):
        self.values: List[UpdateTemplateInputBoundaryItem] | None = None

    def build(self) -> UpdateTemplateAPIRequestInput:
        return UpdateTemplateAPIRequestInput(
            documents=self.values or UpdateEventTemplateRequestInputItemBuilder().build_n()
        )

    def with_values(self, values: List[UpdateTemplateInputBoundaryItem]) -> UpdateEventTemplateRequestInputBuilder:
        self.values = values
        return self


class UpdateEventTemplateRequestInputItemBuilder:
    def __init__(self):
        self._name: str | None = None
        self._id: UUID | None = None
        self._document: TemplatePayloads | None = None

    def build(self) -> UpdateEventTemplateInputBoundaryItem:
        document = self._document or TemplatePayloadBuilder().build()
        return UpdateEventTemplateInputBoundaryItem(
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            document=document,
            id=self._id or uuid4(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
        )

    def build_n(self, n: int | None = None) -> List[UpdateEventTemplateInputBoundaryItem]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_document(self, document: TemplatePayloadBase) -> Self:
        self._document = document
        return self
