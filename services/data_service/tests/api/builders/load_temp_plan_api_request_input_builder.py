import random

from services.data_service.tests.api.builders.load_temp_plan_input_builder import LoadTempPlanInputBuilder
from services.data_service.v1.api.models.request.plans.load_temp_plan_api_request_input import (
    LoadTempPlanAPIRequestInput,
)


class LoadTempPlanAPIRequestInputBuilder:
    def build(self) -> LoadTempPlanAPIRequestInput:
        load_plan_inputs = [LoadTempPlanInputBuilder().build() for _ in range(random.randint(1, 10))]
        return LoadTempPlanAPIRequestInput(documents=load_plan_inputs)
