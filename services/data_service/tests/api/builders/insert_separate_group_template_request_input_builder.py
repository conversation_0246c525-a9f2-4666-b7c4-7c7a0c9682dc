from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.data_service.api.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertSeparateGroupTemplateInputBoundaryItem,
)


class InsertSeparateGroupTemplateRequestInputBuilder:
    def build(self) -> InsertTemplateAPIRequestInput:
        return InsertTemplateAPIRequestInput(documents=InsertSeparateGroupTemplateRequestInputItemBuilder().build_n())


class InsertSeparateGroupTemplateRequestInputItemBuilder:
    def __init__(self):
        self._name: str | None = None
        self._template_ids: list[UUID] | None = None

    def build(self) -> InsertSeparateGroupTemplateInputBoundaryItem:
        return InsertSeparateGroupTemplateInputBoundaryItem(
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            template_ids=self._template_ids
            or [uuid4() for _ in range(PrimitiveTypesGenerator.generate_random_int(1, 5))],
        )

    def build_n(self, n: int | None = None) -> Sequence[InsertSeparateGroupTemplateInputBoundaryItem]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_template_ids(self, template_ids: list[UUID]) -> Self:
        self._template_ids = template_ids
        return self
