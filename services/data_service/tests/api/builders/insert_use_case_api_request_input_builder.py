import random
from typing import Self, Sequence

from services.data_service.api.models.request.use_case.insert_use_case_api_request_input import (
    InsertUseCaseAPIRequestInput,
)
from services.data_service.application.use_cases.use_case.models.insert_use_case_input_boundary import (
    InsertUseCaseInput,
)
from services.data_service.tests.api.builders.insert_use_case_input_builder import InsertUseCaseInputBuilder


class InsertUseCaseAPIRequestInputBuilder:
    def __init__(self):
        self._use_cases: Sequence[InsertUseCaseInput] | None = None

    def build(self) -> InsertUseCaseAPIRequestInput:
        load_plan_inputs = self._use_cases or [
            InsertUseCaseInputBuilder().build() for _ in range(random.randint(1, 10))
        ]
        return InsertUseCaseAPIRequestInput(documents=load_plan_inputs)

    def with_use_cases(self, use_cases: Sequence[InsertUseCaseInput]) -> Self:
        self._use_cases = use_cases
        return self
