import random
from typing import Self, Sequence

from services.data_service.api.models.request.plan.insert_plans_api_request_input import InsertPlansAPIRequestInput
from services.data_service.application.use_cases.plans.models.insert_plans_input_boundary import InsertPlanInput
from services.data_service.tests.api.builders.insert_plan_input_builder import InsertPlanInputBuilder


class InsertPlansAPIRequestInputBuilder:
    def __init__(self):
        self._plans: Sequence[InsertPlanInput] | None = None

    def build(self) -> InsertPlansAPIRequestInput:
        load_plan_inputs = self._plans or [InsertPlanInputBuilder().build() for _ in range(random.randint(1, 10))]
        return InsertPlansAPIRequestInput(documents=load_plan_inputs)

    def with_plans(self, plans: Sequence[InsertPlanInput]) -> Self:
        self._plans = plans
        return self
