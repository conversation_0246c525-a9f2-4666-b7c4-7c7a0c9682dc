import pytest
from starlette import status

from services.base.application.utils.urls import join_as_url
from services.data_service.constants import (
    API_FILTER_INTERVAL,
    API_FILTER_TIME_GTE,
    API_FILTER_TIME_LTE,
    RE_FETCH_MOST_RECENT,
)
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint
from services.data_service.v02.api.urls import ActivityEndpointUrls


@pytest.mark.parametrize(
    "inputdata",
    [
        {
            API_FILTER_TIME_GTE: "2019-08-17T06:43:30-04:00",
            API_FILTER_TIME_LTE: "2021-08-20T10:43:30-04:00",
            API_FILTER_INTERVAL: "1y",
            RE_FETCH_MOST_RECENT: "true",
        },
        {
            API_FILTER_TIME_GTE: "2020-08-12T06:43:30-04:00",
            API_FILTER_TIME_LTE: "2020-08-18T10:43:30-04:00",
            API_FILTER_INTERVAL: "1d",
            RE_FETCH_MOST_RECENT: "true",
        },
    ],
)
async def test_sleep_endpoint_sample_data_fetch_should_pass_sample_data_fetch_should_pass(
    snapshot, inputdata, seed_user_header
):
    request_url = join_as_url(ActivityEndpointUrls.SLEEP, inputdata)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK

    snapshot.assert_match(response.json())


async def test_steps_endpoint_sample_data_fetch_should_pass(
    snapshot, data_service_common_query_params, seed_user_header
):
    request_url = join_as_url(ActivityEndpointUrls.STEPS, data_service_common_query_params)
    response = await _call_get_endpoint(request_url=request_url, headers=seed_user_header)

    assert response.status_code == status.HTTP_200_OK

    snapshot.assert_match(response.json())
