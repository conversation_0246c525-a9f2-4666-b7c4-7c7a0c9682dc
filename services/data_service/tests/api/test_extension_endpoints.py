import random
from collections import defaultdict
from datetime import datetime
from typing import Any, As<PERSON><PERSON><PERSON>ator, Callable, Dict, List, Sequence, Tu<PERSON>
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.application.utils.urls import join_as_url
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.extension_output_builder import (
    ExtensionResultBuilder,
    ExtensionRunBuilder,
)
from services.data_service.dependency_bootstrapper import DependencyBootstrapper
from services.data_service.tests.api.common_rpc_calls import _call_get_endpoint
from services.data_service.v1.api.urls import ExtensionEndpointUrls


class TestExtensionEndpoints:
    @pytest.fixture
    async def user_with_extension_run_data(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], MemberUser]
    ) -> <PERSON><PERSON>[List[ExtensionRun], MemberUser, UUID]:
        user: MemberUser = await user_factory()
        user_uuid = user.user_uuid
        extension_id = uuid4()
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)

        run_documents = [
            ExtensionRunBuilder().with_user_uuid(user_uuid).with_extension_id(extension_id).build()
            for i in range(random.randint(1, 10))
        ]

        inserted_extension_runs = await extension_run_repo.insert(extension_runs=run_documents)

        yield inserted_extension_runs, user, extension_id
        # Teardown
        _ = await extension_run_repo.delete_by_id(ids=[run.id for run in inserted_extension_runs])

    @pytest.fixture
    async def user_with_extension_run_and_results_data(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], MemberUser]
    ) -> Tuple[List[ExtensionRun], Dict[UUID, List[ExtensionResult]], MemberUser, UUID]:
        user: MemberUser = await user_factory()
        user_uuid = user.user_uuid
        extension_id = uuid4()
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)
        extension_result_repo = dependency_bootstrapper.get(interface=ExtensionResultRepository)

        run_documents = [
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(
                datetime.fromtimestamp(
                    timestamp=random.randint(
                        int(datetime(year=2023, month=1, day=1).timestamp()),
                        int(datetime(year=2023, month=12, day=31).timestamp()),
                    ),
                    tz=ZoneInfo("UTC"),
                )
            )
            .with_extension_id(extension_id)
            .build()
            for _ in range(random.randint(1, 5))
        ]

        inserted_extension_runs = await extension_run_repo.insert(extension_runs=run_documents)
        run_result_map: dict[UUID, list[ExtensionResult]] = defaultdict(list)
        inserted_extension_results = []

        for run in run_documents:
            result_documents = await extension_result_repo.insert(
                extension_results=[
                    ExtensionResultBuilder()
                    .with_user_uuid(user_uuid)
                    .with_timestamp(
                        datetime.fromtimestamp(
                            timestamp=random.randint(
                                int(datetime(year=2023, month=1, day=1).timestamp()),
                                int(datetime(year=2023, month=12, day=31).timestamp()),
                            ),
                            tz=ZoneInfo("UTC"),
                        )
                    )
                    .with_extension_id(extension_id)
                    .build()
                    for _ in range(random.randint(1, 5))
                ],
                parent_id=run.id,
            )

            inserted_extension_results.extend(result_documents)
            run_result_map[run.id].extend(result_documents)

        yield inserted_extension_runs, run_result_map, user, extension_id

        # Teardown
        _ = await extension_result_repo.delete_by_id(ids=[child.id for child in inserted_extension_results])
        _ = await extension_run_repo.delete_by_id(ids=[run.id for run in inserted_extension_runs])

    @pytest.fixture
    async def user_with_single_extension_run_and_results_data(
        self, dependency_bootstrapper: DependencyBootstrapper, user_factory: Callable[[], MemberUser]
    ) -> AsyncGenerator[tuple[ExtensionRun, Sequence[ExtensionResult], MemberUser, UUID], Any]:
        user: MemberUser = await user_factory()
        user_uuid = user.user_uuid
        extension_id = uuid4()
        extension_run_repo = dependency_bootstrapper.get(interface=ExtensionRunRepository)
        extension_result_repo = dependency_bootstrapper.get(interface=ExtensionResultRepository)

        run_document = (
            ExtensionRunBuilder()
            .with_user_uuid(user_uuid)
            .with_timestamp(
                datetime.fromtimestamp(
                    timestamp=random.randint(
                        int(datetime(year=2023, month=1, day=1).timestamp()),
                        int(datetime(year=2023, month=12, day=31).timestamp()),
                    ),
                    tz=ZoneInfo("UTC"),
                )
            )
            .with_extension_id(extension_id)
            .build()
        )

        inserted_extension_runs = await extension_run_repo.insert(extension_runs=[run_document])

        result_documents = await extension_result_repo.insert(
            extension_results=[
                ExtensionResultBuilder()
                .with_user_uuid(user_uuid)
                .with_timestamp(
                    datetime.fromtimestamp(
                        timestamp=random.randint(
                            int(datetime(year=2023, month=1, day=1).timestamp()),
                            int(datetime(year=2023, month=12, day=31).timestamp()),
                        ),
                        tz=ZoneInfo("UTC"),
                    )
                )
                .with_extension_id(extension_id)
                .build()
                for _ in range(random.randint(1, 5))
            ],
            parent_id=run_document.id,
        )

        yield run_document, result_documents, user, extension_id

        # Teardown
        _ = await extension_result_repo.delete_by_id(ids=[child.id for child in result_documents])
        _ = await extension_run_repo.delete_by_id(ids=[run.id for run in inserted_extension_runs])

    async def test_list_extension_runs_endpoint(
        self,
        user_with_extension_run_data,
    ):
        # Arrange
        inserted_extension_runs, user, extension_id = user_with_extension_run_data
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            ExtensionEndpointUrls.LIST_RUNS,
            {"limit": 100, "extension_ids": [extension_id]},
        )
        # Act
        response = await _call_get_endpoint(request_url, headers=headers)

        assert response.status_code == status.HTTP_200_OK
        result = response.json()
        items = result["items"]

        assert len(items) == len(inserted_extension_runs)
        for fetched_object in items:
            assert fetched_object["id"] in [str(run.id) for run in inserted_extension_runs]

    async def test_list_extension_results_endpoint(
        self,
        user_with_extension_run_and_results_data,
    ):
        # Arrange
        inserted_extension_runs, parent_child_map, user, _ = user_with_extension_run_and_results_data
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        for run_id, outputs in parent_child_map.items():
            request_url = join_as_url(
                ExtensionEndpointUrls.LIST_RESULTS,
                {"run_id": run_id},
            )

            # Act
            response = await _call_get_endpoint(request_url, headers=headers)

            assert response.status_code == status.HTTP_200_OK
            result = response.json()
            items = result["items"]

            assert len(items) == len(outputs)
            out_ids = [str(output.id) for output in outputs]
            for fetched_object in items:
                assert fetched_object["id"] in out_ids

    async def test_list_extension_results_endpoint_result_id_as_run_raises_bad_request(
        self,
        user_with_single_extension_run_and_results_data,
    ):
        # Arrange
        extension_run, results, user, _ = user_with_single_extension_run_and_results_data
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            ExtensionEndpointUrls.LIST_RESULTS,
            {"run_id": random.choice([res.id for res in results])},  # picks a result id instead of run_id
        )

        # Act
        response = await _call_get_endpoint(request_url, headers=headers)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    async def test_list_extension_results_endpoint_raises_no_content_error(
        self, user_with_single_extension_run_and_results_data
    ):
        # Arrange
        extension_run, results, user, _ = user_with_single_extension_run_and_results_data
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            ExtensionEndpointUrls.LIST_RESULTS,
            {"run_id": uuid4()},
        )

        # Act
        response = await _call_get_endpoint(request_url, headers=headers)

        assert response.status_code == status.HTTP_204_NO_CONTENT
