from typing import Dict

import pytest
from pydantic import ValidationError

from services.base.application.database.models.sorts import Sort, SortOrder
from services.data_service.v02.api.utils.get_sort_input import get_sort_input


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        (
            {"sort_field": "timestamp", "sort_order": "descending"},
            (
                Sort(
                    name="timestamp",
                    order=SortOrder.DESCENDING,
                )
            ),
        ),
        (
            {"sort_field": "system_properties.created_at", "sort_order": "descending"},
            (
                Sort(
                    name="system_properties.created_at",
                    order=SortOrder.DESCENDING,
                )
            ),
        ),
        (
            {"sort_field": "system_properties.created_at", "sort_order": "ascending"},
            (
                Sort(
                    name="system_properties.created_at",
                    order=SortOrder.ASCENDING,
                )
            ),
        ),
    ],
)
def test_get_sort_input_should_pass(test_input: Dict, expected_result: Sort):
    range_input = get_sort_input(**test_input)
    assert range_input.name == expected_result.name
    assert range_input.order == expected_result.order


@pytest.mark.parametrize(
    "test_input",
    [
        {"sort_field": "system_properties.created_at", "sort_order": "descenting"},
    ],
)
def test_get_sort_input_expected_validation_error(test_input: Dict):
    with pytest.raises(ValidationError):
        get_sort_input(**test_input)
