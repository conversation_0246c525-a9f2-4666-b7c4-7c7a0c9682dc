import random
from datetime import datetime, timezone
from typing import AsyncGenerator, Sequence

import pytest

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.models.filter_types import UserUUIDTermsFilter
from services.base.application.database.models.filters import Filters
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.enums.consumed_nutrition_type import ConsumedNutritionType
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.enums.metadata import Organization
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.temp_plan_repository import TempPlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsConsumablesExtension
from services.base.domain.schemas.events.plan import Plan
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.temp_plan import TempPlan, TempPlanMetadata
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.validations.validate_rrule import validate_rrule
from services.base.infrastructure.database.opensearch.migrations.scripts.diary_events_migration.migrate_diary_events import (
    DiaryEventsMigrator,
)
from services.base.infrastructure.database.opensearch.migrations.scripts.diary_events_migration.migrate_temp_plan_with_events import (
    MigrateTempPlanWithEvents,
    RRuleMapping,
)
from services.base.tests.domain.builders.diary_event_builder import (
    DiaryEventBuilder,
    DiaryEventsConsumablesExtensionBuilder,
)
from services.base.tests.domain.builders.temp_plan_builder import TempPlanBuilder
from services.base.tests.domain.builders.temp_plan_diary_event_builder import TempPlanDiaryEventBuilder


class TestMigrateTempPlanToPlan:
    TARGET_DATA_TYPES = [DiaryEventType.FOOD, DiaryEventType.DRINK, DiaryEventType.SUPPLEMENT]

    @staticmethod
    def _generate_random_consumable_extension(diary_event_type: DiaryEventType) -> DiaryEventsConsumablesExtension:
        assert diary_event_type in (DiaryEventType.DRINK, DiaryEventType.FOOD, DiaryEventType.SUPPLEMENT)
        quantity_type = PrimitiveTypesGenerator.generate_random_enum(enum_type=ConsumedNutritionType)
        if quantity_type == ConsumedNutritionType.UNITS:
            if diary_event_type == DiaryEventType.DRINK:
                units = random.choice(["Liter", "Milliliter", "Tablespoon", "Quart", "Pint"])
            else:
                units = random.choice(["Liter", "Milliliter", "Tablespoon", "Pint", "Milligram", "Gram", "Pound"])
            amount = PrimitiveTypesGenerator.generate_random_float(min_value=1, max_value=1000)
            return (
                DiaryEventsConsumablesExtensionBuilder()
                .with_quantity_type(quantity_type)
                .with_units(units)
                .with_amount(amount)
                .build()
            )
        else:
            return DiaryEventsConsumablesExtensionBuilder().with_quantity_type(quantity_type).build()

    @pytest.fixture(scope="function")
    async def temp_plans_and_diary_events(
        self,
    ) -> AsyncGenerator[tuple[Sequence[TempPlan], Sequence[DiaryEvents]], None]:
        bootstrapper = DependencyBootstrapper().build()
        temp_plan_repo: TempPlanRepository = bootstrapper.get(interface=TempPlanRepository)
        deprecated_event_repo: DeprEventRepository = bootstrapper.get(interface=DeprEventRepository)

        user_uuid = PrimitiveTypesGenerator.generate_random_uuid()

        random_period: RRuleMapping = PrimitiveTypesGenerator.generate_random_enum(enum_type=RRuleMapping)
        interval_period = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5)
        if random_period == RRuleMapping.MINUTELY:
            # 60 minutes is the minimum interval for rrule
            interval = interval_period * random_period.value * 60
        else:
            interval = interval_period * random_period.value

        events: list[DiaryEvents] = []
        plans: list[TempPlan] = []
        for diary_event_type in TestMigrateTempPlanToPlan.TARGET_DATA_TYPES:
            for _ in range(3):
                if diary_event_type in (DiaryEventType.DRINK, DiaryEventType.FOOD, DiaryEventType.SUPPLEMENT):
                    ce = TestMigrateTempPlanToPlan._generate_random_consumable_extension(diary_event_type)
                else:
                    ce = None
                temp_plan = (
                    TempPlanBuilder()
                    .with_interval(interval=interval)
                    .with_metadata(metadata=TempPlanMetadata(user_uuid=user_uuid))
                    .with_plan_events(
                        plan_events=[
                            TempPlanDiaryEventBuilder()
                            .with_type(diary_event_type)
                            .with_consumable_extension(ce=ce)
                            .build()
                        ]
                    )
                    .build()
                )
                plans.append(temp_plan)
                new_events = (
                    DiaryEventBuilder()
                    .with_type(diary_event_type)
                    .with_consumable_extension(ce=ce)
                    .with_plan_id(plan_id=temp_plan.id)
                    .with_name(name=temp_plan.name_of_plan)
                    .with_user_uuid(user_uuid=user_uuid)
                    .with_organization(organization=Organization.BEST_LIFE)
                    .build_n(10)
                )
                events.extend(new_events)

        stored_plans = await temp_plan_repo.insert(plans=plans, force_strong_consistency=True)
        stored_events = await deprecated_event_repo.insert(models=events, force_strong_consistency=True)
        assert len(stored_plans) == len(plans)
        assert len(stored_events) == len(events)

        yield stored_plans, stored_events

        filters = Filters()
        filters.must_filters.with_filters([UserUUIDTermsFilter(value=[str(user_uuid)])])

        await deprecated_event_repo.delete_by_query(user_uuid=user_uuid, data_schema=DiaryEvents, filters=filters)

        await temp_plan_repo.delete_by_id(ids=[p.id for p in stored_plans])

    @pytest.mark.integration
    @pytest.mark.skip
    async def test_migrate_temp_plan_to_plan_does_not_return_value_error(self, temp_plans_and_diary_events):
        try:
            for type in [DiaryEventType.FOOD, DiaryEventType.DRINK, DiaryEventType.SUPPLEMENT]:
                await DiaryEventsMigrator.migrate(diary_event_type=type, dry_run=True)
        except Exception:
            pytest.fail("run_migration should not raise ValueError")

    @pytest.mark.skip
    async def test_migrate_temp_plan_to_plan_creates_new_plan_and_template(self, temp_plans_and_diary_events):
        bootstrapper = DependencyBootstrapper().build()
        plan_repo = bootstrapper.get(interface=PlanRepository)
        template_repo = bootstrapper.get(interface=TemplateRepository)

        for diary_event in TestMigrateTempPlanToPlan.TARGET_DATA_TYPES:
            await DiaryEventsMigrator.migrate(diary_event_type=diary_event, dry_run=False)

        temp_plans, diary_events = temp_plans_and_diary_events

        # Find all plans created by the migration based on the name of the temp plan
        plans_query = SingleDocumentTypeQuery(
            query=AndQuery(
                queries=[
                    ValuesQuery(
                        field_name="name",
                        values=[p.name_of_plan for p in temp_plans],
                    )
                ]
            ),
            domain_type=Plan,
        )
        plans = await plan_repo.search_by_query(query=plans_query, size=len(temp_plans))
        assert len(plans.documents) == len(temp_plans)

        # Find all templates created by the migration based on the name of the temp plan
        templates = await template_repo.search_by_query(
            query=SingleDocumentTypeQuery(
                query=ValuesQuery(field_name="name", values=[p.name_of_plan for p in temp_plans]),
                domain_type=EventTemplate,
            ).to_query(),
            size=len(temp_plans),
        )
        assert len(templates.documents) == len(temp_plans)

        await plan_repo.delete_by_id(ids=[p.id for p in plans.documents])
        await template_repo.delete_by_id(ids=[t.id for t in templates.documents])

    async def test_get_rrule_from_temp_plan(self):
        # Test cases with different intervals
        test_cases = [
            {
                "interval": RRuleMapping.YEARLY.value,  # 1 year
                "expected_freq": "YEARLY",
                "expected_interval": None,  # Interval of 1 should not be included
            },
            {
                "interval": RRuleMapping.MONTHLY.value * 2,  # 2 months
                "expected_freq": "MONTHLY",
                "expected_interval": 2,
            },
            {
                "interval": RRuleMapping.WEEKLY.value * 3,  # 3 weeks
                "expected_freq": "WEEKLY",
                "expected_interval": 3,
            },
            {
                "interval": RRuleMapping.DAILY.value * 4,  # 4 days
                "expected_freq": "DAILY",
                "expected_interval": 4,
            },
            {
                "interval": RRuleMapping.HOURLY.value * 5,  # 5 hours
                "expected_freq": "HOURLY",
                "expected_interval": 5,
            },
            {
                "interval": 5270400,  # 61 days
                "expected_freq": "DAILY",
                "expected_interval": 61,
            },
        ]

        start_time = datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
        # Format the datetime in the compact format used by rrule
        expected_dtstart = start_time.isoformat()

        for test_case in test_cases:
            rule = MigrateTempPlanWithEvents._get_rrule_from_temp_plan(
                start_time=start_time, interval_seconds=test_case["interval"]
            )

            assert validate_rrule(rule=rule)
            rule_str = str(rule)
            assert f"FREQ={test_case['expected_freq']}" in rule_str
            if test_case["expected_interval"] is not None:
                assert f"INTERVAL={test_case['expected_interval']}" in rule_str
            else:
                assert "INTERVAL=" not in rule_str
            assert f"DTSTART:{expected_dtstart}" in rule_str
