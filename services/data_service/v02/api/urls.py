from dataclasses import dataclass

from services.data_service.v02.api.activity_endpoints import activity_router
from services.data_service.v02.api.data_summary_endpoints import summary_router
from services.data_service.v02.api.delete_endpoints import delete_router
from services.data_service.v02.api.diary_endpoints import diary_router
from services.data_service.v02.api.location_endpoints import location_router
from services.data_service.v02.api.measure_endpoints import measure_router
from services.data_service.v02.api.update_endpoints import update_router
from services.data_service.v02.constants import (
    ActivityEndpointRoutes,
    DataSummaryEndpointRoutes,
    DeleteEndpointRoutes,
    DiaryEndpointRoutes,
    LocationEndpointRoutes,
    MeasureEndpointRoutes,
    UpdateEndpointRoutes,
)


@dataclass(frozen=True)
class ActivityEndpointUrls:
    SLEEP = f"{activity_router.prefix}{ActivityEndpointRoutes.SLEEP}"
    STEPS = f"{activity_router.prefix}{ActivityEndpointRoutes.STEPS}"
    INTERACT_SOCIAL_TOTALS_IN_TIME = f"{activity_router.prefix}{ActivityEndpointRoutes.INTERACT_SOCIAL_TOTALS_IN_TIME}"
    INTERACT_SOCIAL_TOTALS_PER_PERSON = (
        f"{activity_router.prefix}{ActivityEndpointRoutes.INTERACT_SOCIAL_TOTALS_PER_PERSON}"
    )
    INTERACT_SOCIAL_TOTALS_PER_PERSON_GROUPED = (
        f"{activity_router.prefix}{ActivityEndpointRoutes.INTERACT_SOCIAL_TOTALS_PER_PERSON_GROUPED}"
    )
    SHOPPING_ACTIVITY = f"{activity_router.prefix}{ActivityEndpointRoutes.SHOPPING_ACTIVITY}"


@dataclass(frozen=True)
class MeasureEndpointUrls:
    HEART_RATE = f"{measure_router.prefix}{MeasureEndpointRoutes.HEART_RATE}"
    RESTING_HEART_RATE = f"{measure_router.prefix}{MeasureEndpointRoutes.RESTING_HEART_RATE}"


@dataclass(frozen=True)
class DiaryEndpointUrls:
    EVENTS = f"{diary_router.prefix}{DiaryEndpointRoutes.EVENTS}"
    EVENTS_UNIQUE = f"{diary_router.prefix}{DiaryEndpointRoutes.EVENTS_UNIQUE}"
    RATINGS = f"{diary_router.prefix}{DiaryEndpointRoutes.RATINGS}"
    NOTES = f"{diary_router.prefix}{DiaryEndpointRoutes.NOTES}"


@dataclass(frozen=True)
class UpdateEndpointUrls:
    BY_ID = f"{update_router.prefix}{UpdateEndpointRoutes.BY_ID}"


@dataclass(frozen=True)
class DeleteEndpointUrls:
    BY_ID = f"{delete_router.prefix}{DeleteEndpointRoutes.BY_ID}"
    BY_FILTER = f"{delete_router.prefix}{DeleteEndpointRoutes.BY_FILTER}"


@dataclass(frozen=True)
class LocationEndpointUrls:
    HISTORY = f"{location_router.prefix}{LocationEndpointRoutes.HISTORY}"
    PLACE = f"{location_router.prefix}{LocationEndpointRoutes.PLACE}"
    MOVEMENT = f"{location_router.prefix}{LocationEndpointRoutes.MOVEMENT}"


@dataclass(frozen=True)
class DataSummaryEndpointUrls:
    USER_DATA = f"{summary_router.prefix}{DataSummaryEndpointRoutes.USER_DATA}"
    TAG_COUNT = f"{summary_router.prefix}{DataSummaryEndpointRoutes.TAG_COUNT}"
    STATISTICS = f"{summary_router.prefix}{DataSummaryEndpointRoutes.STATISTICS}"
    BODY_PARTS = f"{summary_router.prefix}{DataSummaryEndpointRoutes.BODY_PARTS}"
