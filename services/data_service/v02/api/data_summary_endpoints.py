from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.metadata import Organization
from services.data_service.application.enums.taggable_data_type import TaggableDataType
from services.data_service.application.use_cases.aggregate_statistics_use_case import (
    AggregateStatisticsUseCase,
    AggregateStatisticsUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.data_summary_use_case import (
    DataSummaryUseCase,
    DataSummaryUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.depr_count_unique_tags_use_case import (
    DeprCountUniqueTagsUseCase,
    DeprCountUniqueTagsUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.list_body_parts_use_case import (
    ListBodyPartsOutputBoundary,
    ListBodyPartsUseCase,
)
from services.data_service.v02.api.utils.get_common_fetch_input import (
    CommonFetchInputModelOptionalInterval,
    get_common_fetch_input_optional_interval,
)
from services.data_service.v02.constants import DataServicePrefixes, DataSummaryEndpointRoutes

summary_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.DATA_SUMMARY_PREFIX}",
    tags=["info"],
    responses={404: {"description": "Not found"}},
)


@summary_router.get(
    DataSummaryEndpointRoutes.USER_DATA,
    response_model=DataSummaryUseCaseOutputBoundary,
)
async def user_data_endpoint(
    data_type: List[EventType] = Query(min_length=1),
    organization: List[Organization] = Query(min_length=1),
    current_uuid: UUID = Depends(get_current_uuid),
    use_case: DataSummaryUseCase = Injected(DataSummaryUseCase),
):
    return await use_case.execute(
        data_type=data_type,
        organizations=organization,
        user_uuid=current_uuid,
    )


@summary_router.get(DataSummaryEndpointRoutes.TAG_COUNT, response_model=DeprCountUniqueTagsUseCaseOutputBoundary)
async def tag_count_endpoint(
    # Fix typo alias eventually
    data_types: List[TaggableDataType] = Query(min_length=1, alias="data_type"),
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: DeprCountUniqueTagsUseCase = Injected(DeprCountUniqueTagsUseCase),
):
    return await use_case.execute_async(
        data_types=data_types,
        user_uuid=user_uuid,
    )


@summary_router.get(
    DataSummaryEndpointRoutes.STATISTICS,
    response_model=AggregateStatisticsUseCaseOutputBoundary,
    description="Get statistics for a given data type",
)
async def statistics_endpoint(
    data_type: EventType,
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    use_case: AggregateStatisticsUseCase = Depends(AggregateStatisticsUseCase),
):
    return await use_case.execute_async(
        event_type=data_type,
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )


@summary_router.get(
    DataSummaryEndpointRoutes.BODY_PARTS,
    response_model=ListBodyPartsOutputBoundary,
)
def body_parts_endpoint(
    _: UUID = Depends(get_current_uuid),  # All endpoints need to be authorized
    use_case: ListBodyPartsUseCase = Depends(ListBodyPartsUseCase),
):
    """Returns a key-value pair of all supported body parts with their individual numeral codes"""
    return use_case.execute()
