from typing import Union

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.common_data_response import CommonDataResponse
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.data_service.application.use_cases.aggregate_location_movement_use_case import (
    AggregateLocationMovementUseCase,
    AggregateLocationMovementUseCaseOutputItem,
)
from services.data_service.application.use_cases.aggregate_location_place_use_case import (
    AggregateLocationPlaceUseCase,
    AggregateLocationPlaceUseCaseOutputItem,
)
from services.data_service.application.use_cases.aggregate_location_use_case import (
    AggregateLocationUseCase,
    AggregateLocationUseCaseOutputItem,
)
from services.data_service.application.use_cases.list_location_use_case import (
    ListLocationUseCase,
    ListLocationUseCaseOutputItem,
)
from services.data_service.constants import DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES
from services.data_service.v02.api.utils.get_common_fetch_input import (
    CommonFetchInputModel,
    CommonFetchInputModelOptionalInterval,
    get_common_fetch_input,
    get_common_fetch_input_optional_interval,
)
from services.data_service.v02.constants import DataServicePrefixes, LocationEndpointRoutes

location_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.LOCATION_PREFIX}",
    tags=["environment"],
    responses={
        404: {"description": "Not found"},
        204: {"description": "No location data synchronized for given input."},
    },
)


@location_router.get(
    LocationEndpointRoutes.HISTORY,
    response_model=CommonDataResponse[Union[ListLocationUseCaseOutputItem, AggregateLocationUseCaseOutputItem]],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def location_history_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    location_history_aggregation_use_case: AggregateLocationUseCase = Injected(AggregateLocationUseCase),
    location_history_detail_use_case: ListLocationUseCase = Injected(ListLocationUseCase),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    use_case = location_history_aggregation_use_case if has_interval else location_history_detail_use_case
    output_boundary = AggregateLocationUseCaseOutputItem if has_interval else ListLocationUseCaseOutputItem

    # Get Location data
    output = await use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,  # pyright: ignore
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[output_boundary](
        Values=output.results,
        Status=status,
    )


@location_router.get(
    LocationEndpointRoutes.MOVEMENT,
    response_model=CommonDataResponse[AggregateLocationMovementUseCaseOutputItem],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def location_movement_endpoint(
    common_input: CommonFetchInputModel = Depends(get_common_fetch_input),
    location_movement_use_case: AggregateLocationMovementUseCase = Injected(AggregateLocationMovementUseCase),
):
    # Get Location data
    output = await location_movement_use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[AggregateLocationMovementUseCaseOutputItem](
        Values=output.results,
        Status=status,
    )


@location_router.get(
    LocationEndpointRoutes.PLACE,
    response_model=CommonDataResponse[AggregateLocationPlaceUseCaseOutputItem],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def location_place_endpoint(
    common_input: CommonFetchInputModel = Depends(get_common_fetch_input),
    location_place_use_case: AggregateLocationPlaceUseCase = Injected(AggregateLocationPlaceUseCase),
):
    # Get Location data
    output = await location_place_use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )

    return CommonDataResponse[AggregateLocationPlaceUseCaseOutputItem](
        Values=output.results,
        Status=status,
    )
