from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.common_data_response import CommonDataResponse
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.data_service.application.use_cases.aggregate_diary_events_use_case import (
    AggregateDiaryEventsOutputItem,
    AggregateDiaryEventsUseCase,
)
from services.data_service.application.use_cases.list_diary_events_use_case import (
    ListDiaryEventsOutputItem,
    ListDiaryEventsUseCase,
)
from services.data_service.application.use_cases.list_unique_diary_events_use_case import (
    ListDiaryEventsUniqueUseCase,
    ListDiaryEventsUniqueUseCaseOutputBoundary,
)
from services.data_service.constants import DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES
from services.data_service.v02.api.utils.get_common_fetch_input import (
    CommonFetchInputModelOptionalInterval,
    get_common_fetch_input_optional_interval,
)
from services.data_service.v02.constants import DataServicePrefixes, DiaryEndpointRoutes

diary_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.DIARY_PREFIX}",
    tags=["diary"],
    responses={404: {"description": "Not found"}},
)


@diary_router.get(
    DiaryEndpointRoutes.EVENTS,
    response_model=CommonDataResponse[AggregateDiaryEventsOutputItem] | CommonDataResponse[ListDiaryEventsOutputItem],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def diary_event_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    diary_events_aggregation_use_case: AggregateDiaryEventsUseCase = Injected(AggregateDiaryEventsUseCase),
    diary_events_list_use_case: ListDiaryEventsUseCase = Injected(ListDiaryEventsUseCase),
    standard: bool = Query(alias="standard", default=False),
    only_custom_data: Optional[List[str]] = Query(min_length=1, default=None),
    only_names: Optional[List[str]] = Query(min_length=1, default=None),
    only_types: Optional[List[DiaryEventType]] = Query(min_length=1, default=None),
    only_plan_ids: Optional[List[UUID]] = Query(min_length=1, default=None),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    # select the use case
    use_case = diary_events_aggregation_use_case if has_interval else diary_events_list_use_case
    output_boundary = AggregateDiaryEventsOutputItem if has_interval else ListDiaryEventsOutputItem

    output = await use_case.execute_async(
        user_uuid=common_input.user_uuid,
        only_types=only_types,
        only_custom_data=only_custom_data,
        only_plan_ids=only_plan_ids,
        only_names=only_names,
        time_input=common_input.time_input,  # pyright: ignore
        metadata=common_input.metadata,
        standard=standard,
        re_fetch=common_input.re_fetch_most_recent,
    )
    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[output_boundary](Values=output.results, Status=status)


@diary_router.get(
    DiaryEndpointRoutes.EVENTS_UNIQUE,
    response_model=ListDiaryEventsUniqueUseCaseOutputBoundary,
)
async def unique_diary_event_endpoint(
    standard: bool = Query(description="Limits response items to only events with the is_standard field set to true."),
    only_custom_data: bool = Query(default=False),
    diary_events_list_unique_use_case: ListDiaryEventsUniqueUseCase = Injected(ListDiaryEventsUniqueUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
):
    output = await diary_events_list_unique_use_case.execute(
        standard_only=standard,
        user_uuid=user_uuid,
        only_custom_data=only_custom_data,
    )

    if not output.results:
        raise NoContentException("no events found")

    return output
