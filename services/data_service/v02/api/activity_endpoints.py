from typing import Union

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.common_data_response import CommonDataResponse
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.data_service.application.use_cases.aggregate_shopping_activity_use_case import (
    AggregateShoppingActivityUseCase,
    AggregateShoppingActivityUseCaseOutputItem,
)
from services.data_service.application.use_cases.aggregate_sleep_use_case import (
    AggregateSleepUseCase,
    AggregateSleepUseCaseOutputItem,
)
from services.data_service.application.use_cases.aggregate_steps_use_case import (
    AggregateStepsUseCase,
    AggregateStepsUseCaseOutputItem,
)
from services.data_service.application.use_cases.list_shopping_activity_use_case import (
    ListShoppingActivityUseCase,
    ListShoppingActivityUseCaseOutputItem,
)
from services.data_service.application.use_cases.list_sleep_use_case import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Case,
    ListSleepUseCaseOutputItem,
)
from services.data_service.application.use_cases.list_steps_use_case import (
    ListStepsUseCase,
    ListStepsUseCaseOutputItem,
)
from services.data_service.constants import DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES
from services.data_service.v02.api.utils.get_common_fetch_input import (
    CommonFetchInputModelOptionalInterval,
    get_common_fetch_input_optional_interval,
)
from services.data_service.v02.constants import ActivityEndpointRoutes, DataServicePrefixes

activity_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION2_PREFIX}{DataServicePrefixes.ACTIVITY_PREFIX}",
    tags=["activity"],
    responses={404: {"description": "Not found"}},
)


@activity_router.get(
    ActivityEndpointRoutes.SLEEP,
    response_model=CommonDataResponse[Union[ListSleepUseCaseOutputItem, AggregateSleepUseCaseOutputItem]],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
    description=(
        "**NOTE:** when interval is provided response `Values` contain `SleepAggregationOut` (aggregation results),"
        + " otherwise `SleepOut` (details)"
    ),
)
async def sleep_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    sleep_detail_use_case: ListSleepUseCase = Injected(ListSleepUseCase),
    sleep_aggregation_use_case: AggregateSleepUseCase = Injected(AggregateSleepUseCase),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    target_use_case = sleep_aggregation_use_case if has_interval else sleep_detail_use_case

    # get the data
    output = await target_use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,  # pyright: ignore
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[AggregateSleepUseCaseOutputItem if has_interval else ListSleepUseCaseOutputItem](
        Values=output.results, Status=status
    )


@activity_router.get(
    ActivityEndpointRoutes.STEPS,
    response_model=CommonDataResponse[Union[AggregateStepsUseCaseOutputItem, ListStepsUseCaseOutputItem]],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
    description=(
        "**NOTE:** if you provide `interval` data will be aggregated (averaged steps),"
        + "if not you will get detailed data"
    ),
)
async def steps_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    list_steps_use_case: ListStepsUseCase = Injected(ListStepsUseCase),
    aggregate_steps_use_case: AggregateStepsUseCase = Injected(AggregateStepsUseCase),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    target_use_case = aggregate_steps_use_case if has_interval else list_steps_use_case

    output = await target_use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,  # pyright: ignore
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[Union[AggregateStepsUseCaseOutputItem, ListStepsUseCaseOutputItem]](
        Values=output.results, Status=status
    )


@activity_router.get(
    ActivityEndpointRoutes.SHOPPING_ACTIVITY,
    response_model=CommonDataResponse[ListShoppingActivityUseCaseOutputItem]
    | CommonDataResponse[AggregateShoppingActivityUseCaseOutputItem],
    response_model_exclude={"Values": {"__all__": {DocumentLabels.METADATA: {DocumentLabels.USER_UUID}}}},
)
async def shopping_activity_endpoint(
    common_input: CommonFetchInputModelOptionalInterval = Depends(get_common_fetch_input_optional_interval),
    detail_use_case: ListShoppingActivityUseCase = Injected(ListShoppingActivityUseCase),
    aggregation_use_case: AggregateShoppingActivityUseCase = Injected(AggregateShoppingActivityUseCase),
):
    has_interval = isinstance(common_input.time_input, TimeInput)
    use_case = aggregation_use_case if has_interval else detail_use_case
    output_boundary = (
        AggregateShoppingActivityUseCaseOutputItem if has_interval else ListShoppingActivityUseCaseOutputItem
    )

    output = await use_case.execute_async(
        user_uuid=common_input.user_uuid,
        time_input=common_input.time_input,  # pyright: ignore
        re_fetch=common_input.re_fetch_most_recent,
        metadata=common_input.metadata,
    )

    if not output.results:
        raise NoContentException("no data available for the given time range")

    status = (
        {DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES: output.re_fetch_time_input} if output.re_fetch_time_input else {}
    )
    return CommonDataResponse[output_boundary](Values=output.results, Status=status)
