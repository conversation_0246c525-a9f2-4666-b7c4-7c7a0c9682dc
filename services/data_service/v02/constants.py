from dataclasses import dataclass


@dataclass(frozen=True)
class DataServicePrefixes:
    VERSION2_PREFIX = "/api/v0.2"

    ACTIVITY_PREFIX = "/activity"
    MEASURE_PREFIX = "/measure"
    UPDATE_PREFIX = "/update"
    LOCATION_PREFIX = "/location"
    DATA_SUMMARY_PREFIX = "/summary"
    DELETE_PREFIX = "/delete"
    FETCH_PREFIX = "/fetch"
    DIARY_PREFIX = "/diary"


@dataclass(frozen=True)
class ActivityEndpointRoutes:
    SLEEP = "/sleep/"
    STEPS = "/steps/"
    INTERACT_SOCIAL_TOTALS_IN_TIME = "/interact_social_totals_in_time/"
    INTERACT_SOCIAL_TOTALS_PER_PERSON = "/interact_social_totals_per_person/"
    INTERACT_SOCIAL_TOTALS_PER_PERSON_GROUPED = "/interact_social_totals_per_person_grouped/"
    SHOPPING_ACTIVITY = "/shopping_activity/"


@dataclass(frozen=True)
class MeasureEndpointRoutes:
    HEART_RATE = "/heart_rate/"
    RESTING_HEART_RATE = "/resting_heart_rate/"


@dataclass(frozen=True)
class DiaryEndpointRoutes:
    EVENTS = "/events/"
    EVENTS_UNIQUE = "/events/unique/"
    RATINGS = "/ratings/"
    NOTES = "/notes/"


@dataclass(frozen=True)
class LocationEndpointRoutes:
    HISTORY = "/history/"
    MOVEMENT = "/movement/"
    PLACE = "/place/"


@dataclass(frozen=True)
class DataSummaryEndpointRoutes:
    USER_DATA = "/user_data/"
    TAG_COUNT = "/tag_count/"
    STATISTICS = "/statistics/"
    BODY_PARTS = "/body_parts/"


@dataclass(frozen=True)
class DeleteEndpointRoutes:
    BY_ID = "/by_id/"
    BY_FILTER = "/by_filter/"


@dataclass(frozen=True)
class UpdateEndpointRoutes:
    BY_ID = "/by_id/"


@dataclass(frozen=True)
class FetchEndpointRoutes:
    BY_ID = "/by_id/"
