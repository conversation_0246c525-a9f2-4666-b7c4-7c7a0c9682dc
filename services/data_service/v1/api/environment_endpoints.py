from typing import Annotated
from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from fastapi_injector import Injected
from starlette import status
from starlette.responses import Response

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDataResponse
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.schemas.environment import SpaceTimeInput
from services.data_service.application.models.air_quality_bucket import AirQualityBucket
from services.data_service.application.models.pollen_bucket import PollenBucket
from services.data_service.application.models.weather_bucket import WeatherBucket
from services.data_service.application.use_cases.environment.aggregate_air_quality_use_case import (
    AggregateAirQualityUseCase,
    AggregateAirQualityUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.environment.aggregate_pollen_use_case import (
    AggregatePollenUseCase,
    AggregatePollenUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.environment.aggregate_weather_use_case import (
    AggregateWeatherUseCase,
    AggregateWeatherUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.environment.fetch_environment_by_spacetime_use_case import (
    FetchEnvironmentBySpaceTimeUseCase,
)
from services.data_service.application.use_cases.environment.forecast_environment_use_case import (
    ForecastEnvironmentUseCase,
)
from services.data_service.v1.api.constants import DataServicePrefixes, EnvironmentEndpointRoutes
from services.data_service.v1.api.models.output.environment.air_quality_aggregation_api_output import (
    AirQualityAggregationAPIOutput,
)
from services.data_service.v1.api.models.output.environment.pollen_aggregation_api_output import (
    PollenAggregationAPIOutput,
)
from services.data_service.v1.api.models.output.environment.weather_aggregation_api_output import (
    WeatherAggregationAPIOutput,
)

environment_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION1_PREFIX}{DataServicePrefixes.ENVIRONMENT_PREFIX}",
    tags=["environment"],
    responses={
        404: {"description": "Not found"},
    },
)


@environment_router.get(EnvironmentEndpointRoutes.AIR_QUALITY, status_code=200)
async def get_air_quality_aggregated(
    response: Response,
    time_input: Annotated[TimeInput, Query()],
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: AggregateAirQualityUseCase = Injected(AggregateAirQualityUseCase),
) -> AirQualityAggregationAPIOutput:
    """
    Get aggregated air quality data based on a given time range input with a provided interval as periodic buckets.

    Returns:
        AirQualityAggregationAPIOutput: Aggregated air quality data.
    """
    output: AggregateAirQualityUseCaseOutputBoundary = await use_case.execute_async(
        user_uuid=user_uuid,
        time_input=time_input,
    )
    if output.partial_success:
        response.status_code = status.HTTP_206_PARTIAL_CONTENT

    return AirQualityAggregationAPIOutput(data=output.items)


@environment_router.get(EnvironmentEndpointRoutes.WEATHER, status_code=200)
async def get_weather_aggregated(
    response: Response,
    time_input: Annotated[TimeInput, Query()],
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: AggregateWeatherUseCase = Injected(AggregateWeatherUseCase),
) -> WeatherAggregationAPIOutput:
    """
    Get aggregated weather data based on a given time range input with a provided interval as periodic buckets.

    Returns:
        WeatherAggregationAPIOutput: Aggregated air quality data.
    """
    output: AggregateWeatherUseCaseOutputBoundary = await use_case.execute_async(
        user_uuid=user_uuid,
        time_input=time_input,
    )
    if output.partial_success:
        response.status_code = status.HTTP_206_PARTIAL_CONTENT

    return WeatherAggregationAPIOutput(data=output.items)


@environment_router.get(EnvironmentEndpointRoutes.POLLEN, status_code=200)
async def get_pollen_aggregated(
    response: Response,
    time_input: Annotated[TimeInput, Query()],
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: AggregatePollenUseCase = Injected(AggregatePollenUseCase),
) -> PollenAggregationAPIOutput:
    """
    Get aggregated pollen data based on a given time range input with a provided interval as periodic buckets.

    Returns:
        PollenAggregationAPIOutput: Aggregated air quality data.
    """
    output: AggregatePollenUseCaseOutputBoundary = await use_case.execute_async(
        user_uuid=user_uuid,
        time_input=time_input,
    )
    if output.partial_success:
        response.status_code = status.HTTP_206_PARTIAL_CONTENT

    return PollenAggregationAPIOutput(data=output.items)


@environment_router.get(EnvironmentEndpointRoutes.FORECAST_AIR_QUALITY, status_code=200)
async def get_aq_forecast(
    space_time: Annotated[SpaceTimeInput, Query()],
    _: UUID = Depends(get_current_uuid),
    use_case: ForecastEnvironmentUseCase = Injected(ForecastEnvironmentUseCase),
) -> CommonDataResponse[AirQualityBucket]:
    result = await use_case.execute_async(
        environment_model=AirQualityBucket,
        space_time=space_time,
    )
    if not result.data:
        raise NoContentException(message="no data available for the given time range")
    return CommonDataResponse[AirQualityBucket](data=result.data)


@environment_router.get(EnvironmentEndpointRoutes.FORECAST_WEATHER, status_code=200)
async def get_weather_forecast(
    space_time: Annotated[SpaceTimeInput, Query()],
    _: UUID = Depends(get_current_uuid),
    use_case: ForecastEnvironmentUseCase = Injected(ForecastEnvironmentUseCase),
) -> CommonDataResponse[WeatherBucket]:
    result = await use_case.execute_async(
        environment_model=WeatherBucket,
        space_time=space_time,
    )
    if not result.data:
        raise NoContentException(message="no data available for the given time range")
    return CommonDataResponse[WeatherBucket](data=result.data)


@environment_router.get(EnvironmentEndpointRoutes.FORECAST_POLLEN, status_code=200)
async def get_pollen_forecast(
    space_time: Annotated[SpaceTimeInput, Query()],
    _: UUID = Depends(get_current_uuid),
    use_case: ForecastEnvironmentUseCase = Injected(ForecastEnvironmentUseCase),
) -> CommonDataResponse[PollenBucket]:
    result = await use_case.execute_async(
        environment_model=PollenBucket,
        space_time=space_time,
    )
    if not result.data:
        raise NoContentException(message="no data available for the given time range")
    return CommonDataResponse[PollenBucket](data=result.data)


@environment_router.get(EnvironmentEndpointRoutes.AIR_QUALITY_BY_SPACETIME, status_code=200)
async def get_air_quality_by_spacetime(
    space_time: Annotated[SpaceTimeInput, Query()],
    _: UUID = Depends(get_current_uuid),
    use_case: FetchEnvironmentBySpaceTimeUseCase = Injected(FetchEnvironmentBySpaceTimeUseCase),
) -> CommonDataResponse[AirQualityBucket]:
    result = await use_case.execute_async(
        space_time=space_time,
        environment_model=AirQualityBucket,
    )
    if not result.data:
        raise NoContentException(message="no data available for the given time range")
    return CommonDataResponse[AirQualityBucket](data=result.data)


@environment_router.get(EnvironmentEndpointRoutes.WEATHER_BY_SPACETIME, status_code=200)
async def get_weather_by_spacetime(
    space_time: Annotated[SpaceTimeInput, Query()],
    _: UUID = Depends(get_current_uuid),
    use_case: FetchEnvironmentBySpaceTimeUseCase = Injected(FetchEnvironmentBySpaceTimeUseCase),
) -> CommonDataResponse[WeatherBucket]:
    result = await use_case.execute_async(
        space_time=space_time,
        environment_model=WeatherBucket,
    )
    if not result.data:
        raise NoContentException(message="no data available for the given time range")
    return CommonDataResponse[WeatherBucket](data=result.data)


@environment_router.get(EnvironmentEndpointRoutes.POLLEN_BY_SPACETIME, status_code=200)
async def get_pollen_by_spacetime(
    space_time: Annotated[SpaceTimeInput, Query()],
    _: UUID = Depends(get_current_uuid),
    use_case: FetchEnvironmentBySpaceTimeUseCase = Injected(FetchEnvironmentBySpaceTimeUseCase),
) -> CommonDataResponse[PollenBucket]:
    result = await use_case.execute_async(
        space_time=space_time,
        environment_model=PollenBucket,
    )
    if not result.data:
        raise NoContentException(message="no data available for the given time range")
    return CommonDataResponse[PollenBucket](data=result.data)
