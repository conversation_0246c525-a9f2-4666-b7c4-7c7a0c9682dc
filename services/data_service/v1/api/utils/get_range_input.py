from typing import Optional

from fastapi import Query
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from services.base.application.database.models.filter_types import (
    CreatedAtRangeFilter,
    RangeFilter,
    TimestampRangeFilter,
    UpdatedAtRangeFilter,
)
from services.base.domain.constants.document_labels import DocumentLabels


def get_range_input(
    range_field: Optional[str] = Query(default=None, min_length=1),
    gte: Optional[str] = Query(default=None, min_length=1),
    lte: Optional[str] = Query(default=None, min_length=1),
) -> Optional[RangeFilter]:
    if not range_field:
        if any((gte, lte)):
            raise RequestValidationError("gte and lte cannot be defined without range field.")
        return None

    range_filter_type = {
        DocumentLabels.TIMESTAMP: TimestampRangeFilter,
        f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}": CreatedAtRangeFilter,
        f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.UPDATED_AT}": UpdatedAtRangeFilter,
    }.get(range_field, RangeFilter)
    try:
        return range_filter_type(name=range_field, gte=gte, lte=lte)
    except ValidationError as error:
        raise RequestValidationError(error.errors())
