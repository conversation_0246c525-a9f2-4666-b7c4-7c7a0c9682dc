import json

from services.base.application.exceptions import BadRequestException
from services.base.application.utils.encoders import decode_base_64, encode_base_64
from services.data_service.application.models.event_feed_continuation_token import EventFeedContinuationToken
from services.data_service.v1.api.query_marshaller import QueryMarshaller


class ContinuationTokenMarshaller:

    @staticmethod
    def encode_event_feed_continuation_token(token: EventFeedContinuationToken) -> str:
        token_as_dict = {"token": token.token, "query": QueryMarshaller.serialize(token.query)}
        token_as_json = json.dumps(token_as_dict)
        return encode_base_64(token_as_json)

    @staticmethod
    def decode_event_feed_continuation_token(token_in_base_64: str) -> EventFeedContinuationToken:
        try:
            token_as_json = decode_base_64(token_in_base_64)
            token_as_dict = json.loads(token_as_json)
            return EventFeedContinuationToken(
                token=token_as_dict["token"], query=QueryMarshaller.deserialize(token_as_dict["query"])
            )
        except Exception:
            raise BadRequestException("Unable to decode continuation token")
