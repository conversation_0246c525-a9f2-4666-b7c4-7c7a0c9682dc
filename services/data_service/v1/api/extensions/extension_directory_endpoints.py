from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.application.use_cases.extensions.get_extension_detail_use_case import (
    GetExtensionDetailUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_details_use_case import (
    ListExtensionDetailsUseCase,
)
from services.data_service.application.use_cases.extensions.subscribe_extension_use_case import (
    SubscribeExtensionUseCase,
)
from services.data_service.application.use_cases.extensions.unsubscribe_extension_use_case import (
    UnsubscribeExtensionUseCase,
)
from services.data_service.v1.api.constants import DataServicePrefixes, ExtensionDirectoryEndpointRoutes
from services.data_service.v1.api.models.output.extensions.extension_detail_and_schema_api_output import (
    ExtensionDetailAndSchemaAPIOutput,
)
from services.data_service.v1.api.models.output.extensions.extension_detail_api_output import ExtensionDetailAPIOutput
from services.data_service.v1.api.models.request.extensions.subscribe_extension_request_input import (
    SubscribeExtensionRequestInput,
)
from services.data_service.v1.api.models.response.extensions.list_extension_details_response import (
    ListExtensionDetailsResponse,
)
from services.data_service.v1.api.models.response.extensions.subscribe_extension_response import (
    SubscribeExtensionResponse,
)
from services.data_service.v1.api.models.response.extensions.unsubscribe_extension_response import (
    UnsubscribeExtensionResponse,
)

extension_directory_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION1_PREFIX}{DataServicePrefixes.EXTENSION_DIRECTORY_PREFIX}",
    tags=["extensions", "directory"],
    responses={404: {"description": "Not found"}},
)


@extension_directory_router.get(ExtensionDirectoryEndpointRoutes.BASE)
async def get_extension_detail_endpoint(
    extension_id: UUID,
    _: UUID = Depends(get_current_uuid),
    use_case: GetExtensionDetailUseCase = Injected(GetExtensionDetailUseCase),
) -> ExtensionDetailAndSchemaAPIOutput:
    """Gets registered extension detail from its id."""
    result = await use_case.execute_async(extension_id=extension_id)
    return ExtensionDetailAndSchemaAPIOutput.map(model=result)


@extension_directory_router.get(ExtensionDirectoryEndpointRoutes.LIST)
async def list_extension_details_endpoint(
    _: UUID = Depends(get_current_uuid),
    use_case: ListExtensionDetailsUseCase = Injected(ListExtensionDetailsUseCase),
) -> ListExtensionDetailsResponse:
    """List all registered extensions on LLIF platform."""
    output = await use_case.execute_async()
    return ListExtensionDetailsResponse(items=[ExtensionDetailAPIOutput.map(model=result) for result in output.results])


@extension_directory_router.post(ExtensionDirectoryEndpointRoutes.SUBSCRIBE)
async def subscribe_extension_endpoint(
    request_input: SubscribeExtensionRequestInput,
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: SubscribeExtensionUseCase = Injected(SubscribeExtensionUseCase),
) -> SubscribeExtensionResponse:
    """Subscribes extension to authorized user."""
    subscription = await use_case.execute_async(
        extension_id=request_input.extension_id,
        user_id=user_uuid,
        user_settings=request_input.user_settings,
    )
    return SubscribeExtensionResponse.map(model=subscription)


@extension_directory_router.patch(ExtensionDirectoryEndpointRoutes.UNSUBSCRIBE)
async def unsubscribe_extension_endpoint(
    extension_id: UUID,
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: UnsubscribeExtensionUseCase = Injected(UnsubscribeExtensionUseCase),
) -> UnsubscribeExtensionResponse:
    """Unsubscribes extension from authorized user."""
    results = await use_case.execute_async(extension_id=extension_id, user_id=user_uuid)
    return UnsubscribeExtensionResponse.map(model=results)
