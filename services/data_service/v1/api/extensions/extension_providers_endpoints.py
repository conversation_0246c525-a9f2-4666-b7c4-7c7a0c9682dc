from uuid import UUID

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.application.use_cases.extensions.get_extension_provider_use_case import (
    GetExtensionProviderUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_providers_use_case import (
    ListExtensionProvidersUseCase,
)
from services.data_service.v1.api.constants import (
    DataServicePrefixes,
    ExtensionProvidersEndpointRoutes,
)
from services.data_service.v1.api.models.output.extensions.extension_provider_api_output import (
    ExtensionProviderAPIOutput,
)
from services.data_service.v1.api.models.response.extensions.list_extension_providers_response import (
    ListExtensionProvidersResponse,
)

extension_providers_router = APIRouter(
    prefix=f"{DataServicePrefixes.VERSION1_PREFIX}{DataServicePrefixes.EXTENSION_PROVIDERS_PREFIX}",
    tags=["extensions", "providers"],
    responses={404: {"description": "Not found"}},
)


@extension_providers_router.get(ExtensionProvidersEndpointRoutes.LIST)
async def list_extension_providers_endpoint(
    _: UUID = Depends(get_current_uuid),
    use_case: ListExtensionProvidersUseCase = Injected(ListExtensionProvidersUseCase),
) -> ListExtensionProvidersResponse:
    """List all registered extension providers on LLIF platform."""
    output = await use_case.execute_async()
    return ListExtensionProvidersResponse(
        items=[ExtensionProviderAPIOutput.map(model=result) for result in output.results]
    )


@extension_providers_router.get(ExtensionProvidersEndpointRoutes.BASE)
async def get_extension_provider_endpoint(
    provider_id: UUID,
    _: UUID = Depends(get_current_uuid),
    use_case: GetExtensionProviderUseCase = Injected(GetExtensionProviderUseCase),
) -> ExtensionProviderAPIOutput:
    """Gets registered extension provider detail from its id."""
    provider = await use_case.execute_async(provider_id=provider_id)
    return ExtensionProviderAPIOutput.map(model=provider)
