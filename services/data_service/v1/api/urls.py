from dataclasses import dataclass

from services.data_service.v1.api.constants import (
    DataCrudEndpointRoutes,
    EnvironmentEndpointRoutes,
    ExtensionDirectoryEndpointRoutes,
    ExtensionProvidersEndpointRoutes,
    ExtensionsEndpointRoutes,
)
from services.data_service.v1.api.data_crud_endpoints import data_crud_router
from services.data_service.v1.api.environment_endpoints import environment_router
from services.data_service.v1.api.extensions.extension_directory_endpoints import extension_directory_router
from services.data_service.v1.api.extensions.extension_providers_endpoints import extension_providers_router
from services.data_service.v1.api.extensions.extension_results_endpoints import extension_router


@dataclass(frozen=True)
class EnvironmentEndpointUrls:
    AIR_QUALITY = f"{environment_router.prefix}{EnvironmentEndpointRoutes.AIR_QUALITY}"
    POLLEN = f"{environment_router.prefix}{EnvironmentEndpointRoutes.POLLEN}"
    WEATHER = f"{environment_router.prefix}{EnvironmentEndpointRoutes.WEATHER}"

    FORECAST_AIR_QUALITY = f"{environment_router.prefix}{EnvironmentEndpointRoutes.FORECAST_AIR_QUALITY}"
    FORECAST_WEATHER = f"{environment_router.prefix}{EnvironmentEndpointRoutes.FORECAST_WEATHER}"
    FORECAST_POLLEN = f"{environment_router.prefix}{EnvironmentEndpointRoutes.FORECAST_POLLEN}"

    AIR_QUALITY_BY_SPACETIME = f"{environment_router.prefix}{EnvironmentEndpointRoutes.AIR_QUALITY_BY_SPACETIME}"
    WEATHER_BY_SPACETIME = f"{environment_router.prefix}{EnvironmentEndpointRoutes.WEATHER_BY_SPACETIME}"
    POLLEN_BY_SPACETIME = f"{environment_router.prefix}{EnvironmentEndpointRoutes.POLLEN_BY_SPACETIME}"


@dataclass(frozen=True)
class ExtensionEndpointUrls:
    LIST_RUNS = f"{extension_router.prefix}{ExtensionsEndpointRoutes.LIST_RUNS}"
    LIST_RESULTS = f"{extension_router.prefix}{ExtensionsEndpointRoutes.LIST_RESULTS}"


@dataclass(frozen=True)
class ExtensionDirectoryEndpointUrls:
    BASE = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.BASE}"
    LIST = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.LIST}"
    SUBSCRIBE = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.SUBSCRIBE}"
    UNSUBSCRIBE = f"{extension_directory_router.prefix}{ExtensionDirectoryEndpointRoutes.UNSUBSCRIBE}"


@dataclass(frozen=True)
class ExtensionProviderEndpointUrls:
    BASE = f"{extension_providers_router.prefix}{ExtensionProvidersEndpointRoutes.BASE}"
    LIST = f"{extension_providers_router.prefix}{ExtensionProvidersEndpointRoutes.LIST}"


@dataclass(frozen=True)
class DataCrudEndpointsUrls:
    BASE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BASE}"
    EVENT = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.EVENT}"
    BLOOD_PRESSURE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BLOOD_PRESSURE}"
    BLOOD_SUGAR = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BLOOD_SUGAR}"
    BODY_TEMPERATURE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.BODY_TEMPERATURE}"
    HEART_RATE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.HEART_RATE}"
    NOTE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.NOTE}"
    LOCATION = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.LOCATION}"
    PULSE_OXYGEN = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.PULSE_OXYGEN}"
    RATING = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.RATING}"
    RESTING_HEART_RATE = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.RESTING_HEART_RATE}"
    SLEEP = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.SLEEP}"
    STEPS = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.STEPS}"
    WEIGHT = f"{data_crud_router.prefix}{DataCrudEndpointRoutes.WEIGHT}"
