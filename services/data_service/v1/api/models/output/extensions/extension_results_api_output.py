from typing import Literal, Optional

from pydantic import Field

from services.base.application.boundaries.documents import ExtensionResultOutputBoundary
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.enums.analytics.extension_output import ExtensionStatus
from services.base.domain.schemas.extension_output import AnalyticResultFields, ExtensionRunIdentifier
from services.data_service.v1.api.models.output.analytic_metadata_api_output import AnalyticMetadataApiOutput
from services.data_service.v1.api.models.output.extensions.extension_output_schemas_union import (
    EXTENSION_OUTPUT_SCHEMAS_UNION,
)
from services.data_service.v1.api.models.output.identifiable_document_api_output import IdentifiableDocumentAPIOutput
from services.data_service.v1.api.models.output.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)


class ExtensionRunsAPIOutput(SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput, ExtensionRunIdentifier):
    type: Literal["run"] = Field(  # pyright: ignore TODO migrate to ExtensionRun
        alias=ExtensionLabels.TYPE,
        description="Type of the extension's run, used as a parent type relationship join field.",
    )
    result_status: bool = Field(
        alias=AnalyticResultFields.RESULT_STATUS,
        description="Boolean that indicates if the extension or output has relevant results",
    )
    run_summary: Optional[NonEmptyStr] = Field(
        default=None,
        alias=AnalyticResultFields.RUN_SUMMARY,
        description="Field used to store summary data of the run as a JSON string of a Pydantic model",
    )
    extension_status: ExtensionStatus = Field(
        alias=AnalyticResultFields.EXTENSION_STATUS, description="The current status of the extension's run."
    )
    extension_input: dict = Field(alias=ExtensionLabels.EXTENSION_INPUT)
    message: NonEmptyStr = Field(
        alias=AnalyticResultFields.MESSAGE, description="Message that appears in the message inbox"
    )
    summary: NonEmptyStr = Field(alias=AnalyticResultFields.SUMMARY, description="Notification body text")
    metadata: AnalyticMetadataApiOutput = Field(alias=DocumentLabels.METADATA)


class ExtensionResultsAPIOutput(ExtensionResultOutputBoundary):
    # todo: extension cleanup
    metadata: AnalyticMetadataApiOutput = Field(alias=DocumentLabels.METADATA)  # pyright: ignore
    output: EXTENSION_OUTPUT_SCHEMAS_UNION  # pyright: ignore
