from typing import Literal

from pydantic import Field

from services.base.application.boundaries.output_models import EventOutputModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.resting_heart_rate import RestingHeartRateIdentifier, RestingHeartRateSchema


class RestingHeartRateAPIOutput(EventOutputModel, RestingHeartRateSchema, RestingHeartRateIdentifier):
    type: Literal[DataType.RestingHeartRate] = Field(alias=DocumentLabels.TYPE)
