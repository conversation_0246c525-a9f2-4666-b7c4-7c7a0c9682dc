from typing import Literal

from pydantic import Field

from services.base.application.boundaries.output_models import EventOutputModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.steps import StepsIdentifier, StepsSchema


class StepsAPIOutput(EventOutputModel, StepsSchema, StepsIdentifier):
    type: Literal[DataType.Steps] = Field(alias=DocumentLabels.TYPE)
