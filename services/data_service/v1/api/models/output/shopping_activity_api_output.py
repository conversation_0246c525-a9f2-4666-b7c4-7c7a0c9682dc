from typing import Literal

from pydantic import Field

from services.base.application.boundaries.output_models import EventOutputModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.shopping_activity import ShoppingActivityIdentifier, ShoppingActivitySchema


class ShoppingActivityAPIOutput(EventOutputModel, ShoppingActivitySchema, ShoppingActivityIdentifier):
    type: Literal[DataType.ShoppingActivity] = Field(alias=DocumentLabels.TYPE)
