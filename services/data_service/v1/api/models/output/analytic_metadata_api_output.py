from typing import Optional
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.schemas.extension_output import AnalyticMetadataFields
from services.base.domain.schemas.shared import BaseDataModel


class AnalyticMetadataApiOutput(BaseDataModel):
    extension_id: UUID = Field(alias=ExtensionLabels.EXTENSION_ID)
    provider_id: UUID = Field(alias=ExtensionLabels.PROVIDER_ID)
    important: bool = Field(alias=AnalyticMetadataFields.IMPORTANT, default=False)
    urgent: bool = Field(alias=AnalyticMetadataFields.URGENT, default=False)
    favorited_at: Optional[SerializableAwareDatetime] = Field(alias=AnalyticMetadataFields.FAVORITED_AT, default=None)
