from typing import List

from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.v1.api.models.output.extensions.extension_results_api_output import (
    ExtensionResultsAPIOutput,
    ExtensionRunsAPIOutput,
)


class ListExtensionRunsResponse(BaseDataModel):
    items: List[ExtensionRunsAPIOutput] = Field(min_length=1)
    continuation_token: str = Field(min_length=1)


class ListExtensionResultsResponse(BaseDataModel):
    items: List[ExtensionResultsAPIOutput] = Field(min_length=1)
