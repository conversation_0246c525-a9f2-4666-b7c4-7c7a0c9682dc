from dataclasses import dataclass


@dataclass(frozen=True)
class DataServicePrefixes:
    VERSION1_PREFIX = "/api/v1.0"

    ENVIRONMENT_PREFIX = "/environment"
    PLAN_PREFIX = "/plan"
    EXTENSION_PREFIX = "/extension"
    EXTENSION_RESULTS_PREFIX = "/extension/results"
    EXTENSION_DIRECTORY_PREFIX = "/extension/directory"
    EXTENSION_PROVIDERS_PREFIX = "/extension/providers"


@dataclass(frozen=True)
class EnvironmentEndpointRoutes:
    AIR_QUALITY = "/air_quality/"
    FORECAST_AIR_QUALITY = "/forecast/air_quality/"
    WEATHER = "/weather/"
    FORECAST_WEATHER = "/forecast/weather/"
    POLLEN = "/pollen/"
    FORECAST_POLLEN = "/forecast/pollen/"

    AIR_QUALITY_BY_SPACETIME = "/air_quality/spacetime/"
    WEATHER_BY_SPACETIME = "/weather/spacetime/"
    POLLEN_BY_SPACETIME = "/pollen/spacetime/"


@dataclass(frozen=True)
class DataCrudEndpointRoutes:
    BASE = "/"
    BLOOD_PRESSURE = "/blood_pressure/"
    BLOOD_SUGAR = "/blood_sugar/"
    BODY_TEMPERATURE = "/body_temperature/"
    EVENT = "/event/"
    HEART_RATE = "/heart_rate/"
    LOCATION = "/location/"
    NOTE = "/note/"
    PULSE_OXYGEN = "/pulse_oxygen/"
    RATING = "/rating/"
    RESTING_HEART_RATE = "/resting_heart_rate/"
    SLEEP = "/sleep/"
    STEPS = "/steps/"
    WEIGHT = "/weight/"


@dataclass(frozen=True)
class PlanEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    COMPLETE = "/complete/"
    ARCHIVE = "/archive/"
    TEMP = "/temp/"
    TEMP_ARCHIVE = "/temp/archive/"


@dataclass(frozen=True)
class ExtensionsEndpointRoutes:
    LIST_RUNS = "/runs/"
    LIST_RESULTS = "/results/"


@dataclass(frozen=True)
class ExtensionDirectoryEndpointRoutes:
    BASE = "/"
    LIST = "/list/"
    SUBSCRIBE = "/subscribe/"
    UNSUBSCRIBE = "/unsubscribe/"


@dataclass(frozen=True)
class ExtensionProvidersEndpointRoutes:
    BASE = "/"
    LIST = "/list/"
