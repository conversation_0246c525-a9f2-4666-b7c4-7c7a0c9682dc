from uuid import UUI<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadR<PERSON><PERSON><PERSON>xception,
    DuplicateDocumentsFound,
    Incorrect<PERSON>perationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, UseCaseEndpointRoutes
from services.data_service.api.models.request.use_case.insert_use_case_api_request_input import (
    InsertUseCaseAPIRequestInput,
)
from services.data_service.api.models.request.use_case.search_use_case_request_input import SearchUseCaseRequestInput
from services.data_service.api.models.request.use_case.update_use_case_api_request_input import (
    UpdateUseCaseAPIRequestInput,
)
from services.data_service.api.models.response.use_case.use_case_api_output import UseCaseAPIOutput
from services.data_service.application.use_cases.use_case.archive_use_case import ArchiveUseCase
from services.data_service.application.use_cases.use_case.insert_use_case import InsertUseCase
from services.data_service.application.use_cases.use_case.models.insert_use_case_input_boundary import (
    InsertUseCaseInputBoundary,
)
from services.data_service.application.use_cases.use_case.models.search_use_case_input_boundary import (
    SearchUseCaseInputBoundary,
)
from services.data_service.application.use_cases.use_case.models.update_use_case_input_boundary import (
    UpdateUseCaseInputBoundary,
)
from services.data_service.application.use_cases.use_case.search_use_case import SearchUseCase
from services.data_service.application.use_cases.use_case.update_use_case import UpdateUseCase

use_case_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.USE_CASE_PREFIX}",
    tags=["use case"],
    responses={404: {"description": "Not found"}},
)


@use_case_router.post(
    UseCaseEndpointRoutes.SEARCH,
    summary="Search use cases",
    description="Search and filter use cases with advanced query capabilities and pagination support."
)
async def search_use_cases_endpoint(
    use_case: SearchUseCase = Injected(SearchUseCase),
    input_boundary: SearchUseCaseInputBoundary = Depends(SearchUseCaseRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    """
    Search and retrieve use cases based on specified criteria.

    This endpoint allows users to search through their use cases using various filters,
    sorting options, and pagination. Use cases represent specific scenarios or workflows
    that users want to track and manage in their personal data collection.

    **Request Parameters:**
    - `limit` (query): Maximum number of results to return (1-10000, default: 100)
    - `continuation_token` (query): Token for pagination to get next page of results
    - `sort` (body): Sorting configuration with field name and order
    - `query` (body): Advanced query filters using boolean logic and field-specific conditions

    **Request Body Structure:**
    ```json
    {
      "sort": {
        "field_name": "system_properties.created_at",
        "order": "desc"
      },
      "query": {
        "type": "leaf",
        "field": "name",
        "operator": "contains",
        "value": "workout"
      }
    }
    ```

    **Response:**
    - `documents`: Array of matching use case objects with their properties and metadata

    **HTTP Status Codes:**
    - 200: Use cases successfully retrieved
    - 204: No matching use cases found
    - 400: Invalid request parameters or query structure
    - 401: Authentication required
    - 422: Validation error in request body

    **Example Response:**
    ```json
    {
      "documents": [{
        "doc_id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Daily Workout Routine",
        "type": "use_case",
        "tags": ["fitness", "daily", "routine"],
        "archived_at": null,
        "system_properties": {
          "created_at": "2024-01-15T08:30:00Z",
          "updated_at": "2024-01-15T08:30:00Z"
        }
      }]
    }
    ```
    """
    use_cases = await use_case.execute_async(input_boundary=input_boundary)
    if not use_cases:
        raise NoContentException(message="no matching use_cases found")

    output = [UseCaseAPIOutput.map(model=uc) for uc in use_cases]

    return CommonDocumentsResponse[UseCaseAPIOutput](documents=output)


@use_case_router.patch(UseCaseEndpointRoutes.ARCHIVE)
async def archive_use_cases_endpoint(
    use_case_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ArchiveUseCase = Injected(ArchiveUseCase),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    try:
        use_cases = await use_case.execute_async(owner_id=owner_id, use_case_ids=use_case_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[UseCaseAPIOutput](
        documents=[UseCaseAPIOutput(**uc.model_dump(by_alias=True)) for uc in use_cases]
    )


@use_case_router.patch(UseCaseEndpointRoutes.BASE)
async def update_use_cases_endpoint(
    input_boundary: UpdateUseCaseInputBoundary = Depends(UpdateUseCaseAPIRequestInput.to_input_boundary),
    use_case: UpdateUseCase = Injected(UpdateUseCase),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    try:
        use_cases = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="use case duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[UseCaseAPIOutput](
        documents=[UseCaseAPIOutput(**p.model_dump(by_alias=True)) for p in use_cases]
    )


@use_case_router.post(UseCaseEndpointRoutes.BASE)
async def insert_use_cases_endpoint(
    input_boundary: InsertUseCaseInputBoundary = Depends(InsertUseCaseAPIRequestInput.to_input_boundary),
    use_case: InsertUseCase = Injected(InsertUseCase),
) -> CommonDocumentsResponse[UseCaseAPIOutput]:
    try:
        use_cases = await use_case.execute_async(input_boundary=input_boundary)
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate use case detected. A use case with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[UseCaseAPIOutput](
        documents=[UseCaseAPIOutput(**p.model_dump(by_alias=True)) for p in use_cases]
    )
