from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.data_service.api.constants import DataServicePrefixes, RecordEndpointRoutes
from services.data_service.api.mappers.record_api_output_mapper import Record<PERSON>IOutputMapper
from services.data_service.api.models.output.records.record_api_output import RecordAPIOutput
from services.data_service.api.models.request.records.delete_record_api_request_input import (
    DeleteRecordAPIRequestInput,
)
from services.data_service.api.models.request.records.insert_record_api_request_input import (
    InsertRecordAPIRequestInput,
)
from services.data_service.application.use_cases.records.delete_record_by_id_use_case import DeleteRecordByIdUseCase
from services.data_service.application.use_cases.records.insert_record_use_case import InsertRecordUseCase
from services.data_service.application.use_cases.records.models.delete_record_input_boundary import (
    DeleteRecordInputBoundary,
)
from services.data_service.application.use_cases.records.models.insert_record_input_boundary import (
    InsertRecordInputBoundary,
)

record_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.RECORD}",
    tags=["record"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@record_router.post(
    RecordEndpointRoutes.BASE,
    summary="Insert Records",
    description="Create new records in the system. Currently supports sleep records.",
    response_description="Successfully created records with their generated IDs and metadata",
    responses={
        200: {
            "description": "Records successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "type": "sleep_record",
                                "timestamp": "2024-01-15T22:30:00Z",
                                "end_time": "2024-01-16T06:30:00Z",
                                "stage": "deep",
                                "created_at": "2024-01-16T10:00:00Z",
                                "updated_at": None,
                                "deleted_at": None,
                                "metadata": {
                                    "origin": "manual",
                                    "origin_device": "mobile_app",
                                    "source_os": "iOS",
                                    "source_service": "LLIF",
                                    "service": "DATA",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate records found or invalid data",
            "content": {"application/json": {"example": {"detail": "record duplicates found in the input payload"}}},
        },
        401: {"description": "Unauthorized - invalid or missing authentication"},
        422: {"description": "Validation error - invalid request format or data types"},
    },
)
async def insert_record_endpoint(
    request_input: InsertRecordAPIRequestInput = Body(
        ...,
        description="Record data to insert",
        example={
            "documents": [
                {
                    "type": "sleep_record",
                    "timestamp": "2024-01-15T22:30:00Z",
                    "end_time": "2024-01-16T06:30:00Z",
                    "stage": "deep",
                }
            ],
            "metadata": {
                "origin": "manual",
                "origin_device": "mobile_app",
                "source_os": "iOS",
                "source_service": "LLIF",
            },
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertRecordUseCase = Injected(InsertRecordUseCase),
) -> CommonDocumentsResponse[RecordAPIOutput]:
    try:
        records = await use_case.execute_async(
            boundary=InsertRecordInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [RecordAPIOutputMapper.map(record) for record in records]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="record duplicates found in the input payload") from err

    return CommonDocumentsResponse[RecordAPIOutput](documents=documents_api_output)

@record_router.delete(
    RecordEndpointRoutes.BASE,
    summary="Delete Records",
    description="Delete records by their IDs. Currently supports sleep records.",
    response_description="Successfully deleted record IDs",
    responses={
        200: {
            "description": "Records successfully deleted",
            "content": {
                "application/json": {
                    "example": {
                        "document_ids": ["123e4567-e89b-12d3-a456-************", "456e7890-e12b-34d5-a678-************"]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - invalid operation or record not found",
            "content": {"application/json": {"example": {"detail": "Record not found or cannot be deleted"}}},
        },
        401: {"description": "Unauthorized - invalid or missing authentication"},
        422: {"description": "Validation error - invalid request format or data types"},
    },
)
async def delete_record_endpoint(
    request_input: DeleteRecordAPIRequestInput = Body(
        ...,
        description="Record deletion request containing record IDs and types",
        example={
            "documents": [
                {"id": "123e4567-e89b-12d3-a456-************", "type": "sleep_record"},
                {"id": "456e7890-e12b-34d5-a678-************", "type": "sleep_record"},
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteRecordByIdUseCase = Injected(DeleteRecordByIdUseCase),
) -> CommonDocumentsIdsResponse:
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteRecordInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
