from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException, IncorrectOperationException, NoContentException
from services.data_service.api.constants import AnalyseEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.output.event_correlation_api_output import EventCorrelationAPIOutput
from services.data_service.api.models.output.suggest_correlation_parameters_api_output import (
    SuggestCorrelationParametersAPIOutput,
)
from services.data_service.api.models.output.trend_detection_api_output import TrendDetectionAPIOutput
from services.data_service.api.models.request.analyze.event_correlation_api_request_input import (
    EventCorrelationAPIRequestInput,
)
from services.data_service.api.models.request.analyze.suggest_correlation_parameters_api_request_input import (
    SuggestCorrelationParametersAPIRequestInput,
)
from services.data_service.api.models.request.analyze.trend_detection_api_request_input import (
    TrendDetectionAPIRequestInput,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    EventCorrelationUseCase,
    EventCorrelationUseCaseInputBoundary,
)
from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_use_case import (
    SuggestCorrelationParametersUseCase,
    SuggestCorrelationParametersUseCaseInputBoundary,
)
from services.data_service.application.use_cases.trend_detection_use_case import (
    TrendDetectionUseCase,
    TrendDetectionUseCaseInputBoundary,
)

analyse_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.ANALYSE}",
    tags=["analyse"],
    responses={404: {"description": "Not found"}},
)


@analyse_router.post(
    AnalyseEndpointRoutes.TREND_DETECT,
)
async def trend_detection_endpoint(
    input_boundary: TrendDetectionUseCaseInputBoundary = Depends(TrendDetectionAPIRequestInput.to_input_boundary),
    use_case: TrendDetectionUseCase = Injected(TrendDetectionUseCase),
) -> TrendDetectionAPIOutput:
    """
    Detect trends in time series data using linear regression analysis.

    This endpoint analyzes a sequence of numerical values to determine if there's a statistically
    significant upward or downward trend. It uses linear regression with configurable thresholds
    for R-squared (model fit) and relative slope (trend significance).

    **Algorithm:**
    - Applies linear regression to the data series
    - Checks if R-squared meets the minimum threshold for model fit
    - Calculates relative slope (coefficient / mean) to determine trend significance
    - Returns trend classification and statistical metrics

    **Use Cases:**
    - Analyzing health metrics over time (weight, blood pressure, etc.)
    - Detecting patterns in symptom severity
    - Identifying trends in environmental data

    **Request Body:**
    - `data_series`: Array of at least 2 numerical values representing the time series
    - `r2_threshold`: Minimum R-squared value for significant trend (default: 0.3)
    - `relative_slope_threshold`: Minimum absolute relative slope for trend detection (default: 0.01)

    **Response:**
    - `trend_result`: Classification as "UPWARD_TREND", "DOWNWARD_TREND", or "NO_TREND"
    - `coefficient`: Linear regression coefficient (slope)
    - `intercept`: Linear regression intercept
    - `relative_slope`: Coefficient divided by mean value (measure of trend strength)

    **Example:**
    ```json
    {
        "data_series": [1.0, 1.2, 1.5, 1.8, 2.1],
        "r2_threshold": 0.3,
        "relative_slope_threshold": 0.01
    }
    ```

    **Returns:**
    ```json
    {
        "trend_result": "UPWARD_TREND",
        "coefficient": 0.25,
        "intercept": 0.95,
        "relative_slope": 0.167
    }
    ```
    """
    result = await use_case.execute_async(input_boundary=input_boundary)
    return TrendDetectionAPIOutput.map(model=result)


@analyse_router.post(
    AnalyseEndpointRoutes.CORRELATE_EVENT,
)
async def event_correlation_endpoint(
    input_boundary: EventCorrelationUseCaseInputBoundary = Depends(EventCorrelationAPIRequestInput.to_input_boundary),
    use_case: EventCorrelationUseCase = Injected(EventCorrelationUseCase),
) -> EventCorrelationAPIOutput:
    """
    Analyze statistical correlation between two event variables with temporal constraints.

    This endpoint performs correlation analysis between dependent and independent variables from
    user events or environmental data. It supports different correlation methods based on data types:
    - Pearson correlation for continuous variables
    - ANOVA F-statistic for continuous vs discrete/binary variables
    - Chi-squared for discrete/binary vs discrete/binary variables

    **Key Features:**
    - Temporal matching with configurable time windows ("before", "after", "closest")
    - Support for event data and environmental data (weather, air quality, pollen)
    - Field-specific analysis with aggregation methods (mean, sum, count, etc.)
    - Selection bias handling for occurrence-based correlations
    - Statistical significance testing with p-values and confidence intervals

    **Request Body:**
    - `dependent`: Variable to be predicted/explained
      - `query`: Event or environment query defining the data source
      - `field_name`: Specific field to analyze (null for occurrence counting)
      - `aggregation_method`: How to aggregate values (required for environment data)
    - `independent`: Variable used for prediction/explanation (same structure as dependent)
    - `temporal_options`: Time-based matching configuration
      - `type`: "before", "after", or "closest" temporal relationship
      - `time_input`: Time range and interval for matching events

    **Response:**
    - `data`: Array of [dependent, independent] value pairs used in correlation
    - `correlation`: Statistical results including coefficient, p-value, certainty, relationship strength
    - `dependent`/`independent`: Metadata with document counts
    - `suggested_visualisation`: Recommended chart type ("scatter_plot", "box_plot", "heat_map")

    **Example:**
    ```json
    {
        "dependent": {
            "query": {"types": ["symptom"], "filters": {"name": "headache"}},
            "field_name": "rating",
            "aggregation_method": "mean"
        },
        "independent": {
            "query": {"types": ["food"], "filters": {"name": "coffee"}},
            "field_name": null,
            "aggregation_method": null
        },
        "temporal_options": {
            "type": "before",
            "time_input": {"interval": "4h", "time_gte": "2024-01-01", "time_lte": "2024-01-31"}
        }
    }
    ```

    **Errors:**
    - `400 Bad Request`: Invalid query parameters or temporal options
    - `204 No Content`: No matching data found for correlation analysis
    """
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if not result.data:
            raise NoContentException("No data available")
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except NoContentException as err:
        raise NoContentException(message=err.message) from err
    return EventCorrelationAPIOutput.map(model=result)


@analyse_router.post(
    AnalyseEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS,
)
async def suggest_correlation_parameters_endpoint(
    _: UUID = Depends(get_current_uuid),
    input_boundary: SuggestCorrelationParametersUseCaseInputBoundary = Depends(
        SuggestCorrelationParametersAPIRequestInput.to_input_boundary
    ),
    use_case: SuggestCorrelationParametersUseCase = Injected(SuggestCorrelationParametersUseCase),
) -> SuggestCorrelationParametersAPIOutput:
    """
    Get AI-powered suggestions for optimal correlation analysis parameters.

    This endpoint uses artificial intelligence to analyze the provided event queries and suggest
    the most appropriate parameters for running a correlation analysis. It considers the types
    of events, their typical relationships, and domain knowledge to recommend optimal settings.

    **AI Analysis:**
    - Examines event types and their schemas to understand available fields
    - Considers typical temporal relationships between different event categories
    - Suggests appropriate aggregation methods based on data characteristics
    - Provides reasoning for all recommendations

    **Use Cases:**
    - Getting started with correlation analysis when unsure of optimal parameters
    - Validating manual parameter choices against AI recommendations
    - Learning about typical relationships between different event types
    - Automating correlation setup for common analysis patterns

    **Request Body:**
    - `dependent_query`: Query defining the dependent variable events
      - For events: `{"types": ["symptom"], "filters": {...}}`
      - For environment: `{"domain_type": "Weather"}`
    - `independent_query`: Query defining the independent variable events (same format)

    **Response:**
    - `dependent`: Suggested configuration for dependent variable
      - `field_name`: Recommended field to analyze (or null for occurrence)
      - `aggregation_method`: Suggested aggregation method
      - `query`: Original query (passed through)
    - `independent`: Suggested configuration for independent variable (same structure)
    - `temporal_options`: Recommended temporal matching settings
      - `type`: Suggested relationship type ("before", "after", "closest")
      - `time_input`: Recommended time range and interval
    - `reasoning`: Detailed explanation of all suggestions

    **Example:**
    ```json
    {
        "dependent_query": {
            "types": ["symptom"],
            "filters": {"name": "headache"}
        },
        "independent_query": {
            "types": ["food"],
            "filters": {"category": "caffeine"}
        }
    }
    ```

    **Returns:**
    ```json
    {
        "dependent": {
            "field_name": "rating",
            "aggregation_method": "mean",
            "query": {...}
        },
        "independent": {
            "field_name": null,
            "aggregation_method": null,
            "query": {...}
        },
        "temporal_options": {
            "type": "before",
            "time_input": {"interval": "4h", "time_gte": "...", "time_lte": "..."}
        },
        "reasoning": "Suggesting 'before' with '4h' interval as caffeine intake often precedes headache onset within hours..."
    }
    ```

    **Errors:**
    - `204 No Content`: AI could not generate suggestions for the provided queries
    - `401 Unauthorized`: Authentication required
    """
    result = await use_case.execute_async(input_boundary=input_boundary)
    if not result:
        raise NoContentException("No suggestion available")
    return SuggestCorrelationParametersAPIOutput.map(model=result)
