import logging
from uuid import UUID

from fastapi import APIRouter, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import NoContentException
from services.data_service.api.constants import DataServicePrefixes, LookupEndpointRoutes
from services.data_service.api.models.request.content.content_lookup_request_input import (
    PostContentLookupRequestInput,
)
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary
from services.data_service.application.use_cases.content.content_lookup_use_case import (
    ContentLookupOutputBoundary,
    ContentLookupUseCase,
)

lookup_router = APIRouter(
    prefix=DataServicePrefixes.V3 + DataServicePrefixes.EXPERIMENTAL_PREFIX + DataServicePrefixes.LOOKUP_PREFIX,
    tags=["lookup"],
    responses={
        404: {"description": "Not found"},
    },
)


@lookup_router.post(
    LookupEndpointRoutes.BASE,
    responses={204: {"description": "Couldn't get content from URL", "model": None}},
    summary="Extract content metadata from URL",
    description="""
    Extract structured metadata from web content using URL lookup.

    This endpoint analyzes a provided URL and extracts key metadata including title, content type,
    and description. It supports various content types including articles, videos, websites,
    and interactive content. The service attempts to parse standard web metadata (Open Graph,
    Twitter Cards, etc.) and categorizes the content appropriately.

    **Supported Content Types:**
    - **Articles**: Blog posts, news articles, documentation
    - **Videos**: YouTube, Vimeo, and other video platforms
    - **Websites**: General web pages and landing pages
    - **Interactive**: Web applications, tools, and interactive content
    - **Audio**: Podcasts, music, and audio content
    - **Images**: Image galleries, photos, and visual content

    **Content Analysis Features:**
    - Automatic content type detection and categorization
    - Title extraction from page metadata or content
    - Description parsing from meta tags and content
    - Robust error handling for inaccessible or malformed content

    **Example URLs:**
    - `https://example.com/article` → Article with title and description
    - `https://youtube.com/watch?v=xyz` → Video content with metadata
    - `https://github.com/user/repo` → Interactive/website content

    **Error Handling:**
    - Returns 204 No Content when URL cannot be accessed or parsed
    - Handles network timeouts, invalid URLs, and parsing failures gracefully
    - Logs detailed error information for debugging while returning user-friendly responses
    """,
    response_description="Structured content metadata including title, type, and description extracted from the URL",
)
async def post_content_lookup(
    _: UUID = Depends(get_current_uuid),
    body: PostContentLookupRequestInput = Body(
        ...,
        description="URL to analyze and extract content metadata from",
        examples={
            "article": {
                "summary": "News article URL",
                "description": "Extract metadata from a news article",
                "value": {"url": "https://example.com/news/article-title"}
            },
            "video": {
                "summary": "Video content URL",
                "description": "Extract metadata from video content",
                "value": {"url": "https://youtube.com/watch?v=dQw4w9WgXcQ"}
            },
            "website": {
                "summary": "General website URL",
                "description": "Extract metadata from a general website",
                "value": {"url": "https://github.com/user/repository"}
            }
        }
    ),
    use_case: ContentLookupUseCase = Injected(ContentLookupUseCase),
) -> ContentLookupOutputBoundary:
    try:
        return await use_case.execute(input_boundary=ContentLookupInputBoundary(url=body.url))
    except Exception as error:
        # For now let's make everything 204 on error until we do request url validation
        logging.exception(f"could not lookup url: {body.url}, error: {error}")
        raise NoContentException("could not lookup url")
