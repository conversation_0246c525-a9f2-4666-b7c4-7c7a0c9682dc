from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.application.exceptions import BadRequestException, IncorrectOperationException, NoContentException
from services.data_service.api.constants import AggregationEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.output.date_histogram_api_output import DataHistogramAPIOutput
from services.data_service.api.models.output.frequency_distribution_api_output import FrequencyDistributionAPIOutput
from services.data_service.api.models.request.aggregate.date_histogram_api_request_input import (
    DataHistogramAPIRequestInput,
)
from services.data_service.api.models.request.aggregate.frequency_distribution_api_request_input import (
    FrequencyDistributionAPIRequestInput,
)
from services.data_service.api.models.response.calendar_aggregations_api_response import (
    CalendarFrequencyDistributionAPIOutput,
    CalendarHistogramAggregationAPIOutput,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregations_api_request_input import (
    CalendarFrequencyDistributionAPIRequestInput,
    CalendarHistogramAggregationAPIRequestInput,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregations_input_boundary import (
    CalendarFrequencyDistributionInputBoundary,
    CalendarHistogramAggregationInputBoundary,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_frequency_distribution_use_case import (
    CalendarFrequencyDistributionUseCase,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_histogram_aggregation_use_case import (
    CalendarHistogramAggregationUseCase,
)
from services.data_service.application.use_cases.date_histogram_use_case import (
    DateHistogramUseCase,
    DateHistogramUseCaseInputBoundary,
)
from services.data_service.application.use_cases.frequency_distribution_use_case import (
    FrequencyDistributionUseCase,
    FrequencyDistributionUseCaseInputBoundary,
)

aggregate_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.AGGREGATION}",
    tags=["aggregate"],
    responses={404: {"description": "Not found"}},
)


@aggregate_router.post(
    AggregationEndpointRoutes.DATE_HISTOGRAM,
    summary="Generate date histogram aggregations",
    description="""
    Creates time-based histogram aggregations of documents with configurable time intervals and field aggregations.

    This endpoint groups documents into time buckets based on their timestamps and applies aggregation methods
    (sum, avg, min, max, count) to specified fields within each bucket. Useful for analyzing trends over time,
    creating time series visualizations, and understanding temporal patterns in data.

    **Key Features:**
    - Configurable time intervals (minutes, hours, days, weeks, months, years)
    - Multiple aggregation methods per field (sum, average, min, max, count)
    - Timezone support for proper time bucket alignment
    - Moving average calculations with configurable window sizes
    - Document count per time bucket

    **Returns:** Array of time buckets with timestamp ranges, document counts, and field aggregation results.
    """,
)
async def date_histogram_endpoint(
    input_boundary: DateHistogramUseCaseInputBoundary = Depends(DataHistogramAPIRequestInput.to_input_boundary),
    use_case: DateHistogramUseCase = Injected(DateHistogramUseCase),
) -> DataHistogramAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    if not result.results:
        raise NoContentException("no data available for the given queries")
    return DataHistogramAPIOutput.map(model=result)


@aggregate_router.post(
    AggregationEndpointRoutes.FREQUENCY_DISTRIBUTION,
    summary="Generate frequency distribution of field values",
    description="""
    Analyzes the frequency distribution of unique values for a specified field across document types.

    This endpoint counts how many times each unique value appears in the specified field, providing insights
    into data distribution patterns. Useful for understanding value popularity, identifying outliers,
    and creating categorical data visualizations.

    **Key Features:**
    - Counts occurrences of unique values in any document field
    - Supports multiple document types in a single query
    - Results grouped by document type for clear categorization
    - Sorted by frequency (most common values first)
    - Configurable result size limit (up to 100,000 unique values)

    **Use Cases:**
    - Analyzing category distributions (e.g., event types, tags, ratings)
    - Finding most common values in datasets
    - Data quality assessment and outlier detection
    - Creating frequency charts and categorical visualizations

    **Returns:** Dictionary mapping document types to value-frequency pairs, sorted by frequency descending.
    """,
)
async def frequency_distribution_endpoint(
    input_boundary: FrequencyDistributionUseCaseInputBoundary = Depends(
        FrequencyDistributionAPIRequestInput.to_input_boundary
    ),
    use_case: FrequencyDistributionUseCase = Injected(FrequencyDistributionUseCase),
) -> FrequencyDistributionAPIOutput:
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if all(not type_output for type_output in result.data.values()):
            raise NoContentException("No data available")
        return FrequencyDistributionAPIOutput.map(model=result)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@aggregate_router.post(
    AggregationEndpointRoutes.CALENDAR_HISTOGRAM_AGGREGATION,
    response_model=CalendarHistogramAggregationAPIOutput,
    summary="Generate calendar-based histogram aggregations",
    description="""
    Creates histogram aggregations based on calendar patterns (months, weekdays, hours, lunar phases, etc.)
    with field value aggregations for each calendar bucket.

    This endpoint groups documents by calendar-based patterns and applies aggregation methods to specified
    fields within each calendar bucket. Perfect for discovering cyclical patterns, seasonal trends, and
    time-of-day behaviors in your data.

    **Calendar Aggregation Types:**
    - **month_names**: Groups by calendar months (January, February, etc.)
    - **weekdays**: Groups by days of the week (Monday, Tuesday, etc.)
    - **hours_in_day**: Groups by hours (0-23) for daily pattern analysis
    - **days_of_month**: Groups by day of month (1-31) for monthly patterns
    - **parts_of_month**: Groups by month sections (1-5) for start/mid/end analysis
    - **lunar_phases**: Groups by moon phases (New Moon, First Quarter, Full Moon, Last Quarter)

    **Key Features:**
    - Multiple aggregation methods (sum, average, min, max, count)
    - Optional null value filling for complete calendar coverage
    - Document count per calendar bucket
    - Aggregated field values per bucket

    **Use Cases:**
    - Seasonal trend analysis (monthly patterns)
    - Daily routine analysis (hourly patterns)
    - Weekly behavior patterns (weekday analysis)
    - Lunar cycle correlations
    - Monthly distribution analysis

    **Returns:** Array of calendar buckets with aggregation keys, document counts, and field aggregation results.
    """,
)
async def calendar_histogram_aggregation_endpoint(
    calendar_histogram_aggregation_use_case: CalendarHistogramAggregationUseCase = Injected(
        CalendarHistogramAggregationUseCase
    ),
    input_boundary: CalendarHistogramAggregationInputBoundary = Depends(
        CalendarHistogramAggregationAPIRequestInput.to_input_boundary
    ),
) -> CalendarHistogramAggregationAPIOutput:
    try:
        result = await calendar_histogram_aggregation_use_case.execute_async(
            user_uuid=input_boundary.owner_id,
            calendar_aggregation_type=input_boundary.calendar_aggregation_type,
            field_name=input_boundary.field_name,
            aggregation_method=input_boundary.aggregation_method,
            query=input_boundary.query,
            fill_null_values=input_boundary.fill_null_values,
        )
        if not result:
            raise NoContentException("No documents found for CalendarHistogramAggregation")

        return CalendarHistogramAggregationAPIOutput.map(model=result)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@aggregate_router.post(
    AggregationEndpointRoutes.CALENDAR_FREQUENCY_DISTRIBUTION,
    response_model=CalendarFrequencyDistributionAPIOutput,
    summary="Generate calendar-based frequency distributions",
    description="""
    Analyzes document frequency distributions based on calendar patterns to identify cyclical behaviors
    and temporal patterns in data occurrence.

    This endpoint counts how many documents occur within each calendar-based bucket (months, weekdays,
    hours, etc.), revealing patterns like seasonal activity, weekly routines, daily habits, or lunar
    cycle correlations. Unlike histogram aggregations, this focuses purely on document counts rather
    than field value aggregations.

    **Calendar Aggregation Types:**
    - **month_names**: Document counts by calendar months (January, February, etc.)
    - **weekdays**: Document counts by days of the week (Monday, Tuesday, etc.)
    - **hours_in_day**: Document counts by hours (0-23) for daily activity patterns
    - **days_of_month**: Document counts by day of month (1-31) for monthly patterns
    - **parts_of_month**: Document counts by month sections (1-5) for start/mid/end analysis
    - **lunar_phases**: Document counts by moon phases (New Moon, First Quarter, Full Moon, Last Quarter)
    - **time_between**: Document counts by time intervals between consecutive events

    **Key Features:**
    - Pure frequency counting (no field value aggregation)
    - Optional null value filling for complete calendar coverage
    - Specialized time-between analysis for event spacing patterns
    - Sorted results for easy pattern identification

    **Use Cases:**
    - Activity pattern discovery (when do events typically occur?)
    - Seasonal behavior analysis
    - Daily/weekly routine identification
    - Event clustering and spacing analysis
    - Lunar cycle correlation studies
    - Data collection pattern validation

    **Returns:** Array of calendar buckets with aggregation keys and document frequency counts.
    """,
)
async def calendar_frequency_distribution_endpoint(
    calendar_frequency_distribution_use_case: CalendarFrequencyDistributionUseCase = Injected(
        CalendarFrequencyDistributionUseCase
    ),
    input_boundary: CalendarFrequencyDistributionInputBoundary = Depends(
        CalendarFrequencyDistributionAPIRequestInput.to_input_boundary
    ),
) -> CalendarFrequencyDistributionAPIOutput:
    try:
        result = await calendar_frequency_distribution_use_case.execute_async(
            user_uuid=input_boundary.owner_id,
            calendar_aggregation_type=input_boundary.calendar_aggregation_type,
            query=input_boundary.query,
            fill_null_values=input_boundary.fill_null_values,
        )
        if not result:
            raise NoContentException("No documents found for CalendarFrequencyDistribution")

        return CalendarFrequencyDistributionAPIOutput.map(model=result)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
