from typing import Optional

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel


class SystemPropertiesSchemaAPIOutput(BaseDataModel):
    created_at: SerializableAwareDatetime = Field(alias=DocumentLabels.CREATED_AT)
    updated_at: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.UPDATED_AT, default=None)
    deleted_at: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.DELETED_AT, default=None)


class SystemPropertiesDocumentAPIOutput(BaseDataModel):
    system_properties: SystemPropertiesSchemaAPIOutput = Field(alias=DocumentLabels.SYSTEM_PROPERTIES, default=...)
