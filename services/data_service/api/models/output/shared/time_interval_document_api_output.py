from typing import Optional

from pydantic import Field, computed_field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel


class TimestampDocumentAPIOutput(BaseDataModel):
    timestamp: SerializableAwareDatetime = Field(alias=DocumentLabels.TIMESTAMP)


class TimeIntervalDocumentAPIOutput(TimestampDocumentAPIOutput):
    end_time: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.END_TIME, default=None)

    @computed_field(alias=DocumentLabels.DURATION)
    @property
    def duration(self) -> float:
        if not self.end_time:
            return 0
        secs = (self.end_time - self.timestamp).total_seconds()
        return secs


class TimeIntervalDocumentStrictAPIOutput(TimestampDocumentAPIOutput):
    end_time: SerializableAwareDatetime = Field(alias=DocumentLabels.END_TIME)

    @computed_field(alias=DocumentLabels.DURATION)
    @property
    def duration(self) -> float:
        secs = (self.end_time - self.timestamp).total_seconds()
        return secs
