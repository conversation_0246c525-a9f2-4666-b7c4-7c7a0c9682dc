from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.records.sleep_record import SleepRecordFields, SleepRecordIdentifier
from services.data_service.api.models.output.events.event_metadata_api_output import EventMetadataAPIOutput
from services.data_service.api.models.output.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.models.output.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)
from services.data_service.api.models.output.shared.time_interval_document_api_output import (
    TimeIntervalDocumentStrictAPIOutput,
)


class SleepRecordAPIOutput(
    SleepRecordIdentifier,
    SystemPropertiesDocumentAPIOutput,
    IdentifiableDocumentAPIOutput,
    TimeIntervalDocumentStrictAPIOutput,
):
    type: Literal[DataType.SleepRecord] = Field(alias=SleepRecordFields.TYPE)
    stage: SleepStage = Field(alias=SleepRecordFields.STAGE)
    metadata: EventMetadataAPIOutput
