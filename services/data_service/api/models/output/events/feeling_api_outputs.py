from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.exercise.exercise import ExerciseFields
from services.base.domain.schemas.events.feeling.emotion import EmotionCategory, EmotionFields, EmotionIdentifier
from services.base.domain.schemas.events.feeling.stress import Stress<PERSON>ate<PERSON>y, StressFields, StressIdentifier
from services.data_service.api.models.output.events.event_api_output_base import EventAPIOutputBase


class EmotionAPIOutput(EventAPIOutputBase, EmotionIdentifier):
    type: Literal[DataType.Emotion] = Field(alias=EmotionFields.TYPE)
    category: EmotionCategory = Field(alias=EmotionFields.CATEGORY)
    rating: int = Field(alias=ExerciseFields.RATING, ge=-5, le=10)


class StressAPIOutput(EventAPIOutputBase, StressIdentifier):
    type: Literal[DataType.Stress] = Field(alias=StressFields.TYPE)
    category: StressCategory = Field(alias=StressFields.CATEGORY)
    rating: int = Field(alias=ExerciseFields.RATING, ge=-5, le=10)
