from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.sleep_v3 import SleepV3Category, SleepV3Fields, SleepV3Identifier
from services.data_service.api.models.output.events.event_api_output_base import EventAPIOutputBase


class SleepV3APIOutput(EventAPIOutputBase, SleepV3Identifier):
    type: Literal[DataType.SleepV3] = Field(alias=SleepV3Fields.TYPE)
    category: SleepV3Category = Field(alias=SleepV3Fields.CATEGORY)
    rating: int | None = Field(alias=SleepV3Fields.RATING, ge=-5, le=10)
    deep_seconds: int | None = Field(alias=SleepV3Fields.DEEP_SECONDS)
    light_seconds: int | None = Field(alias=SleepV3Fields.LIGHT_SECONDS)
    rem_seconds: int | None = Field(alias=SleepV3Fields.REM_SECONDS)
    awake_seconds: int | None = Field(alias=SleepV3Fields.AWAKE_SECONDS)
    restless_moments: int | None = Field(alias=SleepV3Fields.RESTLESS_MOMENTS)
    provider_score: int | None = Field(alias=SleepV3Fields.PROVIDER_SCORE)
    llif_score: int | None = Field(alias=SleepV3Fields.LLIF_SCORE)
