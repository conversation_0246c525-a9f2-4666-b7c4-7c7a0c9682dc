from abc import ABC
from typing import Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.core_event import Core<PERSON>ventFields
from services.base.domain.schemas.events.document_base import AssetReference, DocumentValueLimits
from services.base.domain.schemas.events.event import EventFields, EventPlanExtension, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.data_service.api.models.output.events.event_metadata_api_output import EventMetadataAPIOutput
from services.data_service.api.models.output.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.models.output.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)
from services.data_service.api.models.output.shared.time_interval_document_api_output import (
    TimeIntervalDocumentAPIOutput,
)


class EventAPIOutputBase(
    SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput, TimeIntervalDocumentAPIOutput, TypeIdentifier, ABC
):
    name: NonEmptyStr = Field(alias=EventFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH)
    submission_id: UUID = Field(alias=CoreEventFields.SUBMISSION_ID)
    template_id: UUID | None = Field(alias=EventFields.TEMPLATE_ID)
    tags: UniqueSequenceStr = Field(
        alias=DocumentLabels.TAGS,
        max_length=DocumentValueLimits.MaxTagsCount,
    )
    group_id: UUID | None = Field(alias=CoreEventFields.GROUP_ID)
    metadata: EventMetadataAPIOutput = Field(alias=EventFields.METADATA)
    asset_references: Sequence[AssetReference] | None = Field(min_length=1, alias=EventFields.ASSET_REFERENCES)
    plan_extension: EventPlanExtension | None = Field(alias=EventFields.PLAN_EXTENSION)
    note: NonEmptyStr | None = Field(
        alias=EventFields.NOTE,
        default=None,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
