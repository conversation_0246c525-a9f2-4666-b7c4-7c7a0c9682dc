from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.medication.medication import (
    ConsumeUnit,
    MedicationCategory,
    MedicationDetails,
    MedicationFields,
    MedicationIdentifier,
    MedicationValueLimits,
    SingleDoseInformation,
    VolumeUnit,
    WeightUnit,
)
from services.data_service.api.models.output.events.event_api_output_base import EventAPIOutputBase


class MedicationAPIOutput(EventAPIOutputBase, MedicationIdentifier):
    type: Literal[DataType.Medication] = Field(alias=MedicationFields.TYPE)
    category: MedicationCategory = Field(alias=MedicationFields.CATEGORY)
    medication_details: MedicationDetails = Field(alias=MedicationFields.MEDICATION_DETAILS)
    single_dose_information: SingleDoseInformation = Field(alias=MedicationFields.SINGLE_DOSE_INFORMATION)
    consumed_amount: Rounded6Float = Field(
        alias=MedicationFields.CONSUMED_AMOUNT,
        ge=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
        le=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
    )
    consume_unit: VolumeUnit | WeightUnit | ConsumeUnit = Field(alias=MedicationFields.CONSUME_UNIT)
