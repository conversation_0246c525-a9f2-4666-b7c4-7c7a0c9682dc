from abc import ABC
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.audio import AudioCategory, AudioIdentifier
from services.base.domain.schemas.events.content.content import ContentCategory, ContentIdentifier
from services.base.domain.schemas.events.content.content_collection import ContentFields, ContentValueLimits
from services.base.domain.schemas.events.content.image import ImageCategory, ImageIdentifier
from services.base.domain.schemas.events.content.interactive import InteractiveCategory, InteractiveIdentifier
from services.base.domain.schemas.events.content.text import TextCategory, TextIdentifier
from services.base.domain.schemas.events.content.video import VideoCategory, VideoIdentifier
from services.data_service.api.models.output.events.event_api_output_base import EventAPIOutputBase


class CollectionContentAPIOutput(EventAPIOutputBase, ABC):
    title: NonEmptyStr | None = Field(alias=ContentFields.TITLE, max_length=ContentValueLimits.TITLE_MAX_LENGTH)
    url: str | None = Field(alias=ContentFields.URL, default=None, max_length=ContentValueLimits.URL_MAX_LENGTH)
    rating: int | None = Field(
        alias=ContentFields.RATING,
        ge=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
        le=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
        default=None,
    )


class ContentAPIOutput(CollectionContentAPIOutput, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY)


class AudioAPIOutput(CollectionContentAPIOutput, AudioIdentifier):
    type: Literal[DataType.Audio] = Field(alias=ContentFields.TYPE)
    category: AudioCategory = Field(alias=ContentFields.CATEGORY)


class InteractiveAPIOutput(CollectionContentAPIOutput, InteractiveIdentifier):
    type: Literal[DataType.Interactive] = Field(alias=ContentFields.TYPE)
    category: InteractiveCategory = Field(alias=ContentFields.CATEGORY)


class ImageAPIOutput(CollectionContentAPIOutput, ImageIdentifier):
    type: Literal[DataType.Image] = Field(alias=ContentFields.TYPE)
    category: ImageCategory = Field(alias=ContentFields.CATEGORY)


class TextAPIOutput(CollectionContentAPIOutput, TextIdentifier):
    type: Literal[DataType.Text] = Field(alias=ContentFields.TYPE)
    category: TextCategory = Field(alias=ContentFields.CATEGORY)


class VideoAPIOutput(CollectionContentAPIOutput, VideoIdentifier):
    type: Literal[DataType.Video] = Field(alias=ContentFields.TYPE)
    category: VideoCategory = Field(alias=ContentFields.CATEGORY)
