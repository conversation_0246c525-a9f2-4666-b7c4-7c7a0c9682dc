from services.data_service.application.use_cases.calendar_aggregation.calendar_frequency_distribution_use_case import (
    CalendarFrequencyDistributionUseCaseOutputBoundary,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_histogram_aggregation_use_case import (
    CalendarHistogramAggregationUseCaseOutputBoundary,
)


class CalendarFrequencyDistributionAPIOutput(CalendarFrequencyDistributionUseCaseOutputBoundary): ...


class CalendarHistogramAggregationAPIOutput(CalendarHistogramAggregationUseCaseOutputBoundary): ...
