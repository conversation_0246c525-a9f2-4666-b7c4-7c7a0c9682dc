from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.events.use_case import Use<PERSON>aseFields
from services.data_service.api.models.output.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.models.output.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)


class UseCaseAPIOutput(SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput):
    type: Literal[DataType.UseCase] = Field(alias=DocumentLabels.TYPE)
    name: str = Field(alias=UseCaseFields.NAME)
    archived_at: SerializableAwareDatetime | None = Field(alias=UseCaseFields.ARCHIVED_AT)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)
