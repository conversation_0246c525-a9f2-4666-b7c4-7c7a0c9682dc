from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends
from pydantic import Field

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.annotated_types import NonEmptyStr
from services.data_service.api.queries.document_query_api import DocumentQueryAPI
from services.data_service.application.use_cases.frequency_distribution_use_case import (
    FrequencyDistributionUseCaseInputBoundary,
)


class FrequencyDistributionAPIRequestInput(DocumentQueryAPI):
    field_name: NonEmptyStr = Field(min_length=1)

    @staticmethod
    def to_input_boundary(
        request_input: FrequencyDistributionAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> FrequencyDistributionUseCaseInputBoundary:
        return FrequencyDistributionUseCaseInputBoundary(
            query=request_input.to_query(),
            field_name=request_input.field_name,
            owner_id=owner_id,
        )
