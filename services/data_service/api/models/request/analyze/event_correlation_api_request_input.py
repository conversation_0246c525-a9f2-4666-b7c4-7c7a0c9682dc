from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends
from pydantic import model_validator

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.query.boolean_query import Boolean<PERSON>uery
from services.base.domain.schemas.query.leaf_query import LeafQuery, RangeQuery
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    CorrelationTemporalOptions,
    CorrelationVariableInput,
    EnvironmentTypedQuery,
    EventCorrelationUseCaseInputBoundary,
)


class EventCorrelationAPIRequestInput(BaseDataModel):
    dependent: CorrelationVariableInput
    independent: CorrelationVariableInput
    temporal_options: CorrelationTemporalOptions

    @staticmethod
    def to_input_boundary(
        request_input: EventCorrelationAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> EventCorrelationUseCaseInputBoundary:
        return EventCorrelationUseCaseInputBoundary(
            dependent=request_input.dependent,
            independent=request_input.independent,
            temporal_options=request_input.temporal_options,
            owner_id=owner_id,
        )

    @staticmethod
    def validate_leaf_query(leaf_query: LeafQuery):
        if isinstance(leaf_query, RangeQuery) and (leaf_query.field_name == DocumentLabels.TIMESTAMP):
            raise QueryValidationException("Variable queries cannot contain timestamp range queries")

    def validate_query(self, boolean_query: BooleanQuery):
        for query in boolean_query.queries:
            if isinstance(query, LeafQuery):
                self.validate_leaf_query(query)
            elif isinstance(query, BooleanQuery):
                self.validate_query(boolean_query=query)

    @model_validator(mode="after")
    def validate_variable_queries_dont_include_timestamp_range_query(self):
        dependent_variable_query = self.dependent.query
        independent_variable_query = self.independent.query
        for variable_query in [dependent_variable_query, independent_variable_query]:
            if isinstance(variable_query, EnvironmentTypedQuery):
                continue
            query = variable_query.query
            if isinstance(query, LeafQuery):
                self.validate_leaf_query(query)
            elif isinstance(query, BooleanQuery):
                self.validate_query(query)
        return self
