from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.application.use_cases.trend_detection_use_case import TrendDetectionUseCaseInputBoundary


class TrendDetectionAPIRequestInput(TrendDetectionUseCaseInputBoundary):
    @staticmethod
    def to_input_boundary(
        request_input: TrendDetectionAPIRequestInput = Body(...),
        _: UUID = Depends(get_current_uuid),
    ) -> TrendDetectionUseCaseInputBoundary:
        return TrendDetectionUseCaseInputBoundary(
            data_series=request_input.data_series,
            r2_threshold=request_input.r2_threshold,
            relative_slope_threshold=request_input.relative_slope_threshold,
        )
