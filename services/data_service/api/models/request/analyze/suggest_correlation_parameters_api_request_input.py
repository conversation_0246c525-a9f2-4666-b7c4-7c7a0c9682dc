from __future__ import annotations

from fastapi import Body

from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_use_case import (
    SuggestCorrelationParametersUseCaseInputBoundary,
)


class SuggestCorrelationParametersAPIRequestInput(SuggestCorrelationParametersUseCaseInputBoundary):
    ...

    @staticmethod
    def to_input_boundary(
        request_input: SuggestCorrelationParametersAPIRequestInput = Body(...),
    ) -> SuggestCorrelationParametersUseCaseInputBoundary:
        return SuggestCorrelationParametersUseCaseInputBoundary.map(model=request_input)
