from __future__ import annotations

from typing import Sequence
from uuid import UUID

from fastapi import Body, Depends
from pydantic import Field

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.use_case.models.insert_use_case_input_boundary import (
    InsertUseCaseInput,
    InsertUseCaseInputBoundary,
)


class InsertUseCaseAPIRequestInput(BaseDataModel):
    documents: Sequence[InsertUseCaseInput] = Field(min_length=1)

    @staticmethod
    def to_input_boundary(
        body: InsertUseCaseAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> InsertUseCaseInputBoundary:
        return InsertUseCaseInputBoundary(
            documents=body.documents,
            owner_id=owner_id,
        )
