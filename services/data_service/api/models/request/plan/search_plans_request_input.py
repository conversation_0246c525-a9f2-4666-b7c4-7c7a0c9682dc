from __future__ import annotations

import json
import logging
from typing import Optional
from uuid import UUID

from fastapi import Body, Depends, Query
from fastapi.exceptions import RequestValidationError
from pydantic import Field

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.query.boolean_query_api import CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.api.query.mapper.single_document_type_query_mapper import SingleDocumentTypeQueryMapper
from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.application.database.models.sorts import Sort
from services.base.application.utils.encoders import decode_base_64
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.plan import Plan
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.boundaries.continuation_token_input import ContinuationTokenInput
from services.data_service.application.use_cases.plans.models.search_plans_input_boundary import (
    SearchPlansInputBoundary,
)


class SearchPlansRequestInput(BaseDataModel):
    sort: SortRequestInput = Field(
        default=SortRequestInput(field_name=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}")
    )
    query: LeafQueryAPIAnnotated | CompoundBooleanQueryAPI | None = Field(default=None)

    @staticmethod
    def to_input_boundary(
        owner_id: UUID = Depends(get_current_uuid),
        request_input: SearchPlansRequestInput = Body(...),
        limit: int = Query(default=100, gt=0, le=10000),
        continuation_token: Optional[str] = Query(default=None, min_length=1),
    ) -> SearchPlansInputBoundary:
        query = (
            SingleDocumentTypeQueryMapper.map(query=request_input.query, domain_type=Plan)
            if request_input.query
            else None
        )
        return SearchPlansInputBoundary(
            limit=limit,
            continuation_token=request_input._decode_continuation_token(continuation_token),
            query=query,
            sorts=[Sort(name=request_input.sort.field_name, order=request_input.sort.order)],
            owner_id=owner_id,
        )

    @staticmethod
    def _decode_continuation_token(continuation_token: Optional[str]) -> ContinuationTokenInput | None:
        if continuation_token:
            try:
                return ContinuationTokenInput(**json.loads(decode_base_64(encoded_token=continuation_token)))
            except Exception as error:
                logging.exception(error)
                raise RequestValidationError("Unable to decode continuation token.")
        else:
            return None
