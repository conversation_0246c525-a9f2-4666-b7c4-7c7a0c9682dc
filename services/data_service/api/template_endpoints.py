from uuid import UUID

from fastapi import API<PERSON>outer, Body, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadRe<PERSON>Exception,
    DuplicateDocumentsFound,
    IncorrectOperationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, TemplateRoutes
from services.data_service.api.mappers.template_api_output_mapper import TemplateAPIOutputMapper
from services.data_service.api.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.api.models.request.template.search_templates_request_input import (
    SearchTemplatesRequestInput,
)
from services.data_service.api.models.request.template.update_template_request_input import (
    UpdateTemplateAPIRequestInput,
)
from services.data_service.api.models.response.template.template_api_output import (
    TemplateAPIOutput,
)
from services.data_service.application.use_cases.templates.archive_template_use_case import (
    ArchiveTemplateUseCase,
)
from services.data_service.application.use_cases.templates.insert_template_use_case import (
    InsertTemplateUseCase,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.models.search_templates_input_boundary import (
    SearchTemplatesInputBoundary,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.search_template_use_case import SearchTemplatesUseCase
from services.data_service.application.use_cases.templates.update_template_use_case import (
    UpdateTemplatesUseCase,
)

template_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.TEMPLATE_PREFIX}",
    tags=["template"],
    responses={404: {"description": "Not found"}},
)


@template_router.post(
    TemplateRoutes.SEARCH,
    summary="Search Templates",
    description="""
    Search and retrieve templates with advanced filtering, sorting, and pagination capabilities.

    This endpoint allows you to find templates based on various criteria including name, type,
    tags, and other template properties. Results are returned in a paginated format with
    configurable sorting options.

    **Key Features:**
    - **Advanced Filtering**: Query by name, template type, tags, archived status, and more
    - **Flexible Sorting**: Sort by any template field (created_at, name, updated_at, etc.)
    - **Pagination**: Use limit and continuation_token for efficient data retrieval
    - **Boolean Queries**: Combine multiple filters with AND/OR logic
    - **Template Types**: Search across both event templates and group templates

    **Template Types:**
    - **Event Templates**: Contain structured event data for creating standardized events
    - **Group Templates**: Collections of related event templates for complex workflows

    **Common Use Cases:**
    - Find templates by name or partial name match
    - Filter templates by specific tags or categories
    - Retrieve recently created or modified templates
    - Search for templates of a specific type (event vs group)
    - Find non-archived templates for active use

    **Pagination:**
    Use the `limit` parameter to control page size (1-10000, default: 100).
    Use `continuation_token` from previous responses to fetch subsequent pages.
    """,
    response_description="List of templates matching the search criteria",
    responses={
        200: {
            "description": "Templates found and returned successfully",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning Exercise Template",
                                "tags": ["exercise", "morning", "routine"],
                                "document_type": "exercise",
                                "document_name": "Morning Workout",
                                "archived_at": None,
                                "created_at": "2024-01-15T08:00:00Z",
                                "updated_at": None,
                            }
                        ]
                    }
                }
            },
        },
        204: {"description": "No templates found matching the search criteria"},
        400: {"description": "Invalid search parameters or malformed request"},
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request parameters"},
    },
)
async def search_templates_endpoint(
    use_case: SearchTemplatesUseCase = Injected(SearchTemplatesUseCase),
    input_boundary: SearchTemplatesInputBoundary = Depends(SearchTemplatesRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    documents = await use_case.execute_async(input_boundary=input_boundary)
    if not documents:
        raise NoContentException("no templates found")

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.post(
    TemplateRoutes.BASE,
    summary="Create Templates",
    description="""
    Create new templates in the system for standardizing event creation and workflows.

    This endpoint allows you to create both event templates and group templates that can be used
    to streamline data entry and ensure consistency across your personal data collection.

    **Template Types:**

    **Event Templates:**
    - Contain pre-configured event data with standardized fields
    - Include event type, name, and structured document payload
    - Can be used to quickly create consistent events
    - Support all event types (exercise, nutrition, symptoms, medications, etc.)

    **Group Templates:**
    - Collections of related event templates for complex workflows
    - Can reference existing templates by ID or include embedded templates
    - Useful for multi-step routines or related event sequences
    - Support both separate template references and inline template definitions

    **Key Features:**
    - **Duplicate Detection**: Prevents creation of templates with identical details
    - **Validation**: Ensures all required fields are present and properly formatted
    - **Tagging**: Support for custom tags to organize and categorize templates
    - **Flexible Structure**: Accommodates various event types and data structures

    **Common Use Cases:**
    - Create exercise routine templates with pre-filled workout data
    - Build medication templates with dosage and timing information
    - Design symptom tracking templates with consistent rating scales
    - Establish group templates for morning/evening routines

    **Request Body:**
    The request should contain a `documents` array with template definitions. Each template
    must include a name, optional tags, and type-specific data (document for event templates,
    template_ids or templates for group templates).
    """,
    response_description="Successfully created templates with their generated IDs and metadata",
    responses={
        200: {
            "description": "Templates successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning Exercise Template",
                                "tags": ["exercise", "morning", "routine"],
                                "document_type": "exercise",
                                "document_name": "Morning Workout",
                                "document": {
                                    "type": "exercise",
                                    "name": "Morning Workout",
                                    "duration_minutes": 30,
                                    "intensity": "moderate",
                                },
                                "archived_at": None,
                                "created_at": "2024-01-15T08:00:00Z",
                                "updated_at": None,
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate templates or validation errors",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate": {
                            "summary": "Duplicate template",
                            "value": {
                                "detail": "Duplicate templates detected. A template with the same details already exists in the system."
                            },
                        },
                        "validation": {
                            "summary": "Validation error",
                            "value": {"detail": "Invalid template data: missing required fields"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def insert_template_endpoint(
    use_case: InsertTemplateUseCase = Injected(InsertTemplateUseCase),
    input_data: InsertTemplateAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(
            boundary=InsertTemplateInputBoundary.map(model=input_data),
            owner_id=owner_id,
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate templates detected. A template with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.patch(
    TemplateRoutes.BASE,
    summary="Update Templates",
    description="""
    Update existing templates with new data, maintaining their identity and relationships.

    This endpoint allows you to modify existing templates while preserving their unique identifiers
    and system metadata. You can update template names, tags, document content, and other properties
    for both event templates and group templates.

    **Update Capabilities:**
    - **Template Properties**: Modify name, tags, and other metadata
    - **Event Template Content**: Update document payload, event type, and structure
    - **Group Template Structure**: Modify template references and embedded templates
    - **Partial Updates**: Only specified fields are updated, others remain unchanged
    - **Batch Operations**: Update multiple templates in a single request

    **Key Features:**
    - **Identity Preservation**: Template IDs remain unchanged during updates
    - **Duplicate Detection**: Prevents updates that would create duplicate templates
    - **Validation**: Ensures updated data meets all requirements and constraints
    - **Atomic Operations**: All updates in a batch succeed or fail together
    - **Relationship Integrity**: Maintains valid references in group templates

    **Update Types:**

    **Event Template Updates:**
    - Modify event document structure and content
    - Update template name and descriptive information
    - Change tags and categorization
    - Adjust event type and associated metadata

    **Group Template Updates:**
    - Add or remove template references
    - Update embedded template definitions
    - Modify group structure and organization
    - Change template ordering and relationships

    **Common Use Cases:**
    - Refine template content based on usage patterns
    - Update template names for better organization
    - Modify event structures to capture additional data
    - Reorganize group templates for improved workflows
    - Update tags and metadata for better searchability

    **Request Body:**
    The request should contain a `documents` array with template updates. Each update
    must include the template ID and the fields to be modified. Only provided fields
    will be updated; omitted fields remain unchanged.

    **Important Notes:**
    - Template IDs must exist and belong to the authenticated user
    - Updates that would create duplicates are rejected
    - Group template references must point to valid, accessible templates
    - All validation rules apply to updated content
    """,
    response_description="Successfully updated templates with their modified data",
    responses={
        200: {
            "description": "Templates successfully updated",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Updated Morning Exercise Template",
                                "tags": ["exercise", "morning", "routine", "updated"],
                                "document_type": "exercise",
                                "document_name": "Enhanced Morning Workout",
                                "document": {
                                    "type": "exercise",
                                    "name": "Enhanced Morning Workout",
                                    "duration_minutes": 45,
                                    "intensity": "high",
                                },
                                "archived_at": None,
                                "created_at": "2024-01-15T08:00:00Z",
                                "updated_at": "2024-01-16T09:30:00Z",
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate templates, validation errors, or invalid references",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate": {
                            "summary": "Duplicate template",
                            "value": {"detail": "template duplicates found in the update payload"},
                        },
                        "validation": {
                            "summary": "Validation error",
                            "value": {"detail": "Invalid template data: template ID not found"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "One or more template IDs not found"},
        422: {"description": "Validation error in request body"},
    },
)
async def update_template_endpoint(
    use_case: UpdateTemplatesUseCase = Injected(UpdateTemplatesUseCase),
    input_data: UpdateTemplateAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(
            input_boundary=UpdateTemplateInputBoundary.map(model=input_data),
            owner_id=owner_id,
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="template duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.patch(
    TemplateRoutes.ARCHIVE,
    summary="Archive Templates",
    description="""
    Archive templates to remove them from active use while preserving their data and history.

    This endpoint allows you to archive one or more templates, marking them as inactive without
    permanently deleting them. Archived templates are excluded from search results by default
    but can still be retrieved if specifically requested, and their data remains intact for
    historical reference.

    **Archive Behavior:**
    - **Soft Deletion**: Templates are marked as archived, not permanently deleted
    - **Data Preservation**: All template data, metadata, and relationships are preserved
    - **Search Exclusion**: Archived templates are excluded from default search results
    - **Reversible Operation**: Archived templates can potentially be restored (implementation dependent)
    - **Batch Processing**: Multiple templates can be archived in a single operation

    **Key Features:**
    - **Identity Preservation**: Template IDs and all metadata remain unchanged
    - **Timestamp Recording**: Archive operation is timestamped for audit purposes
    - **Relationship Integrity**: Group template references to archived templates are handled gracefully
    - **Atomic Operations**: All templates in the request are archived together or the operation fails
    - **Validation**: Ensures all specified template IDs exist and belong to the user

    **Use Cases:**
    - **Template Lifecycle Management**: Remove outdated or unused templates from active use
    - **Seasonal Templates**: Archive templates that are only relevant at certain times
    - **Experimental Templates**: Archive test templates after evaluation
    - **Cleanup Operations**: Organize template library by removing obsolete items
    - **Workflow Optimization**: Reduce clutter in template selection interfaces

    **Archive Effects:**
    - Templates are marked with an `archived_at` timestamp
    - Default searches will not return archived templates
    - Group templates referencing archived templates may need attention
    - Template usage statistics and history are preserved
    - API responses will include the archive timestamp

    **Query Parameters:**
    - `template_ids`: Array of UUID strings identifying templates to archive
    - Multiple IDs can be provided to archive templates in batch
    - All specified templates must exist and belong to the authenticated user

    **Important Notes:**
    - Archiving is typically irreversible through this API (restoration may require separate operations)
    - Group templates containing references to archived templates should be reviewed
    - Archived templates may still appear in historical data and analytics
    - Consider the impact on existing workflows before archiving frequently-used templates
    """,
    response_description="Successfully archived templates with updated metadata",
    responses={
        200: {
            "description": "Templates successfully archived",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning Exercise Template",
                                "tags": ["exercise", "morning", "routine"],
                                "document_type": "exercise",
                                "document_name": "Morning Workout",
                                "archived_at": "2024-01-16T10:30:00Z",
                                "created_at": "2024-01-15T08:00:00Z",
                                "updated_at": "2024-01-16T09:30:00Z",
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - invalid template IDs or operation not allowed",
            "content": {
                "application/json": {
                    "example": {"detail": "One or more template IDs are invalid or do not belong to the user"}
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "One or more template IDs not found"},
        422: {"description": "Validation error in query parameters"},
    },
)
async def archive_templates_endpoint(
    use_case: ArchiveTemplateUseCase = Injected(ArchiveTemplateUseCase),
    template_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(owner_id=owner_id, template_ids=template_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])
