from uuid import UUID

from fastapi import API<PERSON>outer, Body, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.exceptions import (
    BadRe<PERSON>Exception,
    DuplicateDocumentsFound,
    IncorrectOperationException,
    NoContentException,
)
from services.data_service.api.constants import DataServicePrefixes, TemplateRoutes
from services.data_service.api.mappers.template_api_output_mapper import TemplateAPIOutputMapper
from services.data_service.api.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.api.models.request.template.search_templates_request_input import (
    SearchTemplatesRequestInput,
)
from services.data_service.api.models.request.template.update_template_request_input import (
    UpdateTemplateAPIRequestInput,
)
from services.data_service.api.models.response.template.template_api_output import (
    TemplateAPIOutput,
)
from services.data_service.application.use_cases.templates.archive_template_use_case import (
    ArchiveTemplateUseCase,
)
from services.data_service.application.use_cases.templates.insert_template_use_case import (
    InsertTemplateUseCase,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.models.search_templates_input_boundary import (
    SearchTemplatesInputBoundary,
)
from services.data_service.application.use_cases.templates.models.update_template_input_boundary import (
    UpdateTemplateInputBoundary,
)
from services.data_service.application.use_cases.templates.search_template_use_case import SearchTemplatesUseCase
from services.data_service.application.use_cases.templates.update_template_use_case import (
    UpdateTemplatesUseCase,
)

template_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.TEMPLATE_PREFIX}",
    tags=["template"],
    responses={404: {"description": "Not found"}},
)


@template_router.post(
    TemplateRoutes.SEARCH,
    summary="Search Templates",
    description="""
    Search and retrieve templates with advanced filtering, sorting, and pagination capabilities.

    This endpoint allows you to find templates based on various criteria including name, type,
    tags, and other template properties. Results are returned in a paginated format with
    configurable sorting options.

    **Key Features:**
    - **Advanced Filtering**: Query by name, template type, tags, archived status, and more
    - **Flexible Sorting**: Sort by any template field (created_at, name, updated_at, etc.)
    - **Pagination**: Use limit and continuation_token for efficient data retrieval
    - **Boolean Queries**: Combine multiple filters with AND/OR logic
    - **Template Types**: Search across both event templates and group templates

    **Template Types:**
    - **Event Templates**: Contain structured event data for creating standardized events
    - **Group Templates**: Collections of related event templates for complex workflows

    **Common Use Cases:**
    - Find templates by name or partial name match
    - Filter templates by specific tags or categories
    - Retrieve recently created or modified templates
    - Search for templates of a specific type (event vs group)
    - Find non-archived templates for active use

    **Pagination:**
    Use the `limit` parameter to control page size (1-10000, default: 100).
    Use `continuation_token` from previous responses to fetch subsequent pages.
    """,
    response_description="List of templates matching the search criteria",
    responses={
        200: {
            "description": "Templates found and returned successfully",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "name": "Morning Exercise Template",
                                "tags": ["exercise", "morning", "routine"],
                                "document_type": "exercise",
                                "document_name": "Morning Workout",
                                "archived_at": None,
                                "created_at": "2024-01-15T08:00:00Z",
                                "updated_at": None
                            }
                        ]
                    }
                }
            }
        },
        204: {"description": "No templates found matching the search criteria"},
        400: {"description": "Invalid search parameters or malformed request"},
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request parameters"}
    }
)
async def search_templates_endpoint(
    use_case: SearchTemplatesUseCase = Injected(SearchTemplatesUseCase),
    input_boundary: SearchTemplatesInputBoundary = Depends(SearchTemplatesRequestInput.to_input_boundary),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    documents = await use_case.execute_async(input_boundary=input_boundary)
    if not documents:
        raise NoContentException("no templates found")

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.post(TemplateRoutes.BASE)
async def insert_template_endpoint(
    use_case: InsertTemplateUseCase = Injected(InsertTemplateUseCase),
    input_data: InsertTemplateAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(
            boundary=InsertTemplateInputBoundary.map(model=input_data),
            owner_id=owner_id,
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(
            message="Duplicate templates detected. A template with the same details already exists in the system."
        ) from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.patch(TemplateRoutes.BASE)
async def update_template_endpoint(
    use_case: UpdateTemplatesUseCase = Injected(UpdateTemplatesUseCase),
    input_data: UpdateTemplateAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(
            input_boundary=UpdateTemplateInputBoundary.map(model=input_data),
            owner_id=owner_id,
        )
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="template duplicates found in the update payload") from err
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])


@template_router.patch(TemplateRoutes.ARCHIVE)
async def archive_templates_endpoint(
    use_case: ArchiveTemplateUseCase = Injected(ArchiveTemplateUseCase),
    template_ids: list[UUID] = Query(...),
    owner_id: UUID = Depends(get_current_uuid),
) -> CommonDocumentsResponse[TemplateAPIOutput]:
    try:
        documents = await use_case.execute_async(owner_id=owner_id, template_ids=template_ids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err

    return CommonDocumentsResponse[TemplateAPIOutput](documents=[TemplateAPIOutputMapper.map(d) for d in documents])
