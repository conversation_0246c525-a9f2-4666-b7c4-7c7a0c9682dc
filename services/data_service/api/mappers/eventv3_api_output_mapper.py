from services.base.domain.schemas.events.event import Event
from services.data_service.api.models.output.events.event_api_output_v3 import EventV3APIOutput
from services.data_service.type_resolver import TypeResolver


class EventV3APIOutputMapper:

    @classmethod
    def map(cls, document: Event) -> EventV3APIOutput:
        api_output_schema = TypeResolver.get_event_api_output_v3(type_id=document.type_id())
        return api_output_schema(**document.model_dump(by_alias=True))
