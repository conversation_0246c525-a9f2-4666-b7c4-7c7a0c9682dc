import os

from settings.app_config import ROOT_DIR

UNEXPECTED_RESULT = "Unexpected results format"
# Constants for API contract
DEFAULT_RESPONSE_VALUES = "Values"
DEFAULT_RESPONSE_STATUS = "Status"
DEFAULT_RESPONSE_STATISTICS = "Statistics"
DEFAULT_RESPONSE_STATUS_NEW_TIME_QUERIES = "NewTimeQueries"
# ALL API FILTER KEYS
API_FILTER_TIME_GTE = "time_gte"
API_FILTER_TIME_LTE = "time_lte"
API_FILTER_INTERVAL = "interval"
# API non-filter keys
RE_FETCH_MOST_RECENT = "re_fetch_most_recent"

DATETIME_OVERRIDE_VALUE = "2022-08-12T10:56:10.900+00:00"
DATETIME_UPDATED_OVERRIDE_VALUE = "2022-08-13T10:56:10.900+00:00"

# The de421.bsp file is a binary ephemeris file containing data for the positions and motions of solar system bodies,
# used for precise astronomical calculations.
EPHEMERIS_PATH: str = os.path.join(
    ROOT_DIR, "services/data_service/application/use_cases/calendar_aggregation", "de421.bsp"
)

# Uncomment if task manager is used
# DATA_SERVICE_QUEUE_WAIT_TIME: int = 40
# DATA_SERVICE_QUEUE_POLLING_TIME: int = 20

# Handlers

# Queues

# Topics
