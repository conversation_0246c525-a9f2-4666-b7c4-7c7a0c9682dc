from pydantic import Field

from services.base.domain.schemas.environment import EnvironmentMetadata
from services.base.domain.schemas.pollen import PollenFields, PollenSpecies
from services.base.domain.schemas.shared import CoordinatesModel, TimestampModel


class PollenBucket(TimestampModel):
    coordinates: CoordinatesModel = Field(alias=PollenFields.COORDINATES)
    metadata: EnvironmentMetadata = Field(alias=PollenFields.METADATA)
    tree: PollenSpecies | None = Field(alias=PollenFields.TREE)
    weed: PollenSpecies | None = Field(alias=PollenFields.WEED)
    grass: PollenSpecies | None = Field(alias=PollenFields.GRASS)
