from typing import Sequence
from uuid import UUID

from pydantic import Field, field_validator

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.events.use_case import UseCaseFields
from services.base.domain.schemas.shared import BaseDataModel


class InsertUseCaseInput(BaseDataModel):
    name: NonEmptyStr = Field(alias=UseCaseFields.NAME, min_length=1)
    tags: UniqueSequenceStr = Field(
        alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount, default_factory=list
    )


class InsertUseCaseInputBoundary(BaseDataModel):
    documents: Sequence[InsertUseCaseInput] = Field(min_length=1)
    owner_id: UUID

    @classmethod
    @field_validator("documents")
    def duplicates_validator(cls, documents: Sequence[InsertUseCaseInput]) -> Sequence[InsertUseCaseInput]:
        names = {d.name for d in documents}
        if len(names) != len(documents):
            raise ValueError("duplicate entries found")
        return documents
