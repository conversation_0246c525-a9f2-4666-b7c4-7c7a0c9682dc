import calendar
from collections import OrderedD<PERSON>
from typing import Optional, Sequence
from uuid import UUID

import numpy as np

from services.base.application.boundaries.aggregates import CalendarHistogramAggregate
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.domain.schemas.query.aggregations import (
    CalendarAggregationType,
    SimpleAggregationMethod,
)
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregation_common_methods import (
    CommonCalendarAggregationMethods,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregation_lunar_phase_determinator import (
    LunarPhaseDeterminator,
)


class CalendarHistogramAggregationUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[CalendarHistogramAggregate]


class CalendarHistogramAggregationUseCase:
    def __init__(self, lunar_determinator: LunarPhaseDeterminator, search_service: DocumentSearchService):
        self._search_service = search_service
        self._lunar_determinator = lunar_determinator

    async def execute_async(
        self,
        user_uuid: UUID,
        query: Query,
        field_name: str,
        aggregation_method: SimpleAggregationMethod,
        calendar_aggregation_type: CalendarAggregationType,
        fill_null_values: bool = True,
    ) -> Optional[CalendarHistogramAggregationUseCaseOutputBoundary]:
        query = CommonQueryAdjustments.add_user_uuid_to_query(query=query, user_uuid=user_uuid)

        response = await self._search_service.search_documents_by_query(
            query=query, size=10000, sorts=[CommonSorts.timestamp(order=SortOrder.ASCENDING)]
        )
        documents = response.documents
        if not documents:
            return None

        agg_result = self.calendar_aggregate(
            documents=documents,
            field_name=field_name,
            aggregation_method=aggregation_method,
            aggregation_type=calendar_aggregation_type,
            fill_null_values=fill_null_values,
        )

        return CalendarHistogramAggregationUseCaseOutputBoundary(
            results=agg_result,
        )

    def calendar_aggregate(
        self,
        documents: Sequence,
        field_name: str,
        aggregation_method: SimpleAggregationMethod,
        aggregation_type: CalendarAggregationType,
        fill_null_values: bool,
    ) -> list[CalendarHistogramAggregate]:
        aggregation = {}
        bucket_labels = []

        if aggregation_type == CalendarAggregationType.WEEKDAYS:
            for doc in documents:
                dt = doc.timestamp
                weekday_index = dt.weekday()
                weekday_names = CommonCalendarAggregationMethods.get_possible_keys(CalendarAggregationType.WEEKDAYS)
                bucket_key = weekday_names[weekday_index]
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                aggregation[bucket_key].append(getattr(doc, field_name))

        elif aggregation_type == CalendarAggregationType.MONTH_NAMES:
            for doc in documents:
                dt = doc.timestamp
                month_index = dt.month - 1
                month_names = CommonCalendarAggregationMethods.get_possible_keys(CalendarAggregationType.MONTH_NAMES)
                bucket_key = month_names[month_index]
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                aggregation[bucket_key].append(getattr(doc, field_name))

        elif aggregation_type == CalendarAggregationType.HOURS_IN_DAY:
            for doc in documents:
                dt = doc.timestamp
                hour = dt.hour
                bucket_key = str(hour)
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                aggregation[bucket_key].append(getattr(doc, field_name))

        elif aggregation_type == CalendarAggregationType.DAYS_OF_MONTH:
            for doc in documents:
                dt = doc.timestamp
                day = dt.day
                bucket_key = str(day)
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                aggregation[bucket_key].append(getattr(doc, field_name))

        elif aggregation_type == CalendarAggregationType.PARTS_OF_MONTH:
            for doc in documents:
                dt = doc.timestamp
                total_days = calendar.monthrange(dt.year, dt.month)[1]
                month_part = CommonCalendarAggregationMethods.get_month_part(dt.day, total_days)
                bucket_key = str(month_part)
                if bucket_key not in aggregation:
                    aggregation[bucket_key] = []
                aggregation[bucket_key].append(getattr(doc, field_name))

        elif aggregation_type == CalendarAggregationType.LUNAR_PHASES:
            phase_names = CommonCalendarAggregationMethods.get_possible_keys(aggregation_type=aggregation_type)
            for doc in documents:
                dt = doc.timestamp.date()
                phase_index = self._lunar_determinator.get_lunar_phase(doc_date=dt)
                phase_name = phase_names[phase_index]
                if phase_name not in aggregation:
                    aggregation[phase_name] = []
                aggregation[phase_name].append(getattr(doc, field_name))

        elif aggregation_type == CalendarAggregationType.TIME_BETWEEN:
            total_buckets = 5
            if len(documents) < 2:
                return [
                    CalendarHistogramAggregate(
                        aggregation_key=f"Bucket {i + 1}",
                        agg_method=aggregation_method.value,
                        doc_count=0,
                        value=None,
                    )
                    for i in range(total_buckets)
                ]

            time_differences = [
                (documents[i + 1].timestamp - documents[i].timestamp).total_seconds() for i in range(len(documents) - 1)
            ]

            min_diff, max_diff = min(time_differences), max(time_differences)
            bucket_edges = np.linspace(min_diff, max_diff, num=6)

            bucket_labels = [
                f"{bucket_edges[i]:.2f}s - {bucket_edges[i + 1]:.2f}s" for i in range(len(bucket_edges) - 1)
            ]

            time_between_aggregation = {label: [] for label in bucket_labels}

            for i in range(len(documents) - 1):
                diff = time_differences[i]
                bucket_index = min(np.digitize(diff, bucket_edges) - 1, len(bucket_labels) - 1)
                time_between_aggregation[bucket_labels[bucket_index]].append(getattr(documents[i], field_name))

            aggregation = time_between_aggregation

        # Fill null values
        if fill_null_values:
            if aggregation_type == CalendarAggregationType.TIME_BETWEEN:
                # For TIME_BETWEEN, preserve the bucket_labels order
                aggregation = OrderedDict(
                    (label, aggregation.get(label, [])) for label in bucket_labels
                )  # Initialize missing buckets with empty lists
            else:
                # For other calendar types, use _get_possible_keys to ensure all keys are present
                all_possible_keys = CommonCalendarAggregationMethods.get_possible_keys(
                    aggregation_type=aggregation_type
                )
                aggregation = OrderedDict(
                    (key, aggregation.get(key, [])) for key in all_possible_keys
                )  # Initialize missing buckets with empty lists

        # Calculate aggregation values
        aggregation_result = []
        for key, values in aggregation.items():
            doc_count = len(values)
            if aggregation_method == SimpleAggregationMethod.AVG:
                value = round(np.mean(values), 6) if values else None
            elif aggregation_method == SimpleAggregationMethod.SUM:
                value = sum(values)
            elif aggregation_method == SimpleAggregationMethod.MAX:
                value = max(values) if values else None
            elif aggregation_method == SimpleAggregationMethod.MIN:
                value = min(values) if values else None
            else:
                raise ValueError(f"Unsupported aggregation method {aggregation_method}")

            aggregation_result.append(
                CalendarHistogramAggregate(
                    aggregation_key=key,
                    agg_method=aggregation_method.value,
                    doc_count=doc_count,
                    value=float(value) if value is not None else None,
                )
            )

        return aggregation_result
