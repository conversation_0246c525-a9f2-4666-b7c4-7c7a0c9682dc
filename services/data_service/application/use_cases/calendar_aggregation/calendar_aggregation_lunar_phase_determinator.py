from datetime import date
from functools import lru_cache

from skyfield.almanac import moon_phases
from skyfield.api import load
from skyfield.searchlib import find_discrete
from skyfield.timelib import Time


class LunarPhaseDeterminator:
    def __init__(self, ephemeris_path: str):
        self.ts = load.timescale()
        self.eph = load(ephemeris_path)

    @lru_cache(maxsize=1000)
    def get_lunar_phase(self, doc_date: date) -> int:
        """
        Determine the lunar phase closest to a given date.

        This method calculates the lunar phase (e.g., New Moon, First Quarter,
        Full Moon, Last Quarter) that occurs closest to a specified date. It uses
        Skyfield's ephemeris data and time system to perform precise astronomical
        calculations.

        Args:
            ts (Timescale): A Skyfield Timescale object used to create time instances.
            eph: A Skyfield ephemeris object containing astronomical data.
            doc_date (datetime.date): The date for which to calculate the closest lunar phase.

        Returns:
            int: The index of the lunar phase closest to the given date.
                 Possible values are:
                 - 0: New Moon
                 - 1: First Quarter
                 - 2: Full Moon
                 - 3: Last Quarter

        Raises:
            ValueError: If no lunar phases are found within ±7 days of the given date.
        """
        t = self.ts.utc(doc_date.year, doc_date.month, doc_date.day)
        phase_times, phase_types = find_discrete(t - 7, t + 7, moon_phases(self.eph))

        if phase_types.size == 0:
            raise ValueError("No lunar phase found in the given range")

        # Find the exact phase at or just before `t`
        # best_match: Tuple(Time_object, phase_index)
        best_match: tuple[Time, int] = min(
            ((pt, phase) for pt, phase in zip(phase_times, phase_types)),
            key=lambda x: abs(x[0].tt - t.tt),
        )

        return best_match[1]
