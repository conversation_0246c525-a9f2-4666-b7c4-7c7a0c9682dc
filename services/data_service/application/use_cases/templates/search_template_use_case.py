from typing import Sequence

from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.templates.template import Template
from services.data_service.application.use_cases.templates.models.search_templates_input_boundary import (
    SearchTemplatesInputBoundary,
)


class SearchTemplatesUseCase:
    def __init__(self, template_repo: TemplateRepository):
        self._template_repo = template_repo

    async def execute_async(self, input_boundary: SearchTemplatesInputBoundary) -> Sequence[Template]:
        query = CommonQueryAdjustments.add_user_uuid_to_query(
            user_uuid=input_boundary.owner_id, query=input_boundary.query
        )
        response = await self._template_repo.search_by_query(query=query, size=10_000)
        return response.documents
