import re
from urllib.parse import urlparse

from httpx import AsyncClient, HTTPError
from pydantic import HttpUrl
from scrapy import Selector

from services.base.application.exceptions import NoContentException
from services.base.domain.schemas.events.content.text import TextCategory
from services.data_service.application.use_cases.content.content_lookup_boundaries import ContentLookupOutputBoundary
from services.data_service.application.use_cases.content.handlers.content_handler_base import ContentHandlerBase
from services.data_service.application.use_cases.content.models.x_post import XPost


class XHandler(ContentHandlerBase):
    """Handler for X (formerly Twitter) content that includes service functionality."""

    _TIMEOUT = 5
    _API_HEADERS = {
        "User-Agent": "Mozilla/5.0 (compatible; XService/1.0)",
        "Accept": "application/json",
    }

    # Matches URLs like:
    # - https://x.com/username/status/123456789
    # - https://twitter.com/username/status/123456789
    # - http://www.twitter.com/username/status/123456789?s=20
    URL_PATTERN = re.compile(
        r"https?://(?:www\.)?(?:twitter\.com|x\.com)/(?P<username>[^/]+)/status/(?P<post_id>\d+)/?(?:\?.*)?$",
        re.IGNORECASE,
    )

    def __init__(self, client: AsyncClient):
        self._client = client

    async def can_handle(self, url: str) -> bool:
        parsed_url = urlparse(url)
        return parsed_url.netloc.replace("www.", "") in ["x.com", "twitter.com"]

    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        try:
            http_url = HttpUrl(url)
        except Exception as e:
            raise NoContentException(f"Invalid URL format: {str(e)}")

        if not self._is_valid_x_url(url=http_url):
            raise NoContentException(f"Invalid X post URL format: {url}")

        post = await self._get_post_details(url=http_url)
        return self._normalize_output(
            title=post.title,
            type=TextCategory.TEXT,
            description=post.description,
        )

    @staticmethod
    def _is_valid_x_url(url: HttpUrl) -> bool:
        """
        Validates if a URL matches the X post format.
        Must use x.com/twitter.com domain and include username/status ID.
        """
        return bool(XHandler.URL_PATTERN.match(str(url)))

    @staticmethod
    def _extract_post_text(html_content: str) -> str:
        """Extracts text content from HTML, in order of preference: p tags with lang, then blockquote."""
        selector = Selector(text=html_content)

        text = " ".join(selector.css("p[lang]::text").getall()).strip()

        if not text:
            text = selector.css("blockquote::text").get("").strip()

        if not text:
            text = " ".join(selector.css("blockquote *::text").getall()).strip()

        text = " ".join(text.split())

        return text

    async def _get_post_details(self, url: HttpUrl) -> XPost:
        """Fetches and formats X post content using the oembed API."""
        match = XHandler.URL_PATTERN.match(str(url))
        if not match:
            raise NoContentException("Invalid X post URL format")

        username = match.group("username")
        post_id = match.group("post_id")
        post_url = f"https://x.com/{username}/status/{post_id}"
        oembed_url = f"https://publish.twitter.com/oembed?url={post_url}"

        try:
            oembed_response = await self._client.get(
                url=oembed_url,
                timeout=XHandler._TIMEOUT,
                headers=XHandler._API_HEADERS,
            )

            if oembed_response.status_code == 200:
                oembed_data = oembed_response.json()
                if html_content := oembed_data.get("html"):
                    if post_text := XHandler._extract_post_text(html_content):
                        author = oembed_data.get("author_name", username)

                        return XPost(
                            title=f"Post by {author} (@{username})".strip(),
                            description=post_text.strip() if post_text else None,
                        )

            raise NoContentException(f"Could not retrieve X post content for {post_url}")

        except HTTPError as e:
            raise NoContentException(f"HTTP error while fetching X post: {str(e)}")
        except Exception as e:
            raise NoContentException(f"Unexpected error: {str(e)}")
