from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.schemas.events.content.audio import AudioCategory
from services.base.domain.schemas.events.content.content import ContentCategory
from services.base.domain.schemas.events.content.image import ImageCategory
from services.base.domain.schemas.events.content.interactive import InteractiveCategory
from services.base.domain.schemas.events.content.text import TextCategory
from services.base.domain.schemas.events.content.video import VideoCategory
from services.base.domain.schemas.shared import BaseDataModel


class ContentLookupOutputBoundary(BaseDataModel):
    title: NonEmptyStr = Field(description="title of the webpage as provided by the url")
    type: ContentCategory | AudioCategory | VideoCategory | TextCategory | ImageCategory | InteractiveCategory = Field(
        description="type of the content, e.g. article, video, website"
    )
    description: NonEmptyStr | None = Field(
        description="description provided by the website about the url",
    )
