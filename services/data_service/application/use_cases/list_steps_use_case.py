from typing import List, Optional, Union
from uuid import UUID

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.time_input import TimeInput, TimeRangeInput
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.shared import BaseDataModel, TimestampModel
from services.data_service.application.use_cases.helpers.do_list_query_helper import DoListQueryHelper


class ListStepsUseCaseOutputItem(TimestampModel):
    steps: int


class ListStepsUseCaseOutputBoundary(BaseDataModel):
    results: List[ListStepsUseCaseOutputItem]
    re_fetch_time_input: Optional[Union[TimeInput, TimeRangeInput]] = None


class ListStepsUseCase:
    def __init__(self, search_service: DocumentSearchService, do_list_query: DoListQueryHelper):
        self._search_service = search_service
        self._do_list_query = do_list_query

    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: Union[TimeInput, TimeRangeInput],
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
    ) -> ListStepsUseCaseOutputBoundary:
        and_query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    metadata.to_and_query(),
                    CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid),
                ]
            )
            .build_and_query()
        )

        results, used_time_input = await self._do_list_query.execute_async(
            query=and_query,
            time_input=time_input,
            re_fetch=re_fetch,
            data_type=DataType.Steps,
            return_type=ListStepsUseCaseOutputItem,
        )
        return ListStepsUseCaseOutputBoundary(
            results=results, re_fetch_time_input=used_time_input if used_time_input != time_input else None
        )
