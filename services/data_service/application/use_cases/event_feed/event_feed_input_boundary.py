from services.base.application.database.models.sorts import Sort
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.models.event_feed_continuation_token import EventFeedContinuationToken


class EventFeedInputBoundary(BaseDataModel):
    limit: int
    sort: Sort
    continuation_token: EventFeedContinuationToken | None = None
    query: Query
