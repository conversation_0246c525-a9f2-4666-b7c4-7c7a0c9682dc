from typing import Optional, Sequence, cast
from uuid import UUID

from pydantic import Field

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.application.exceptions import IncorrectOperationException, NoContentException
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.data_service.application.models.event_feed_continuation_token import EventFeedContinuationToken
from services.data_service.application.use_cases.event_feed.event_feed_input_boundary import EventFeedInputBoundary

EXC_MESSAGE_QUERY_MUTATED = "filter parameters differ from parameters that the continuation token was created with"


class EventFeedUseCaseOutputBoundary(BaseDataModel):
    events: Sequence[Event | DeprEventModel] = Field(...)
    continuation_token: EventFeedContinuationToken = Field(...)


class EventFeedUseCase:

    def __init__(self, search_service: DocumentSearchService):
        self._search_service = search_service

    async def execute_async(
        self,
        user_uuid: UUID,
        input_boundary: EventFeedInputBoundary,
    ) -> EventFeedUseCaseOutputBoundary:
        self._validate_continuation_token(
            continuation_token=input_boundary.continuation_token, query=input_boundary.query
        )
        query = CommonQueryAdjustments.add_user_uuid_to_query(query=input_boundary.query, user_uuid=user_uuid)

        response = await self._search_service.search_documents_by_query(
            size=input_boundary.limit,
            query=query,
            sorts=[input_boundary.sort, CommonSorts.internal_id(order=input_boundary.sort.order)],
            continuation_token=input_boundary.continuation_token.token if input_boundary.continuation_token else None,
        )

        if not response.documents:
            raise NoContentException(message="No documents were found for any datatype")

        new_continuation_token = EventFeedContinuationToken(
            token=response.continuation_token, query=input_boundary.query
        )

        return EventFeedUseCaseOutputBoundary(
            continuation_token=new_continuation_token,
            events=cast(Sequence[Event | DeprEventModel], response.documents),
        )

    def _validate_continuation_token(
        self, continuation_token: Optional[EventFeedContinuationToken], query: Query
    ) -> None:
        if continuation_token is None:
            return None

        if query != continuation_token.query:
            raise IncorrectOperationException(message=EXC_MESSAGE_QUERY_MUTATED)
