from datetime import datetime

from pydantic import model_validator
from pydantic.fields import Field

from services.base.application.input_validators.shared import InputTimestampModel
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.sleep_stages import SleepStage


class LoadSleepInput(InputTimestampModel):
    sleep_stage: SleepStage
    end_time: datetime = Field(alias=DocumentLabels.END_TIME)

    @model_validator(mode="after")
    def validate_datetime_range_filter(self):
        if self.timestamp > self.end_time:
            raise ValueError("timestamp has to be less than end_time")

        return self
