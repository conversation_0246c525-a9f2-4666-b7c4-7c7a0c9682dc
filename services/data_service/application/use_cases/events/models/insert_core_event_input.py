from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.core_event import (
    CoreEvent,
    CoreEventCategory,
    CoreEventFields,
    CoreEventIdentifier,
)
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertCoreEventInput(InsertEventInput, CoreEventIdentifier):
    type: Literal[DataType.CoreEvent] = Field(alias=EventFields.TYPE)
    category: CoreEventCategory = Field(alias=CoreEventFields.CATEGORY)
    rating: int | None = Field(
        alias=CoreEventFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        default=None,
    )
    note: NonEmptyStr | None = Field(
        alias=CoreEventFields.NOTE, default=None, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self, ctx: EventInsertionContext) -> CoreEvent:
        return CoreEvent(
            # technical
            type=DataType.CoreEvent,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            # specific
            note=self.note,
            rating=self.rating,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )
