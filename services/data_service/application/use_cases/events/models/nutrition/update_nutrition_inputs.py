from abc import ABC
from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory, DrinkIdentifier
from services.base.domain.schemas.events.nutrition.food import FoodCategory, FoodIdentifier
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionCollection, NutritionFields
from services.base.domain.schemas.events.nutrition.supplement import (
    SupplementCategory,
    SupplementIdentifier,
)
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateNutritionInputBase(UpdateEventInput, NutritionCollection, ABC):  # noqa: F821

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.rating,
            ]
        )


class UpdateDrinkInput(UpdateNutritionInputBase, DrinkIdentifier):
    type: Literal[DataType.Drink] = Field(alias=NutritionFields.TYPE)
    category: DrinkCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: VolumeUnit | Literal["item", "serving"] | None = Field(alias=NutritionFields.CONSUMED_TYPE)


class UpdateFoodInput(UpdateNutritionInputBase, FoodIdentifier):
    type: Literal[DataType.Food] = Field(alias=NutritionFields.TYPE)
    category: FoodCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] | None = Field(
        alias=NutritionFields.CONSUMED_TYPE
    )


class UpdateSupplementInput(UpdateNutritionInputBase, SupplementIdentifier):
    type: Literal[DataType.Supplement] = Field(alias=NutritionFields.TYPE)
    category: SupplementCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] | None = Field(
        alias=NutritionFields.CONSUMED_TYPE
    )


UpdateNutritionInputs = UpdateDrinkInput | UpdateFoodInput | UpdateSupplementInput
