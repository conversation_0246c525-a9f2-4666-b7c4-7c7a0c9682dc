from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.events.document_base import (
    RBACSchema,
)
from services.base.domain.schemas.records.sleep_record import SleepR<PERSON>ord, SleepRecordFields, SleepRecordIdentifier
from services.data_service.application.models.shared import InputTimeIntervalStrict
from services.data_service.application.use_cases.records.models.record_insertion_context import RecordInsertionContext


class InsertSleepRecordInput(InputTimeIntervalStrict, SleepRecordIdentifier):
    type: Literal[DataType.SleepRecord] = Field(alias=SleepRecordFields.TYPE)
    stage: SleepStage = Field(alias=SleepRecordFields.STAGE)

    def to_domain(self, ctx: RecordInsertionContext) -> SleepRecord:
        return SleepRecord(
            rbac=RBACSchema(owner_id=ctx.owner_id),
            id=uuid4(),
            type=self.type,
            timestamp=self.timestamp,
            end_time=self.end_time,
            stage=self.stage,
            metadata=ctx.metadata,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        content_string: str = "".join(
            [
                self.timestamp.isoformat(),
            ]
        )
        return Hasher.content_sha256(content=content_string)
