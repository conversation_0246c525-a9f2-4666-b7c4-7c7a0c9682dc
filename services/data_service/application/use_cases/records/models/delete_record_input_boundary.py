from typing import Sequence

from pydantic import Field, computed_field, field_validator

from services.base.domain.content_hash import Hasher
from services.base.domain.enums.record_type import RecordType
from services.base.domain.schemas.events.document_base import DocumentLabels
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.events.models.shared import IdentifiableInputDocument


class DeleteRecordInput(IdentifiableInputDocument):
    type: RecordType = Field(alias=DocumentLabels.TYPE)

    @computed_field()
    @property
    def content_hash(self) -> str:
        content_string: str = "".join(
            [
                self.type.value,
                str(self.id),
            ]
        )
        return Hasher.content_sha256(content=content_string)


class DeleteRecordInputBoundary(BaseDataModel):
    documents: Sequence[DeleteRecordInput] = Field(min_length=1)

    @field_validator("documents")
    def duplicates_validator(cls, documents: Sequence[DeleteRecordInput]) -> Sequence[DeleteRecordInput]:
        content_hashes = {d.content_hash for d in documents}
        if len(content_hashes) != len(documents):
            raise ValueError("duplicate entries found")
        return documents
