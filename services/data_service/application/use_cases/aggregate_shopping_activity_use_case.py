from datetime import datetime
from typing import Dict, List, Optional, Sequence
from uuid import UUID

from pydantic import Field

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel, TimestampModel
from services.base.domain.schemas.shopping_activity import ShoppingActivity, ShoppingActivityFields
from services.base.domain.schemas.user import User
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_time_aggregated_fields_async,
)
from services.base.infrastructure.database.opensearch.query_methods.utils import are_results_empty
from services.data_service.application.utils.find_latest_entries import find_latest_entries
from services.data_service.application.utils.metadata_filters import prepare_metadata_query


class AggregateShoppingActivityUseCaseOutput(BaseDataModel):
    key: str
    doc_count: int = Field(ge=0, default=0)
    subtotal: float = Field(default=0, ge=0)
    subtotal_tax: float = Field(default=0, ge=0)
    total: float = Field(default=0, ge=0)


class AggregateShoppingActivityUseCaseOutputItem(TimestampModel):
    category: Optional[Dict[str, AggregateShoppingActivityUseCaseOutput]] = Field(default=None)
    subtotal: Optional[float] = Field(default=None, ge=0)
    subtotal_tax: Optional[float] = Field(default=None, ge=0)
    total: Optional[float] = Field(default=None, ge=0)
    quantity: Optional[int] = Field(default=None, ge=0)


class AggregateShoppingActivityUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[AggregateShoppingActivityUseCaseOutputItem]
    re_fetch_time_input: Optional[TimeInput] = Field(default=None)


class AggregateShoppingActivityUseCase:
    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: TimeInput,
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
    ) -> AggregateShoppingActivityUseCaseOutputBoundary:
        label_item_detail = f"{ShoppingActivityFields.ITEM_DETAIL}"
        label_subtotal = f"{label_item_detail}.{ShoppingActivityFields.ITEM_SUBTOTAL}"
        label_subtotal_tax = f"{label_item_detail}.{ShoppingActivityFields.ITEM_SUBTOTAL_TAX}"
        label_total = f"{label_item_detail}.{ShoppingActivityFields.ITEM_TOTAL}"
        label_quantity = f"{label_item_detail}.{ShoppingActivityFields.QTY}"
        label_category = f"{label_item_detail}.{ShoppingActivityFields.CATEGORY}"
        label_sum = "sum"

        custom_sub_aggs = {
            "fields": {
                "top_hits": {
                    "size": 100,
                    "_source": [
                        label_category,
                        label_total,
                        label_subtotal,
                        label_subtotal_tax,
                    ],
                }
            },
            f"{label_subtotal}_{label_sum}": {label_sum: {"field": "item_detail.item_subtotal"}},
            f"{label_subtotal_tax}_{label_sum}": {label_sum: {"field": "item_detail.item_subtotal_tax"}},
            f"{label_total}_{label_sum}": {label_sum: {"field": "item_detail.item_total"}},
            f"{label_quantity}_{label_sum}": {label_sum: {"field": "item_detail.qty"}},
        }

        and_query = (
            BooleanQueryBuilder()
            .add_queries(queries=prepare_metadata_query(metadata_input=metadata))
            .add_query(query=CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[ShoppingActivity], query=and_query)])

        results = await get_time_aggregated_fields_async(
            custom_sub_aggs=custom_sub_aggs,
            time_gte=time_input.time_gte,
            time_lte=time_input.time_lte,
            interval=time_input.interval,
            query=query,
        )

        if are_results_empty(results):
            if not re_fetch:
                raise NoContentException("No shopping data available for the given time range.")

            new_time_input = TimeInput.map(
                model=find_latest_entries(
                    data_type=DataType.ShoppingActivity,
                    current_user=User(user_uuid=user_uuid),
                    old_parameters=time_input,
                ),
                fields={"interval": time_input.interval},
            )
            result: AggregateShoppingActivityUseCaseOutputBoundary = await self.execute_async(
                user_uuid=user_uuid,
                time_input=new_time_input,
                metadata=metadata,
            )
            result.re_fetch_time_input = new_time_input
            return result

        # Filter and process aggregation part
        aggregation_results = results["aggregations"]["requested_histogram"]["buckets"]

        output_results: List[AggregateShoppingActivityUseCaseOutputItem] = []

        for entity in aggregation_results:
            shopping_model = AggregateShoppingActivityUseCaseOutputItem(
                timestamp=entity.get("key_as_string"),
                subtotal=entity.get(f"{label_subtotal}_{label_sum}").get("value"),
                subtotal_tax=entity.get(f"{label_subtotal_tax}_{label_sum}").get("value"),
                total=entity.get(f"{label_total}_{label_sum}").get("value"),
                quantity=entity.get(f"{label_quantity}_{label_sum}").get("value"),
            )
            output_results.append(shopping_model)

        category_sub_aggregations: Dict[datetime, Dict[str, AggregateShoppingActivityUseCaseOutput]] = {}

        # One sub aggregation per output result time bucket
        for bucket in aggregation_results:
            timestamp = bucket["key_as_string"]
            category_sub_aggregations[timestamp] = {}

            for entry in bucket["fields"]["hits"]["hits"]:
                data: List = entry["_source"][label_item_detail]
                for order in data:
                    name = order.get(ShoppingActivityFields.CATEGORY)
                    category_entity: AggregateShoppingActivityUseCaseOutput = category_sub_aggregations[timestamp].get(
                        name, AggregateShoppingActivityUseCaseOutput(key=name)
                    )
                    category_entity.doc_count += 1
                    category_entity.total += round(order[ShoppingActivityFields.ITEM_TOTAL], 2)
                    category_entity.subtotal += round(order[ShoppingActivityFields.ITEM_SUBTOTAL], 2)
                    category_entity.subtotal_tax += round(order[ShoppingActivityFields.ITEM_SUBTOTAL_TAX], 2)
                    category_sub_aggregations[timestamp][name] = category_entity

        for output, category_sub_aggregation in zip(output_results, category_sub_aggregations.values()):
            if category_sub_aggregation:
                output.category = category_sub_aggregation

        return AggregateShoppingActivityUseCaseOutputBoundary(results=output_results)
