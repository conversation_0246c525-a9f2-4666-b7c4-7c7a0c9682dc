from typing import Sequence

from pydantic import Field

from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_provider import ExtensionProvider
from services.base.domain.schemas.shared import BaseDataModel


class ListExtensionProvidersOutputBoundary(BaseDataModel):
    results: Sequence[ExtensionProvider] = Field(...)


class ListExtensionProvidersUseCase:
    def __init__(self, extension_provider_repository: ExtensionProviderRepository):
        self._extension_provider_repository = extension_provider_repository

    async def execute_async(self) -> ListExtensionProvidersOutputBoundary:
        extension_providers = await self._extension_provider_repository.get(
            wrapper=ReadFromDatabaseWrapper(search_keys={})
        )
        return ListExtensionProvidersOutputBoundary(results=extension_providers)
