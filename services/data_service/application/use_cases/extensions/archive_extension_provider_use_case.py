from datetime import datetime, timezone
from uuid import UUID

from services.base.application.exceptions import NoContentException
from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.schemas.extensions.extension_provider import ExtensionProvider


class ArchiveExtensionProviderUseCase:
    async def execute_async(
        self, extension_provider_repository: ExtensionProviderRepository, provider_id: UUID
    ) -> ExtensionProvider:
        extension_provider = await extension_provider_repository.get_by_provider_id(provider_id=provider_id)
        if not extension_provider:
            raise NoContentException(f"Extension provider with given id {provider_id} not found.")
        extension_provider.archived_at = datetime.now(timezone.utc)
        # TODO(jaja): Archive all existing extensions ?
        return (await extension_provider_repository.upsert(extension_providers=[extension_provider]))[0]
