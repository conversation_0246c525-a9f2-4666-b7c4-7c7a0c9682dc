from datetime import datetime, timezone
from uuid import UUID

from services.base.application.exceptions import NoContentException
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail


class ArchiveExtensionUseCase:
    async def execute_async(
        self, extension_detail_repository: ExtensionDetailRepository, extension_id: UUID
    ) -> ExtensionDetail:
        extension_detail = await extension_detail_repository.get_by_extension_id(extension_id=extension_id)
        if not extension_detail:
            raise NoContentException(f"Extension provider with given id {extension_id} not found.")
        extension_detail.archived_at = datetime.now(timezone.utc)
        return (await extension_detail_repository.upsert(extension_details=[extension_detail]))[0]
