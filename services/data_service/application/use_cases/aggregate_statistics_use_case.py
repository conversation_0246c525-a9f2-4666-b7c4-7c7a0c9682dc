from typing import List, Optional
from uuid import UUID

from pydantic import Field

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.application.exceptions import BadRequestException, NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.heart_rate import HeartRateFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.resting_heart_rate import RestingHeartRateFields
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.sleep import SleepFields, SleepSummaryFields
from services.base.domain.schemas.steps import StepsFields
from services.base.domain.schemas.user import User
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import OpenSearchWrapper
from services.base.infrastructure.database.opensearch.query_methods.utils import (
    are_results_empty,
)
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client
from services.data_service.application.utils.find_latest_entries import find_latest_entries
from services.data_service.application.utils.metadata_filters import prepare_metadata_query
from services.data_service.application.utils.number_convertors import replace_nan_with_none

statistics_aggregation_fields_per_data_type = {
    EventType.HeartRate: [
        HeartRateFields.BPM_MAX,
        HeartRateFields.BPM_MIN,
        HeartRateFields.BPM_AVG,
    ],
    EventType.RestingHeartRate: [
        RestingHeartRateFields.BPM_MIN,
        RestingHeartRateFields.BPM_MAX,
        RestingHeartRateFields.BPM_AVG,
    ],
    EventType.Sleep: [
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.EFFICIENCY}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.AWAKE_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.EVENTS_COUNT}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.FALL_ASLEEP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.AFTER_WAKEUP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.ASLEEP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.IN_BED_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.DEEP_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.LIGHT_SECONDS}",
        f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}.{SleepSummaryFields.REM_SECONDS}",
    ],
    EventType.Steps: [StepsFields.STEPS],
}

_client = get_async_default_os_client()


class AggregateStatisticsUseCaseStandardDeviationBounds(BaseDataModel):
    upper: float = Field(alias="upper")
    lower: float = Field(alias="lower")
    upper_population: float = Field(alias="upper_population")
    lower_population: float = Field(alias="lower_population")
    upper_sampling: Optional[float] = Field(alias="upper_sampling", default=None)
    lower_sampling: Optional[float] = Field(alias="lower_sampling", default=None)


class AggregateStatisticsUseCaseExtendedStats(BaseDataModel):
    count: int = Field(alias="count")
    min: float = Field(alias="min")
    max: float = Field(alias="max")
    avg: float = Field(alias="avg")
    sum: float = Field(alias="sum")
    sum_of_squares: float = Field(alias="sum_of_squares")
    variance: float = Field(alias="variance")
    variance_population: float = Field(alias="variance_population")
    variance_sampling: Optional[float] = Field(alias="variance_sampling", default=None)
    std_deviation: float = Field(alias="std_deviation")
    std_deviation_population: float = Field(alias="std_deviation_population")
    std_deviation_sampling: Optional[float] = Field(alias="std_deviation_sampling", default=None)
    std_deviation_bounds: Optional[AggregateStatisticsUseCaseStandardDeviationBounds] = Field(
        alias="std_deviation_bounds", default=None
    )


class AggregateStatisticsUseCaseOutputItem(BaseDataModel):
    aggregation_name: str = Field(alias="aggregation_name")
    extended_stats: AggregateStatisticsUseCaseExtendedStats = Field(
        description="Extended stats", alias="extended_stats"
    )


class AggregateStatisticsUseCaseOutputBoundary(BaseDataModel):
    statistics: List[AggregateStatisticsUseCaseOutputItem] = Field(description="List of statistics aggregation results")
    re_fetch_time_input: Optional[TimeRangeInput] = Field(alias="re_fetch_time_input", default=None)


def create_custom_sub_aggregations(requested_fields: List[str]):
    custom_sub_aggregations = {}
    for field in requested_fields:
        if "." in field:
            field_name = field.split(".")[-1]
        else:
            field_name = field
        custom_sub_aggregations[field_name + "_extended_stats"] = {"extended_stats": {"field": field}}
    return custom_sub_aggregations


class AggregateStatisticsUseCase:
    async def execute_async(
        self,
        user_uuid: UUID,
        event_type: EventType,
        time_input: TimeRangeInput,
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
    ) -> AggregateStatisticsUseCaseOutputBoundary:
        if event_type not in statistics_aggregation_fields_per_data_type:
            raise BadRequestException(f"Data type: {event_type.value} is not supported")

        requested_fields = statistics_aggregation_fields_per_data_type[event_type]
        custom_sub_aggregations = create_custom_sub_aggregations(requested_fields)

        and_query = (
            BooleanQueryBuilder()
            .add_queries(queries=prepare_metadata_query(metadata_input=metadata))
            .add_query(CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
            .add_query(CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid))
        ).build_and_query()
        query = Query(type_queries=[TypeQuery(domain_types=[event_type.to_domain_model()], query=and_query)])
        query_result = QueryTranslator.translate(query)

        aggs = OpenSearchWrapper.build_aggregation_query(
            requested_fields_and_agg=requested_fields,
            time_gte=time_input.time_gte,
            time_lte=time_input.time_lte,
            custom_sub_aggs=custom_sub_aggregations,
            time_field_name=DocumentLabels.TIMESTAMP,
        )

        body = (query_result.query_as_dict or {}) | {"aggregations": aggs}

        aggregations = body["aggregations"]["requested_histogram"]["aggs"]
        body["aggregations"] = aggregations

        results = await _client.search(
            body=body,
            params={"size": 1000},
            index=query_result.indices,
        )

        if are_results_empty(results):
            if not re_fetch:
                raise NoContentException("No data available for the given time range.")

            new_time_input = find_latest_entries(
                data_type=DataType(event_type.value),
                current_user=User(user_uuid=user_uuid),
                old_parameters=time_input,
            )

            result: AggregateStatisticsUseCaseOutputBoundary = await self.execute_async(
                user_uuid=user_uuid,
                event_type=event_type,
                time_input=new_time_input,
                metadata=metadata,
            )
            result.re_fetch_time_input = new_time_input
            return result

        output_list: List[AggregateStatisticsUseCaseOutputItem] = []

        for key, value in results["aggregations"].items():
            value = replace_nan_with_none(value)
            output_list.append(
                AggregateStatisticsUseCaseOutputItem(
                    aggregation_name=key, extended_stats=AggregateStatisticsUseCaseExtendedStats(**value)
                )
            )

        return AggregateStatisticsUseCaseOutputBoundary(statistics=output_list)
