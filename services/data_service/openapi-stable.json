{"openapi": "3.1.0", "info": {"title": "Data Service (Stable)", "version": "v3"}, "paths": {"/health": {"get": {"summary": "Endpoint", "operationId": "endpoint_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v3/asset/by_id/": {"get": {"tags": ["assets"], "summary": "Fetch Asset By Id", "description": "Fetch and stream an asset by its ID.\n\nRetrieves an asset (such as an image, document, or other file) from the user's\nstorage container and streams it back to the client. The asset is returned as\na streaming response to efficiently handle large files.\n\nArgs:\n    user_uuid: The UUID of the authenticated user making the request\n    asset_id: The unique identifier of the asset to fetch. Must match the\n             asset ID regex pattern and have minimum length of 1\n    fetch_assets_use_case: Injected use case for fetching assets by ID\n\nReturns:\n    StreamingResponse: A streaming response containing the asset content with\n                      media type set to \"image/jpeg\"\n\nRaises:\n    404: Asset not found or user doesn't have access to the asset\n    400: Invalid asset ID format\n    401: User not authenticated", "operationId": "fetch_asset_by_id_v3_asset_by_id__get", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "pattern": "^[A-Za-z0-9._-]+$", "title": "Asset Id"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/asset/url/": {"get": {"tags": ["assets"], "summary": "Fetch Asset U<PERSON>", "description": "Fetch URLs for multiple assets by their IDs.\n\nRetrieves pre-signed URLs for multiple assets from the user's storage container.\nThese URLs can be used to directly access the assets without going through the\nstreaming endpoint. This is useful for batch operations or when you need direct\naccess URLs for frontend applications.\n\nArgs:\n    user_uuid: The UUID of the authenticated user making the request\n    asset_ids: A sequence of asset IDs to fetch URLs for. Each ID must match\n              the asset ID regex pattern, have minimum length of 1, and\n              whitespace will be stripped\n    fetch_assets_use_case: Injected use case for fetching asset URLs\n\nReturns:\n    FetchAssetUrlAPIOutput: A response containing a mapping of asset IDs to\n                           their corresponding URLs. The structure is:\n                           {\"assets\": {\"asset_id_1\": \"url_1\", \"asset_id_2\": \"url_2\"}}\n\nRaises:\n    404: One or more assets not found or user doesn't have access\n    400: Invalid asset ID format or bad request\n    401: User not authenticated", "operationId": "fetch_asset_url_v3_asset_url__get", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "asset_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "minLength": 1, "pattern": "^[A-Za-z0-9._-]+$"}, "title": "Asset Ids"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FetchAssetUrlAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/document/by_query/": {"delete": {"tags": ["document"], "summary": "Delete User Data By Query Endpoint", "description": "schedules deletion of all of the user's V3 documents", "operationId": "delete_user_data_by_query_endpoint_v3_document_by_query__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentQueryAPI"}, {"type": "null"}], "title": "Query"}}}}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/document/all_data/": {"delete": {"tags": ["document"], "summary": "Delete All User Data Endpoint", "description": "schedules deletion of all of the user's V3 documents", "operationId": "delete_all_user_data_endpoint_v3_document_all_data__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/event/feed/": {"post": {"tags": ["event"], "summary": "Event Feed Endpoint", "description": "The endpoint allows iteration through user documents which are sorted by document timestamp in descending order.\nThe feed is paginated,\n and each page contains a continuation token that can be used to request the next page of results.\nThe initial call can define data_type and organization filters,\n which must then stay immutable for subsequent calls with continuation token returned.\nThe limit and range parameters can be mutated to fine scope the next request.", "operationId": "event_feed_endpoint_v3_event_feed__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventFeedAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventFeedAPIResponse"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/event/": {"post": {"tags": ["event"], "summary": "Insert Event Endpoint", "operationId": "insert_event_endpoint_v3_event__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertEventAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["event"], "summary": "Update Event Endpoint", "operationId": "update_event_endpoint_v3_event__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEventAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["event"], "summary": "Delete Event Endpoint", "operationId": "delete_event_endpoint_v3_event__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteEventAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsIdsResponse"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/event/modify_assets/": {"patch": {"tags": ["event"], "summary": "Modify Event Assets Endpoint", "operationId": "modify_event_assets_endpoint_v3_event_modify_assets__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModifyEventAssetsAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/use_case/search/": {"post": {"tags": ["use case"], "summary": "Search Use Cases Endpoint", "operationId": "search_use_cases_endpoint_v3_use_case_search__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/SearchUseCaseRequestInput"}, {"type": "null"}], "title": "Request Input"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/use_case/archive/": {"patch": {"tags": ["use case"], "summary": "Archive Use Cases Endpoint", "operationId": "archive_use_cases_endpoint_v3_use_case_archive__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "use_case_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Use Case Ids"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/use_case/": {"patch": {"tags": ["use case"], "summary": "Update Use Cases Endpoint", "operationId": "update_use_cases_endpoint_v3_use_case__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUseCaseAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["use case"], "summary": "Insert Use Cases Endpoint", "operationId": "insert_use_cases_endpoint_v3_use_case__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertUseCaseAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/date_histogram/": {"post": {"tags": ["aggregate"], "summary": "Generate date histogram aggregations", "description": "Creates time-based histogram aggregations of documents with configurable time intervals and field aggregations.\n\n    This endpoint groups documents into time buckets based on their timestamps and applies aggregation methods\n    (sum, avg, min, max, count) to specified fields within each bucket. Useful for analyzing trends over time,\n    creating time series visualizations, and understanding temporal patterns in data.\n\n    **Key Features:**\n    - Configurable time intervals (minutes, hours, days, weeks, months, years)\n    - Multiple aggregation methods per field (sum, average, min, max, count)\n    - Timezone support for proper time bucket alignment\n    - Moving average calculations with configurable window sizes\n    - Document count per time bucket\n\n    **Returns:** Array of time buckets with timestamp ranges, document counts, and field aggregation results.", "operationId": "date_histogram_endpoint_v3_aggs_date_histogram__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataHistogramAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataHistogramAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/frequency_distribution/": {"post": {"tags": ["aggregate"], "summary": "Generate frequency distribution of field values", "description": "Analyzes the frequency distribution of unique values for a specified field across document types.\n\n    This endpoint counts how many times each unique value appears in the specified field, providing insights\n    into data distribution patterns. Useful for understanding value popularity, identifying outliers,\n    and creating categorical data visualizations.\n\n    **Key Features:**\n    - Counts occurrences of unique values in any document field\n    - Supports multiple document types in a single query\n    - Results grouped by document type for clear categorization\n    - Sorted by frequency (most common values first)\n    - Configurable result size limit (up to 100,000 unique values)\n\n    **Use Cases:**\n    - Analyzing category distributions (e.g., event types, tags, ratings)\n    - Finding most common values in datasets\n    - Data quality assessment and outlier detection\n    - Creating frequency charts and categorical visualizations\n\n    **Returns:** Dictionary mapping document types to value-frequency pairs, sorted by frequency descending.", "operationId": "frequency_distribution_endpoint_v3_aggs_frequency_distribution__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrequencyDistributionAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrequencyDistributionAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/calendar_histogram_aggregation/": {"post": {"tags": ["aggregate"], "summary": "Generate calendar-based histogram aggregations", "description": "Creates histogram aggregations based on calendar patterns (months, weekdays, hours, lunar phases, etc.)\n    with field value aggregations for each calendar bucket.\n\n    This endpoint groups documents by calendar-based patterns and applies aggregation methods to specified\n    fields within each calendar bucket. Perfect for discovering cyclical patterns, seasonal trends, and\n    time-of-day behaviors in your data.\n\n    **Calendar Aggregation Types:**\n    - **month_names**: Groups by calendar months (January, February, etc.)\n    - **weekdays**: Groups by days of the week (Monday, Tuesday, etc.)\n    - **hours_in_day**: Groups by hours (0-23) for daily pattern analysis\n    - **days_of_month**: Groups by day of month (1-31) for monthly patterns\n    - **parts_of_month**: Groups by month sections (1-5) for start/mid/end analysis\n    - **lunar_phases**: Groups by moon phases (New Moon, First Quarter, Full Moon, Last Quarter)\n\n    **Key Features:**\n    - Multiple aggregation methods (sum, average, min, max, count)\n    - Optional null value filling for complete calendar coverage\n    - Document count per calendar bucket\n    - Aggregated field values per bucket\n\n    **Use Cases:**\n    - Seasonal trend analysis (monthly patterns)\n    - Daily routine analysis (hourly patterns)\n    - Weekly behavior patterns (weekday analysis)\n    - Lunar cycle correlations\n    - Monthly distribution analysis\n\n    **Returns:** Array of calendar buckets with aggregation keys, document counts, and field aggregation results.", "operationId": "calendar_histogram_aggregation_endpoint_v3_aggs_calendar_histogram_aggregation__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarHistogramAggregationAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarHistogramAggregationAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/calendar_frequency_distribution/": {"post": {"tags": ["aggregate"], "summary": "Generate calendar-based frequency distributions", "description": "Analyzes document frequency distributions based on calendar patterns to identify cyclical behaviors\n    and temporal patterns in data occurrence.\n\n    This endpoint counts how many documents occur within each calendar-based bucket (months, weekdays,\n    hours, etc.), revealing patterns like seasonal activity, weekly routines, daily habits, or lunar\n    cycle correlations. Unlike histogram aggregations, this focuses purely on document counts rather\n    than field value aggregations.\n\n    **Calendar Aggregation Types:**\n    - **month_names**: Document counts by calendar months (January, February, etc.)\n    - **weekdays**: Document counts by days of the week (Monday, Tuesday, etc.)\n    - **hours_in_day**: Document counts by hours (0-23) for daily activity patterns\n    - **days_of_month**: Document counts by day of month (1-31) for monthly patterns\n    - **parts_of_month**: Document counts by month sections (1-5) for start/mid/end analysis\n    - **lunar_phases**: Document counts by moon phases (New Moon, First Quarter, Full Moon, Last Quarter)\n    - **time_between**: Document counts by time intervals between consecutive events\n\n    **Key Features:**\n    - Pure frequency counting (no field value aggregation)\n    - Optional null value filling for complete calendar coverage\n    - Specialized time-between analysis for event spacing patterns\n    - Sorted results for easy pattern identification\n\n    **Use Cases:**\n    - Activity pattern discovery (when do events typically occur?)\n    - Seasonal behavior analysis\n    - Daily/weekly routine identification\n    - Event clustering and spacing analysis\n    - Lunar cycle correlation studies\n    - Data collection pattern validation\n\n    **Returns:** Array of calendar buckets with aggregation keys and document frequency counts.", "operationId": "calendar_frequency_distribution_endpoint_v3_aggs_calendar_frequency_distribution__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarFrequencyDistributionAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarFrequencyDistributionAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/ai/suggest/event/": {"post": {"tags": ["ai"], "summary": "Suggest Event from Natural Language", "description": "Generate structured event data from natural language input using AI.\n\n    This endpoint processes a natural language query and returns a structured event that can be\n    inserted into the user's timeline. The AI analyzes the query to:\n\n    - **Classify event type**: Determines the appropriate event category (exercise, nutrition, symptom, etc.)\n    - **Extract temporal information**: Identifies timestamps, durations, and end times\n    - **Apply user context**: Uses the user's timezone and existing templates for better suggestions\n    - **Structure data**: Returns properly formatted event data ready for insertion\n\n    **Examples of supported queries:**\n    - \"I had a big mac for lunch yesterday\"\n    - \"Play football at 10am for 1 hour\"\n    - \"Feeling tired and headache this morning\"\n    - \"30 minute run in the park\"\n\n    The response includes all necessary fields to create an event, with timestamps adjusted\n    to the user's timezone and content structured according to the detected event type.", "operationId": "suggest_event_endpoint_v3_ai_suggest_event__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestEventRequestInput", "description": "Natural language query describing the event to be suggested"}}}}, "responses": {"200": {"description": "Structured event data ready for insertion, with type-specific fields populated based on the natural language input", "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Output"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Output"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Output"}, {"$ref": "#/components/schemas/InsertAudioInput-Output"}, {"$ref": "#/components/schemas/InsertContentInput-Output"}, {"$ref": "#/components/schemas/InsertImageInput-Output"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Output"}, {"$ref": "#/components/schemas/InsertTextInput-Output"}, {"$ref": "#/components/schemas/InsertVideoInput-Output"}, {"$ref": "#/components/schemas/InsertCardioInput-Output"}, {"$ref": "#/components/schemas/InsertExerciseInput-Output"}, {"$ref": "#/components/schemas/InsertStrengthInput-Output"}, {"$ref": "#/components/schemas/InsertEmotionInput-Output"}, {"$ref": "#/components/schemas/InsertStressInput-Output"}, {"$ref": "#/components/schemas/InsertDrinkInput-Output"}, {"$ref": "#/components/schemas/InsertFoodInput-Output"}, {"$ref": "#/components/schemas/InsertSupplementInput-Output"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Output"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Output"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Output"}, {"$ref": "#/components/schemas/InsertNoteInput-Output"}, {"$ref": "#/components/schemas/InsertSymptomInput-Output"}, {"$ref": "#/components/schemas/InsertMedicationInput-Output"}], "discriminator": {"propertyName": "type", "mapping": {"blood_glucose": "#/components/schemas/InsertBloodGlucoseInput-Output", "blood_pressure": "#/components/schemas/InsertBloodPressureInput-Output", "body_metric": "#/components/schemas/InsertBodyMetricInput-Output", "audio": "#/components/schemas/InsertAudioInput-Output", "content": "#/components/schemas/InsertContentInput-Output", "image": "#/components/schemas/InsertImageInput-Output", "interactive": "#/components/schemas/InsertInteractiveInput-Output", "text": "#/components/schemas/InsertTextInput-Output", "video": "#/components/schemas/InsertVideoInput-Output", "cardio": "#/components/schemas/InsertCardioInput-Output", "exercise": "#/components/schemas/InsertExerciseInput-Output", "strength": "#/components/schemas/InsertStrengthInput-Output", "emotion": "#/components/schemas/InsertEmotionInput-Output", "stress": "#/components/schemas/InsertStressInput-Output", "drink": "#/components/schemas/InsertDrinkInput-Output", "food": "#/components/schemas/InsertFoodInput-Output", "supplement": "#/components/schemas/InsertSupplementInput-Output", "core_event": "#/components/schemas/InsertCoreEventInput-Output", "sleep": "#/components/schemas/InsertSleepV3Input-Output", "event_group": "#/components/schemas/InsertEventGroupInput-Output", "note": "#/components/schemas/InsertNoteInput-Output", "symptom": "#/components/schemas/InsertSymptomInput-Output", "medication": "#/components/schemas/InsertMedicationInput-Output"}}, "title": "Response Suggest Event Endpoint V3 Ai Suggest Event  Post"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3record/": {"post": {"tags": ["record"], "summary": "Insert Record Endpoint", "operationId": "insert_record_endpoint_v3record__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertRecordAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_SleepRecordAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["record"], "summary": "Delete Record Endpoint", "operationId": "delete_record_endpoint_v3record__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRecordAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsIdsResponse"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/analyse/trend_detect/": {"post": {"tags": ["analyse"], "summary": "Trend Detection Endpoint", "description": "Detect trends in time series data using linear regression analysis.\n\nThis endpoint analyzes a sequence of numerical values to determine if there's a statistically\nsignificant upward or downward trend. It uses linear regression with configurable thresholds\nfor R-squared (model fit) and relative slope (trend significance).\n\n**Algorithm:**\n- Applies linear regression to the data series\n- Checks if R-squared meets the minimum threshold for model fit\n- Calculates relative slope (coefficient / mean) to determine trend significance\n- Returns trend classification and statistical metrics\n\n**Use Cases:**\n- Analyzing health metrics over time (weight, blood pressure, etc.)\n- Detecting patterns in symptom severity\n- Identifying trends in environmental data\n\n**Request Body:**\n- `data_series`: Array of at least 2 numerical values representing the time series\n- `r2_threshold`: Minimum R-squared value for significant trend (default: 0.3)\n- `relative_slope_threshold`: Minimum absolute relative slope for trend detection (default: 0.01)\n\n**Response:**\n- `trend_result`: Classification as \"UPWARD_TREND\", \"DOWNWARD_TREND\", or \"NO_TREND\"\n- `coefficient`: Linear regression coefficient (slope)\n- `intercept`: Linear regression intercept\n- `relative_slope`: Coefficient divided by mean value (measure of trend strength)\n\n**Example:**\n```json\n{\n    \"data_series\": [1.0, 1.2, 1.5, 1.8, 2.1],\n    \"r2_threshold\": 0.3,\n    \"relative_slope_threshold\": 0.01\n}\n```\n\n**Returns:**\n```json\n{\n    \"trend_result\": \"UPWARD_TREND\",\n    \"coefficient\": 0.25,\n    \"intercept\": 0.95,\n    \"relative_slope\": 0.167\n}\n```", "operationId": "trend_detection_endpoint_v3_analyse_trend_detect__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrendDetectionAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrendDetectionAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/analyse/correlate/event/": {"post": {"tags": ["analyse"], "summary": "Event Correlation Endpoint", "description": "Analyze statistical correlation between two event variables with temporal constraints.\n\nThis endpoint performs correlation analysis between dependent and independent variables from\nuser events or environmental data. It supports different correlation methods based on data types:\n- Pearson correlation for continuous variables\n- ANOVA F-statistic for continuous vs discrete/binary variables\n- Chi-squared for discrete/binary vs discrete/binary variables\n\n**Key Features:**\n- Temporal matching with configurable time windows (\"before\", \"after\", \"closest\")\n- Support for event data and environmental data (weather, air quality, pollen)\n- Field-specific analysis with aggregation methods (mean, sum, count, etc.)\n- Selection bias handling for occurrence-based correlations\n- Statistical significance testing with p-values and confidence intervals\n\n**Request Body:**\n- `dependent`: Variable to be predicted/explained\n  - `query`: Event or environment query defining the data source\n  - `field_name`: Specific field to analyze (null for occurrence counting)\n  - `aggregation_method`: How to aggregate values (required for environment data)\n- `independent`: Variable used for prediction/explanation (same structure as dependent)\n- `temporal_options`: Time-based matching configuration\n  - `type`: \"before\", \"after\", or \"closest\" temporal relationship\n  - `time_input`: Time range and interval for matching events\n\n**Response:**\n- `data`: Array of [dependent, independent] value pairs used in correlation\n- `correlation`: Statistical results including coefficient, p-value, certainty, relationship strength\n- `dependent`/`independent`: Metadata with document counts\n- `suggested_visualisation`: Recommended chart type (\"scatter_plot\", \"box_plot\", \"heat_map\")\n\n**Example:**\n```json\n{\n    \"dependent\": {\n        \"query\": {\"types\": [\"symptom\"], \"filters\": {\"name\": \"headache\"}},\n        \"field_name\": \"rating\",\n        \"aggregation_method\": \"mean\"\n    },\n    \"independent\": {\n        \"query\": {\"types\": [\"food\"], \"filters\": {\"name\": \"coffee\"}},\n        \"field_name\": null,\n        \"aggregation_method\": null\n    },\n    \"temporal_options\": {\n        \"type\": \"before\",\n        \"time_input\": {\"interval\": \"4h\", \"time_gte\": \"2024-01-01\", \"time_lte\": \"2024-01-31\"}\n    }\n}\n```\n\n**Errors:**\n- `400 Bad Request`: Invalid query parameters or temporal options\n- `204 No Content`: No matching data found for correlation analysis", "operationId": "event_correlation_endpoint_v3_analyse_correlate_event__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventCorrelationAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventCorrelationAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/analyse/correlate/event/suggest/parameters": {"post": {"tags": ["analyse"], "summary": "Suggest Correlation Parameters Endpoint", "description": "Get AI-powered suggestions for optimal correlation analysis parameters.\n\nThis endpoint uses artificial intelligence to analyze the provided event queries and suggest\nthe most appropriate parameters for running a correlation analysis. It considers the types\nof events, their typical relationships, and domain knowledge to recommend optimal settings.\n\n**AI Analysis:**\n- Examines event types and their schemas to understand available fields\n- Considers typical temporal relationships between different event categories\n- Suggests appropriate aggregation methods based on data characteristics\n- Provides reasoning for all recommendations\n\n**Use Cases:**\n- Getting started with correlation analysis when unsure of optimal parameters\n- Validating manual parameter choices against AI recommendations\n- Learning about typical relationships between different event types\n- Automating correlation setup for common analysis patterns\n\n**Request Body:**\n- `dependent_query`: Query defining the dependent variable events\n  - For events: `{\"types\": [\"symptom\"], \"filters\": {...}}`\n  - For environment: `{\"domain_type\": \"Weather\"}`\n- `independent_query`: Query defining the independent variable events (same format)\n\n**Response:**\n- `dependent`: Suggested configuration for dependent variable\n  - `field_name`: Recommended field to analyze (or null for occurrence)\n  - `aggregation_method`: Suggested aggregation method\n  - `query`: Original query (passed through)\n- `independent`: Suggested configuration for independent variable (same structure)\n- `temporal_options`: Recommended temporal matching settings\n  - `type`: Suggested relationship type (\"before\", \"after\", \"closest\")\n  - `time_input`: Recommended time range and interval\n- `reasoning`: Detailed explanation of all suggestions\n\n**Example:**\n```json\n{\n    \"dependent_query\": {\n        \"types\": [\"symptom\"],\n        \"filters\": {\"name\": \"headache\"}\n    },\n    \"independent_query\": {\n        \"types\": [\"food\"],\n        \"filters\": {\"category\": \"caffeine\"}\n    }\n}\n```\n\n**Returns:**\n```json\n{\n    \"dependent\": {\n        \"field_name\": \"rating\",\n        \"aggregation_method\": \"mean\",\n        \"query\": {...}\n    },\n    \"independent\": {\n        \"field_name\": null,\n        \"aggregation_method\": null,\n        \"query\": {...}\n    },\n    \"temporal_options\": {\n        \"type\": \"before\",\n        \"time_input\": {\"interval\": \"4h\", \"time_gte\": \"...\", \"time_lte\": \"...\"}\n    },\n    \"reasoning\": \"Suggesting 'before' with '4h' interval as caffeine intake often precedes headache onset within hours...\"\n}\n```\n\n**Errors:**\n- `204 No Content`: AI could not generate suggestions for the provided queries\n- `401 Unauthorized`: Authentication required", "operationId": "suggest_correlation_parameters_endpoint_v3_analyse_correlate_event_suggest_parameters_post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestCorrelationParametersAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestCorrelationParametersAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/template/search/": {"post": {"tags": ["template"], "summary": "Search Templates Endpoint", "operationId": "search_templates_endpoint_v3_template_search__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchTemplatesRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/template/": {"post": {"tags": ["template"], "summary": "Insert Template Endpoint", "operationId": "insert_template_endpoint_v3_template__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertTemplateAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["template"], "summary": "Update Template Endpoint", "operationId": "update_template_endpoint_v3_template__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTemplateAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/template/archive/": {"patch": {"tags": ["template"], "summary": "Archive Templates Endpoint", "operationId": "archive_templates_endpoint_v3_template_archive__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "template_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Template Ids"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/x/lookup/": {"post": {"tags": ["lookup"], "summary": "Post Content Lookup", "operationId": "post_content_lookup_v3_x_lookup__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostContentLookupRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContentLookupOutputBoundary"}}}}, "404": {"description": "Not found"}, "204": {"description": "Couldn't get content from URL"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/search/": {"post": {"tags": ["plan"], "summary": "Search Plans Endpoint", "operationId": "search_plans_endpoint_v3_plan_search__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchPlansRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/archive/": {"patch": {"tags": ["plan"], "summary": "Archive Plans Endpoint", "operationId": "archive_plans_endpoint_v3_plan_archive__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "plan_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Plan Ids"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/": {"patch": {"tags": ["plan"], "summary": "Update Plans Endpoint", "operationId": "update_plans_endpoint_v3_plan__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlansAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["plan"], "summary": "Insert Plans Endpoint", "operationId": "insert_plans_endpoint_v3_plan__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertPlansAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/complete/": {"post": {"tags": ["plan"], "summary": "Complete Plans Endpoint", "operationId": "complete_plans_endpoint_v3_plan_complete__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompletePlansAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_CompletePlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"ActivityType": {"type": "string", "enum": ["walking", "on_foot", "running", "cycling", "boating", "on_bicycle", "skiing", "in_vehicle", "in_road_vehicle", "in_four_wheeler_vehicle", "motorcycling", "in_passenger_vehicle", "in_bus", "in_rail_vehicle", "in_train", "in_subway", "in_tram", "sailing", "in_ferry", "flying", "still", "unknown_activity_type"], "title": "ActivityType"}, "Administration": {"type": "string", "enum": ["capsule", "tablet", "oral", "injection", "patch", "suppository", "topical", "drops", "inhaler", "spray", "other"], "title": "Administration"}, "AggregationMethod": {"type": "string", "enum": ["stats", "sum", "min", "max", "avg"], "title": "AggregationMethod"}, "AnalysisCertainty": {"type": "string", "enum": ["strong evidence", "moderate evidence", "weak evidence", "insufficient evidence"], "title": "AnalysisCertainty"}, "AnalysisRelationshipLabel": {"type": "string", "enum": ["no relationship", "potential relationship", "very weak relationship", "weak relationship", "moderate relationship", "strong relationship"], "title": "AnalysisRelationshipLabel"}, "Application": {"type": "string", "enum": ["apple_health_kit", "llif_mobile", "my_life_log"], "title": "Application"}, "AssetReference": {"properties": {"asset_type": {"$ref": "#/components/schemas/AssetType"}, "asset_id": {"type": "string", "pattern": "^[A-Za-z0-9._-]+$", "title": "Asset Id", "default": "asset_id"}}, "type": "object", "required": ["asset_type"], "title": "AssetReference"}, "AssetReferenceModel": {"properties": {"asset_type": {"$ref": "#/components/schemas/AssetType"}, "asset_id": {"type": "string", "pattern": "^[A-Za-z0-9._-]+$", "title": "Asset Id"}}, "type": "object", "required": ["asset_type", "asset_id"], "title": "AssetReferenceModel"}, "AssetType": {"type": "string", "enum": ["image", "pdf"], "title": "AssetType"}, "AudioAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "AudioAPIOutput"}, "AudioCategory": {"type": "string", "enum": ["audio", "podcast", "audiobook", "music"], "title": "AudioCategory"}, "AudioTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "AudioTemplatePayload"}, "BloodGlucoseAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory", "default": "blood_glucose"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource", "default": "unknown"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "value", "duration"], "title": "BloodGlucoseAPIOutput"}, "BloodGlucoseCategory": {"type": "string", "enum": ["blood_glucose"], "title": "BloodGlucoseCategory"}, "BloodGlucoseSpecimenSource": {"type": "string", "enum": ["interstitial_fluid", "capillary_blood", "plasma", "serum", "tears", "whole_blood", "unknown"], "title": "BloodGlucoseSpecimenSource"}, "BloodGlucoseTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "value", "specimen_source"], "title": "BloodGlucoseTemplatePayload"}, "BloodPressureAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "diastolic", "systolic", "duration"], "title": "BloodPressureAPIOutput"}, "BloodPressureCategory": {"type": "string", "enum": ["blood_pressure"], "title": "BloodPressureCategory"}, "BloodPressureTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "diastolic", "systolic"], "title": "BloodPressureTemplatePayload"}, "BodyMetricAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "value", "duration"], "title": "BodyMetricAPIOutput"}, "BodyMetricCategory": {"type": "string", "enum": ["body_metric", "body_fat", "body_temperature", "pulse_oxygen", "respiratory_rate", "weight"], "title": "BodyMetricCategory"}, "BodyMetricTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "value"], "title": "BodyMetricTemplatePayload"}, "BodyParts": {"type": "string", "enum": ["right.front.head.forehead", "left.front.head.forehead", "right.front.head.face", "left.front.head.face", "right.front.neck", "left.front.neck", "right.front.torso.shoulder", "right.front.torso.chest", "left.front.torso.chest", "left.front.torso.shoulder", "right.front.torso.shoulder.arm.upper", "left.front.torso.shoulder.arm.upper", "right.front.torso.shoulder.arm.elbow", "left.front.torso.shoulder.arm.elbow", "right.front.torso.shoulder.arm.forearm", "right.front.torso.abdomen", "left.front.torso.abdomen", "left.front.torso.shoulder.arm.forearm", "right.front.torso.shoulder.arm.wrist", "right.front.pelvis.hip", "right.front.pelvis.genital", "left.front.pelvis.genital", "left.front.pelvis.hip", "left.front.torso.shoulder.arm.wrist", "right.front.torso.shoulder.arm.hand", "right.front.leg.upper", "left.front.leg.upper", "left.front.torso.shoulder.arm.hand", "right.front.leg.knee", "left.front.leg.knee", "right.front.leg.lower", "left.front.leg.lower", "right.front.leg.ankle", "left.front.leg.ankle", "right.front.leg.foot", "left.front.leg.foot", "left.back.head.top", "right.back.head.top", "left.back.head", "right.back.head", "left.back.neck", "right.back.neck", "left.back.torso.shoulder", "left.back.torso.upper", "right.back.torso.upper", "right.back.torso.shoulder", "left.back.torso.shoulder.arm.upper", "left.back.torso.middle", "right.back.torso.middle", "right.back.torso.shoulder.arm.upper", "left.back.torso.shoulder.arm.elbow", "right.back.torso.shoulder.arm.elbow", "left.back.torso.shoulder.arm.forearm", "left.back.torso.lower", "right.back.torso.lower", "right.back.torso.shoulder.arm.forearm", "left.back.torso.shoulder.arm.wrist", "left.back.pelvis.hip", "left.back.pelvis.glute", "right.back.pelvis.glute", "right.back.pelvis.hip", "right.back.torso.shoulder.arm.wrist", "left.back.torso.shoulder.arm.hand", "left.back.leg.upper", "right.back.leg.upper", "right.back.torso.shoulder.arm.hand", "left.back.leg.knee", "right.back.leg.knee", "left.back.leg.lower", "right.back.leg.lower", "left.back.leg.ankle", "right.back.leg.ankle", "left.back.leg.foot", "right.back.leg.foot"], "title": "BodyParts", "description": "Body parts as described in the CHOIR body map"}, "BooleanQueryType": {"type": "string", "enum": ["and", "or", "not"], "title": "BooleanQueryType"}, "BucketAggregationMethod": {"type": "string", "enum": ["derivative", "moving_avg", "cumulative_sum"], "title": "BucketAggregationMethod"}, "CalendarAggregationType": {"type": "string", "enum": ["month_names", "hours_in_day", "parts_of_month", "days_of_month", "weekdays", "lunar_phases", "time_between"], "title": "CalendarAggregationType"}, "CalendarFrequencyDistributionAPIOutput": {"properties": {"results": {"items": {"$ref": "#/components/schemas/FrequencyDistributionAggregate"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["results"], "title": "CalendarFrequencyDistributionAPIOutput"}, "CalendarFrequencyDistributionAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "calendar_aggregation_type": {"$ref": "#/components/schemas/CalendarAggregationType"}, "fill_null_values": {"type": "boolean", "title": "Fill Null Values", "default": true}}, "type": "object", "required": ["calendar_aggregation_type"], "title": "CalendarFrequencyDistributionAPIRequestInput"}, "CalendarHistogramAggregate": {"properties": {"aggregation_key": {"type": "string", "title": "Aggregation Key"}, "agg_method": {"type": "string", "title": "Agg Method"}, "doc_count": {"type": "integer", "title": "Doc Count"}, "value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Value"}}, "type": "object", "required": ["aggregation_key", "agg_method", "doc_count", "value"], "title": "CalendarHistogramAggregate"}, "CalendarHistogramAggregationAPIOutput": {"properties": {"results": {"items": {"$ref": "#/components/schemas/CalendarHistogramAggregate"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["results"], "title": "CalendarHistogramAggregationAPIOutput"}, "CalendarHistogramAggregationAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "calendar_aggregation_type": {"$ref": "#/components/schemas/CalendarAggregationType"}, "fill_null_values": {"type": "boolean", "title": "Fill Null Values", "default": true}, "field_name": {"type": "string", "title": "Field Name"}, "aggregation_method": {"$ref": "#/components/schemas/SimpleAggregationMethod"}}, "type": "object", "required": ["calendar_aggregation_type", "field_name", "aggregation_method"], "title": "CalendarHistogramAggregationAPIRequestInput"}, "CardioAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "distance", "elevation", "rating", "duration"], "title": "CardioAPIOutput"}, "CardioCategory": {"type": "string", "enum": ["run", "walk", "hike", "dance", "jump_rope", "row", "elliptical", "stair_climb", "swim", "cycling", "cardio"], "title": "CardioCategory"}, "CardioTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "distance", "elevation", "rating"], "title": "CardioTemplatePayload"}, "CommonDocumentsIdsResponse": {"properties": {"document_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Document Ids"}}, "type": "object", "required": ["document_ids"], "title": "CommonDocumentsIdsResponse"}, "CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/BloodGlucoseAPIOutput"}, {"$ref": "#/components/schemas/BloodPressureAPIOutput"}, {"$ref": "#/components/schemas/BodyMetricAPIOutput"}, {"$ref": "#/components/schemas/AudioAPIOutput"}, {"$ref": "#/components/schemas/ContentAPIOutput"}, {"$ref": "#/components/schemas/ImageAPIOutput"}, {"$ref": "#/components/schemas/InteractiveAPIOutput"}, {"$ref": "#/components/schemas/TextAPIOutput"}, {"$ref": "#/components/schemas/VideoAPIOutput"}, {"$ref": "#/components/schemas/ExerciseAPIOutput"}, {"$ref": "#/components/schemas/CardioAPIOutput"}, {"$ref": "#/components/schemas/StrengthAPIOutput"}, {"$ref": "#/components/schemas/EmotionAPIOutput"}, {"$ref": "#/components/schemas/StressAPIOutput"}, {"$ref": "#/components/schemas/DrinkAPIOutput"}, {"$ref": "#/components/schemas/FoodAPIOutput"}, {"$ref": "#/components/schemas/SupplementAPIOutput"}, {"$ref": "#/components/schemas/CoreEventAPIOutput"}, {"$ref": "#/components/schemas/SleepV3APIOutput"}, {"$ref": "#/components/schemas/EventGroupAPIOutput"}, {"$ref": "#/components/schemas/NoteAPIOutput"}, {"$ref": "#/components/schemas/SymptomAPIOutput"}, {"$ref": "#/components/schemas/MedicationAPIOutput"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioAPIOutput", "blood_glucose": "#/components/schemas/BloodGlucoseAPIOutput", "blood_pressure": "#/components/schemas/BloodPressureAPIOutput", "body_metric": "#/components/schemas/BodyMetricAPIOutput", "cardio": "#/components/schemas/CardioAPIOutput", "content": "#/components/schemas/ContentAPIOutput", "core_event": "#/components/schemas/CoreEventAPIOutput", "drink": "#/components/schemas/DrinkAPIOutput", "emotion": "#/components/schemas/EmotionAPIOutput", "event_group": "#/components/schemas/EventGroupAPIOutput", "exercise": "#/components/schemas/ExerciseAPIOutput", "food": "#/components/schemas/FoodAPIOutput", "image": "#/components/schemas/ImageAPIOutput", "interactive": "#/components/schemas/InteractiveAPIOutput", "medication": "#/components/schemas/MedicationAPIOutput", "note": "#/components/schemas/NoteAPIOutput", "sleep": "#/components/schemas/SleepV3APIOutput", "strength": "#/components/schemas/StrengthAPIOutput", "stress": "#/components/schemas/StressAPIOutput", "supplement": "#/components/schemas/SupplementAPIOutput", "symptom": "#/components/schemas/SymptomAPIOutput", "text": "#/components/schemas/TextAPIOutput", "video": "#/components/schemas/VideoAPIOutput"}}}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[Annotated[Union[BloodGlucoseAPIOutput, BloodPressureAPIOutput, BodyMetricAPIOutput, AudioAPIOutput, ContentAPIOutput, ImageAPIOutput, InteractiveAPIOutput, TextAPIOutput, VideoAPIOutput, ExerciseAPIOutput, CardioAPIOutput, StrengthAPIOutput, EmotionAPIOutput, StressAPIOutput, DrinkAPIOutput, FoodAPIOutput, SupplementAPIOutput, CoreEventAPIOutput, SleepV3APIOutput, EventGroupAPIOutput, NoteAPIOutput, SymptomAPIOutput, MedicationAPIOutput], FieldInfo(annotation=NoneType, required=True, discriminator='type')]]"}, "CommonDocumentsResponse_CompletePlanAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/CompletePlanAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[CompletePlanAPIOutput]"}, "CommonDocumentsResponse_PlanAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/PlanAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[PlanAPIOutput]"}, "CommonDocumentsResponse_SleepRecordAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/SleepRecordAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[SleepRecordAPIOutput]"}, "CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__": {"properties": {"documents": {"items": {"anyOf": [{"$ref": "#/components/schemas/EventTemplateAPIOutput"}, {"$ref": "#/components/schemas/GroupTemplateAPIOutput"}]}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[Union[EventTemplateAPIOutput, GroupTemplateAPIOutput]]"}, "CommonDocumentsResponse_UseCaseAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/UseCaseAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[UseCaseAPIOutput]"}, "CompletePlanAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "plan", "title": "Type"}, "metadata": {"$ref": "#/components/schemas/PlanMetadata"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "next_scheduled_at": {"type": "string", "format": "date-time", "title": "Next Scheduled At"}, "first_completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Completed At"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "streak": {"$ref": "#/components/schemas/PlanStreak"}, "recurrence": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Recurrence"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "priority": {"$ref": "#/components/schemas/Priority"}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "events": {"items": {"oneOf": [{"$ref": "#/components/schemas/BloodGlucoseAPIOutput"}, {"$ref": "#/components/schemas/BloodPressureAPIOutput"}, {"$ref": "#/components/schemas/BodyMetricAPIOutput"}, {"$ref": "#/components/schemas/AudioAPIOutput"}, {"$ref": "#/components/schemas/ContentAPIOutput"}, {"$ref": "#/components/schemas/ImageAPIOutput"}, {"$ref": "#/components/schemas/InteractiveAPIOutput"}, {"$ref": "#/components/schemas/TextAPIOutput"}, {"$ref": "#/components/schemas/VideoAPIOutput"}, {"$ref": "#/components/schemas/ExerciseAPIOutput"}, {"$ref": "#/components/schemas/CardioAPIOutput"}, {"$ref": "#/components/schemas/StrengthAPIOutput"}, {"$ref": "#/components/schemas/EmotionAPIOutput"}, {"$ref": "#/components/schemas/StressAPIOutput"}, {"$ref": "#/components/schemas/DrinkAPIOutput"}, {"$ref": "#/components/schemas/FoodAPIOutput"}, {"$ref": "#/components/schemas/SupplementAPIOutput"}, {"$ref": "#/components/schemas/CoreEventAPIOutput"}, {"$ref": "#/components/schemas/SleepV3APIOutput"}, {"$ref": "#/components/schemas/EventGroupAPIOutput"}, {"$ref": "#/components/schemas/NoteAPIOutput"}, {"$ref": "#/components/schemas/SymptomAPIOutput"}, {"$ref": "#/components/schemas/MedicationAPIOutput"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioAPIOutput", "blood_glucose": "#/components/schemas/BloodGlucoseAPIOutput", "blood_pressure": "#/components/schemas/BloodPressureAPIOutput", "body_metric": "#/components/schemas/BodyMetricAPIOutput", "cardio": "#/components/schemas/CardioAPIOutput", "content": "#/components/schemas/ContentAPIOutput", "core_event": "#/components/schemas/CoreEventAPIOutput", "drink": "#/components/schemas/DrinkAPIOutput", "emotion": "#/components/schemas/EmotionAPIOutput", "event_group": "#/components/schemas/EventGroupAPIOutput", "exercise": "#/components/schemas/ExerciseAPIOutput", "food": "#/components/schemas/FoodAPIOutput", "image": "#/components/schemas/ImageAPIOutput", "interactive": "#/components/schemas/InteractiveAPIOutput", "medication": "#/components/schemas/MedicationAPIOutput", "note": "#/components/schemas/NoteAPIOutput", "sleep": "#/components/schemas/SleepV3APIOutput", "strength": "#/components/schemas/StrengthAPIOutput", "stress": "#/components/schemas/StressAPIOutput", "supplement": "#/components/schemas/SupplementAPIOutput", "symptom": "#/components/schemas/SymptomAPIOutput", "text": "#/components/schemas/TextAPIOutput", "video": "#/components/schemas/VideoAPIOutput"}}}, "type": "array", "minItems": 1, "title": "Events"}}, "type": "object", "required": ["id", "system_properties", "type", "metadata", "name", "template_id", "next_scheduled_at", "first_completed_at", "archived_at", "is_urgent", "is_confirmation_required", "is_absolute_schedule", "streak", "recurrence", "note", "priority", "prompt", "current_completed", "max_completed", "tags", "events"], "title": "CompletePlanAPIOutput"}, "CompletePlanInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "completed_at": {"type": "string", "format": "date-time", "title": "Completed At"}, "payload": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Input"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Input", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}, {"type": "null"}], "title": "Payload"}}, "type": "object", "required": ["id"], "title": "CompletePlanInput"}, "CompletePlansAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/CompletePlanInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CompletePlansAPIRequestInput"}, "CompoundBooleanQueryAPI-Input": {"properties": {"type": {"$ref": "#/components/schemas/BooleanQueryType"}, "queries": {"items": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}]}, "type": "array", "minItems": 1, "title": "Queries"}}, "type": "object", "required": ["type", "queries"], "title": "CompoundBooleanQueryAPI"}, "CompoundBooleanQueryAPI-Output": {"properties": {"type": {"$ref": "#/components/schemas/BooleanQueryType"}, "queries": {"items": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Output"}]}, "type": "array", "minItems": 1, "title": "Queries"}}, "type": "object", "required": ["type", "queries"], "title": "CompoundBooleanQueryAPI"}, "ConsumeUnit": {"type": "string", "enum": ["doses", "items"], "title": "ConsumeUnit"}, "ConsumedNutritionType": {"type": "string", "enum": ["unit", "serving", "item"], "title": "ConsumedNutritionType"}, "ContentAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "ContentAPIOutput"}, "ContentCategory": {"type": "string", "enum": ["content", "sport", "social_media", "news"], "title": "ContentCategory"}, "ContentLookupOutputBoundary": {"properties": {"title": {"type": "string", "title": "Title", "description": "title of the webpage as provided by the url"}, "type": {"anyOf": [{"$ref": "#/components/schemas/ContentCategory"}, {"$ref": "#/components/schemas/AudioCategory"}, {"$ref": "#/components/schemas/VideoCategory"}, {"$ref": "#/components/schemas/TextCategory"}, {"$ref": "#/components/schemas/ImageCategory"}, {"$ref": "#/components/schemas/InteractiveCategory"}], "title": "Type", "description": "type of the content, e.g. article, video, website"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "description provided by the website about the url"}}, "type": "object", "required": ["title", "type", "description"], "title": "ContentLookupOutputBoundary"}, "ContentTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "ContentTemplatePayload"}, "CoordinatesModel": {"properties": {"lat": {"type": "number", "maximum": 90.0, "minimum": -90.0, "title": "Lat"}, "lon": {"type": "number", "maximum": 180.0, "minimum": -180.0, "title": "Lon"}}, "type": "object", "required": ["lat", "lon"], "title": "CoordinatesModel"}, "CoreEventAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "CoreEventAPIOutput"}, "CoreEventCategory": {"type": "string", "enum": ["core_event", "idle_downtime", "idle_nap", "idle_wait", "idle_other", "intimacy_partner", "intimacy_self", "intimacy_spiritual", "intimacy_other", "service_beauty", "service_doctor", "service_education", "service_finance", "service_mental_health", "service_physical_health", "service_property", "service_veterinarian", "service_other", "social_assist", "social_group", "social_individual", "social_remote", "social_other", "task_baby_care", "task_clean", "task_create", "task_finance", "task_garden", "task_laundry", "task_lawn_maintenance", "task_meal", "task_meal_preparation", "task_parent", "task_pet_care", "task_plan", "task_home_care", "task_car_care", "task_study", "task_technology", "task_volunteer", "task_other", "travel_bike", "travel_boat", "travel_drive", "travel_fly", "travel_walk", "travel_other", "work_mentor", "work_network", "work_primary_work", "work_professional_development", "work_supplemental_work", "work_other", "rating_positive", "rating_negative", "rating_neutral"], "title": "CoreEventCategory"}, "CoreEventTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "CoreEventTemplatePayload"}, "CorrelationTemporalOptions": {"properties": {"type": {"type": "string", "enum": ["before", "after", "closest"], "title": "Type"}, "time_input": {"$ref": "#/components/schemas/TimeInput"}}, "type": "object", "required": ["type", "time_input"], "title": "CorrelationTemporalOptions"}, "CorrelationVariableAggregate": {"properties": {"count": {"type": "integer", "minimum": 0.0, "title": "Count"}}, "type": "object", "required": ["count"], "title": "CorrelationVariableAggregate"}, "CorrelationVariableInput-Input": {"properties": {"field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name"}, "query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Query"}, "aggregation_method": {"anyOf": [{"$ref": "#/components/schemas/SimpleAggregationMethod"}, {"type": "null"}]}}, "type": "object", "required": ["field_name", "query", "aggregation_method"], "title": "CorrelationVariableInput"}, "CorrelationVariableInput-Output": {"properties": {"field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name"}, "query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Output"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Query"}, "aggregation_method": {"anyOf": [{"$ref": "#/components/schemas/SimpleAggregationMethod"}, {"type": "null"}]}}, "type": "object", "required": ["field_name", "query", "aggregation_method"], "title": "CorrelationVariableInput"}, "DataHistogramAPIOutput": {"properties": {"results": {"items": {"$ref": "#/components/schemas/DateHistogramAggregate"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["results"], "title": "DataHistogramAPIOutput"}, "DataHistogramAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "aggregation": {"$ref": "#/components/schemas/DateHistogramAggregation"}}, "type": "object", "required": ["aggregation"], "title": "DataHistogramAPIRequestInput"}, "DataIntegrity": {"type": "integer", "enum": [5, 4, 3, 2, 1, 0], "title": "DataIntegrity", "description": "Data integrity captures the quality of the data\n\nVERY HIGH: Cloud to cloud, or mobile to cloud\nHIGH: Data through intermediate proxy like AHK or Google Fit\nMEDIUM: File upload\nLOW: Manual entry\nVERY LOW: Interpolated"}, "DataQuality": {"type": "integer", "enum": [5, 4, 3, 2, 1], "title": "DataQuality", "description": "Curates the quality of the sensor data"}, "DateHistogramAggregate": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "doc_count": {"type": "integer", "title": "Doc Count"}, "aggregates": {"items": {"$ref": "#/components/schemas/DateHistogramFieldAggregate"}, "type": "array", "title": "Aggregates"}}, "type": "object", "required": ["timestamp", "end_time", "doc_count", "aggregates"], "title": "DateHistogramAggregate"}, "DateHistogramAggregation": {"properties": {"interval": {"type": "string", "minLength": 2, "pattern": "^(1[mhdwMqy]|\\d+(ms|s|m|h|d))$", "title": "Interval", "description": "Allowed intervals are NX where N is an integer and X equals ms, s, m, h, dOR 1X where X equals m, h, d, w, M, q, y"}, "default_aggregation_method": {"$ref": "#/components/schemas/AggregationMethod"}, "histogram_field_aggregations": {"items": {"$ref": "#/components/schemas/DateHistogramFieldAggregation"}, "type": "array", "title": "Histogram Field Aggregations"}, "timezone": {"type": "string", "format": "zoneinfo", "title": "Timezone", "default": "UTC"}}, "type": "object", "required": ["interval", "default_aggregation_method", "histogram_field_aggregations"], "title": "DateHistogramAggregation"}, "DateHistogramBucketFieldAggregation": {"properties": {"aggregation_method": {"$ref": "#/components/schemas/BucketAggregationMethod"}, "window": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Window", "description": "window size for moving average", "default": 10}}, "type": "object", "required": ["aggregation_method"], "title": "DateHistogramBucketFieldAggregation"}, "DateHistogramFieldAggregate": {"properties": {"field": {"type": "string", "title": "Field"}, "agg_method": {"type": "string", "title": "Agg Method"}, "value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Value"}}, "type": "object", "required": ["field", "agg_method", "value"], "title": "DateHistogramFieldAggregate"}, "DateHistogramFieldAggregation": {"properties": {"field_name": {"type": "string", "minLength": 1, "title": "Field Name"}, "aggregation_method": {"anyOf": [{"$ref": "#/components/schemas/AggregationMethod"}, {"type": "null"}]}, "bucket_aggregation": {"items": {"$ref": "#/components/schemas/DateHistogramBucketFieldAggregation"}, "type": "array", "title": "Bucket Aggregation"}}, "type": "object", "required": ["field_name"], "title": "DateHistogramFieldAggregation"}, "DeleteEventAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/DeleteEventInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "DeleteEventAPIRequestInput"}, "DeleteEventInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/EventV3Type"}}, "type": "object", "required": ["id", "type"], "title": "DeleteEventInput"}, "DeleteRecordAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/DeleteRecordInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "DeleteRecordAPIRequestInput"}, "DeleteRecordInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/RecordType"}}, "type": "object", "required": ["id", "type"], "title": "DeleteRecordInput"}, "DiaryEventAPIOutput": {"properties": {"tags": {"anyOf": [{"items": {"type": "string"}, "type": "array", "maxItems": 64, "minItems": 1}, {"type": "null"}], "title": "Tags"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "type": {"$ref": "#/components/schemas/DiaryEventType"}, "name": {"type": "string", "maxLength": 32, "minLength": 1, "title": "Name"}, "is_standard": {"type": "boolean", "title": "Is Standard", "default": false}, "is_archived": {"type": "boolean", "title": "Is Archived", "default": false}, "custom_data": {"anyOf": [{"items": {"$ref": "#/components/schemas/DiaryEventsCustomDataItem"}, "type": "array"}, {"type": "null"}], "title": "Custom Data"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReferenceModel"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "explanation": {"anyOf": [{"type": "string", "maxLength": 4096, "minLength": 1}, {"type": "null"}], "title": "Explanation"}, "origin_url": {"anyOf": [{"type": "string", "maxLength": 2048, "minLength": 1}, {"type": "null"}], "title": "Origin Url"}, "intensity": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Intensity"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/DiaryEventsPlanExtension"}, {"type": "null"}]}, "consumables_extension": {"anyOf": [{"$ref": "#/components/schemas/DiaryEventsConsumablesExtension"}, {"type": "null"}]}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}}, "type": "object", "required": ["timestamp", "type", "name", "_doc_id", "metadata"], "title": "DiaryEventAPIOutput"}, "DiaryEventType": {"type": "string", "enum": ["drink", "event", "exercise", "food", "medication", "pain", "supplement", "symptom", "measurement", "task"], "title": "DiaryEventType"}, "DiaryEventsConsumablesExtension": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 128, "minLength": 1}, {"type": "null"}], "title": "Name"}, "units": {"anyOf": [{"type": "string", "maxLength": 32, "minLength": 1}, {"type": "null"}], "title": "Units"}, "quantity": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Quantity"}, "quantity_type": {"anyOf": [{"$ref": "#/components/schemas/ConsumedNutritionType"}, {"type": "null"}]}, "quantity_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Quantity Per Serving"}, "amount": {"anyOf": [{"type": "number", "ge": 0, "le": 1000000}, {"type": "null"}], "title": "Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "fat": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Fat"}, "saturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 300}, {"type": "null"}], "title": "Saturated Fat"}, "polyunsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 300}, {"type": "null"}], "title": "Polyunsaturated Fat"}, "monounsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 300}, {"type": "null"}], "title": "Monounsaturated Fat"}, "trans_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 100}, {"type": "null"}], "title": "Trans Fat"}, "cholesterol": {"anyOf": [{"type": "number", "ge": 0, "le": 4}, {"type": "null"}], "title": "Cholesterol"}, "sodium": {"anyOf": [{"type": "number", "ge": 0, "le": 25}, {"type": "null"}], "title": "Sodium"}, "potassium": {"anyOf": [{"type": "number", "ge": 0, "le": 50}, {"type": "null"}], "title": "Potassium"}, "carbohydrates": {"anyOf": [{"type": "number", "ge": 0, "le": 3000}, {"type": "null"}], "title": "Carbohydrates"}, "fiber": {"anyOf": [{"type": "number", "ge": 0, "le": 250}, {"type": "null"}], "title": "Fiber"}, "sugar": {"anyOf": [{"type": "number", "ge": 0, "le": 600}, {"type": "null"}], "title": "Sugar"}, "protein": {"anyOf": [{"type": "number", "ge": 0, "le": 600}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "vitamin_a": {"anyOf": [{"type": "number", "ge": 0, "le": 0.015}, {"type": "null"}], "title": "Vitamin A"}, "vitamin_c": {"anyOf": [{"type": "number", "ge": 0, "le": 10}, {"type": "null"}], "title": "Vitamin C"}, "iron": {"anyOf": [{"type": "number", "ge": 0, "le": 0.2}, {"type": "null"}], "title": "Iron"}, "calcium": {"anyOf": [{"type": "number", "ge": 0, "le": 15}, {"type": "null"}], "title": "Calcium"}}, "type": "object", "title": "DiaryEventsConsumablesExtension"}, "DiaryEventsCustomDataItem": {"properties": {"key": {"type": "string", "minLength": 1, "title": "Key"}, "value": {"type": "number", "maximum": 1000000000000.0, "minimum": -1000000000000.0, "title": "Value"}}, "type": "object", "required": ["key", "value"], "title": "DiaryEventsCustomDataItem"}, "DiaryEventsPlanExtension": {"properties": {"plan_id": {"type": "string", "format": "uuid", "title": "Plan Id"}, "scheduled_at": {"type": "string", "format": "date-time", "title": "Scheduled At"}}, "type": "object", "required": ["plan_id", "scheduled_at"], "title": "DiaryEventsPlanExtension"}, "DocumentQueryAPI": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/DocumentTypedQueryAPI"}, "type": "array", "title": "Queries"}}, "type": "object", "title": "DocumentQueryAPI"}, "DocumentType": {"type": "string", "enum": ["blood_glucose", "blood_pressure", "body_metric", "audio", "content", "image", "interactive", "text", "video", "cardio", "exercise", "strength", "emotion", "stress", "drink", "food", "supplement", "core_event", "sleep", "note", "symptom", "medication", "event_template", "group_template", "plan", "use_case", "event_group"], "title": "DocumentType"}, "DocumentTypedQueryAPI": {"properties": {"types": {"items": {"$ref": "#/components/schemas/DocumentType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "DocumentTypedQueryAPI"}, "DrinkAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "rating", "consumed_amount", "calories", "flavor", "type", "category", "consumed_type", "duration"], "title": "DrinkAPIOutput"}, "DrinkCategory": {"type": "string", "enum": ["tea", "milk", "wine", "beer", "water", "coffee", "smoothie", "spirits", "plant_based", "soft_drinks", "energy_drinks", "protein_shake", "juices_from_fruit", "juices_from_vegetable", "other"], "title": "DrinkCategory"}, "DrinkTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "items_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Items Per Serving"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "amount_volume": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Volume"}, "unit_volume": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit", "maxLength": 32}, {"type": "null"}]}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["name", "duration", "note", "brand", "rating", "items_per_serving", "consumed_amount", "amount_volume", "unit_volume", "calories", "flavor", "nutrients", "type", "category", "consumed_type"], "title": "DrinkTemplatePayload"}, "EmotionAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": -5.0, "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "EmotionAPIOutput"}, "EmotionCategory": {"type": "string", "enum": ["emotion", "joy", "sad", "fear", "disgust", "anger", "surprise", "anticipation", "trust", "anxiety", "mood"], "title": "EmotionCategory"}, "EmotionTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "EmotionTemplatePayload"}, "EnvironmentTypedQuery": {"properties": {"domain_type": {"type": "string", "enum": ["AirQuality", "Weather", "<PERSON><PERSON>"], "title": "Domain Type"}}, "type": "object", "required": ["domain_type"], "title": "EnvironmentTypedQuery"}, "EventCorrelationAPIOutput": {"properties": {"data": {"items": {"prefixItems": [{"type": "number"}, {"type": "number"}], "type": "array", "maxItems": 2, "minItems": 2}, "type": "array", "title": "Data"}, "independent": {"$ref": "#/components/schemas/CorrelationVariableAggregate"}, "dependent": {"$ref": "#/components/schemas/CorrelationVariableAggregate"}, "correlation": {"$ref": "#/components/schemas/EventCorrelationAggregate"}, "suggested_visualisation": {"type": "string", "enum": ["box_plot", "scatter_plot", "heat_map"], "title": "Suggested Visualisation"}}, "type": "object", "required": ["data", "independent", "dependent", "correlation", "suggested_visualisation"], "title": "EventCorrelationAPIOutput"}, "EventCorrelationAPIRequestInput": {"properties": {"dependent": {"$ref": "#/components/schemas/CorrelationVariableInput-Input"}, "independent": {"$ref": "#/components/schemas/CorrelationVariableInput-Input"}, "temporal_options": {"$ref": "#/components/schemas/CorrelationTemporalOptions"}}, "type": "object", "required": ["dependent", "independent", "temporal_options"], "title": "EventCorrelationAPIRequestInput"}, "EventCorrelationAggregate": {"properties": {"correlation_coefficient": {"type": "number", "maximum": 1.0, "minimum": -1.0, "title": "Correlation Coefficient"}, "p_value": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "P Value"}, "degree_of_freedom": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Degree Of Freedom"}, "covariance": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Covariance"}, "certainty": {"$ref": "#/components/schemas/AnalysisCertainty"}, "relationship": {"$ref": "#/components/schemas/AnalysisRelationshipLabel"}}, "type": "object", "required": ["correlation_coefficient", "p_value", "certainty", "relationship"], "title": "EventCorrelationAggregate"}, "EventFeedAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "timestamp", "order": "descending"}}}, "type": "object", "title": "EventFeedAPIRequestInput"}, "EventFeedAPIResponse": {"properties": {"continuation_token": {"type": "string", "minLength": 1, "title": "Continuation Token"}, "items": {"items": {"anyOf": [{"$ref": "#/components/schemas/HeartRateAPIOutput"}, {"$ref": "#/components/schemas/RestingHeartRateAPIOutput"}, {"$ref": "#/components/schemas/SleepAPIOutput"}, {"$ref": "#/components/schemas/StepsAPIOutput"}, {"$ref": "#/components/schemas/DiaryEventAPIOutput"}, {"$ref": "#/components/schemas/LocationAPIOutput"}, {"$ref": "#/components/schemas/BloodGlucoseAPIOutput"}, {"$ref": "#/components/schemas/BloodPressureAPIOutput"}, {"$ref": "#/components/schemas/BodyMetricAPIOutput"}, {"$ref": "#/components/schemas/AudioAPIOutput"}, {"$ref": "#/components/schemas/ContentAPIOutput"}, {"$ref": "#/components/schemas/ImageAPIOutput"}, {"$ref": "#/components/schemas/InteractiveAPIOutput"}, {"$ref": "#/components/schemas/TextAPIOutput"}, {"$ref": "#/components/schemas/VideoAPIOutput"}, {"$ref": "#/components/schemas/ExerciseAPIOutput"}, {"$ref": "#/components/schemas/CardioAPIOutput"}, {"$ref": "#/components/schemas/StrengthAPIOutput"}, {"$ref": "#/components/schemas/EmotionAPIOutput"}, {"$ref": "#/components/schemas/StressAPIOutput"}, {"$ref": "#/components/schemas/DrinkAPIOutput"}, {"$ref": "#/components/schemas/FoodAPIOutput"}, {"$ref": "#/components/schemas/SupplementAPIOutput"}, {"$ref": "#/components/schemas/CoreEventAPIOutput"}, {"$ref": "#/components/schemas/SleepV3APIOutput"}, {"$ref": "#/components/schemas/EventGroupAPIOutput"}, {"$ref": "#/components/schemas/NoteAPIOutput"}, {"$ref": "#/components/schemas/SymptomAPIOutput"}, {"$ref": "#/components/schemas/MedicationAPIOutput"}]}, "type": "array", "minItems": 1, "title": "Items"}}, "type": "object", "required": ["continuation_token", "items"], "title": "EventFeedAPIResponse"}, "EventGroupAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "duration"], "title": "EventGroupAPIOutput"}, "EventInputAsset": {"properties": {"payload": {"type": "string", "maxLength": 1000000, "minLength": 1, "format": "binary", "title": "Payload"}, "asset_type": {"$ref": "#/components/schemas/AssetType"}, "name": {"type": "string", "minLength": 1, "title": "Name"}}, "type": "object", "required": ["payload", "asset_type", "name"], "title": "EventInputAsset"}, "EventMetadataAPIOutput": {"properties": {"origin": {"$ref": "#/components/schemas/Origin"}, "source_service": {"$ref": "#/components/schemas/SourceService"}}, "type": "object", "required": ["origin", "source_service"], "title": "EventMetadataAPIOutput"}, "EventMetadataInput": {"properties": {"origin": {"$ref": "#/components/schemas/InsertableOrigin"}, "origin_device": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Origin Device"}, "source_os": {"$ref": "#/components/schemas/SourceOS"}, "source_service": {"$ref": "#/components/schemas/SourceService"}}, "type": "object", "required": ["origin", "origin_device", "source_os", "source_service"], "title": "EventMetadataInput"}, "EventPlanExtension": {"properties": {"plan_id": {"type": "string", "format": "uuid", "title": "Plan Id"}}, "type": "object", "required": ["plan_id"], "title": "EventPlanExtension"}, "EventTemplateAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "document_name": {"type": "string", "minLength": 1, "title": "Document Name"}, "document": {"oneOf": [{"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Output"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}], "title": "Document", "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Output", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}, "document_type": {"$ref": "#/components/schemas/EventType"}}, "type": "object", "required": ["id", "system_properties", "name", "tags", "archived_at", "document_name", "document", "document_type"], "title": "EventTemplateAPIOutput"}, "EventType": {"type": "string", "enum": ["audio", "content", "image", "interactive", "text", "video", "blood_glucose", "blood_pressure", "body_metric", "cardio", "exercise", "strength", "emotion", "stress", "drink", "food", "supplement", "core_event", "sleep", "event_group", "note", "symptom", "medication", "HeartRate", "RestingHeartRate", "Sleep", "Steps", "DiaryEvents", "Location"], "title": "EventType"}, "EventTypedQueryAPI-Input": {"properties": {"types": {"items": {"$ref": "#/components/schemas/EventType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "EventTypedQueryAPI"}, "EventTypedQueryAPI-Output": {"properties": {"types": {"items": {"$ref": "#/components/schemas/EventType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Output"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "EventTypedQueryAPI"}, "EventV3Type": {"type": "string", "enum": ["audio", "content", "interactive", "image", "text", "video", "blood_glucose", "blood_pressure", "body_metric", "emotion", "stress", "exercise", "cardio", "strength", "drink", "food", "supplement", "core_event", "note", "symptom", "event_group", "medication", "sleep"], "title": "EventV3Type"}, "ExerciseAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "ExerciseAPIOutput"}, "ExerciseCategory": {"type": "string", "enum": ["static_balance", "dynamic_balance", "stability", "daily_activities", "rehabilitation", "injury_prevention", "static_stretching", "dynamic_stretching", "joint_mobility", "muscle_activation", "self_massage", "sword_arts", "basketball", "volleyball", "tennis", "pickleball", "badminton", "squash", "soccer", "ultimate_frisbee", "rugby", "american_football", "flag_football", "archery", "bowling", "table_tennis", "golf", "cricket", "lacrosse", "field_hockey", "handball", "boxing", "kickboxing", "wrestling", "judo", "activity_other", "sport_other", "exercise"], "title": "ExerciseCategory"}, "ExerciseTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "ExerciseTemplatePayload"}, "ExistsQueryAPI": {"properties": {"type": {"type": "string", "const": "exists", "title": "Type"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}}, "type": "object", "required": ["type", "field_name"], "title": "ExistsQueryAPI"}, "FetchAssetUrlAPIOutput": {"properties": {"assets": {"patternProperties": {"^[A-Za-z0-9._-]+$": {"type": "string"}}, "type": "object", "title": "Assets"}}, "type": "object", "required": ["assets"], "title": "FetchAssetUrlAPIOutput"}, "FoodAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "rating", "consumed_amount", "calories", "flavor", "type", "category", "consumed_type", "duration"], "title": "FoodAPIOutput"}, "FoodCategory": {"type": "string", "enum": ["meat", "fish", "eggs", "dairy", "fruits", "poultry", "seafood", "legumes", "ready_meal", "vegetables", "whole_grain", "fats_and_oils", "homemade_meal", "fast_food_meal", "refined_grains", "restaurant_meal", "sweets_and_desserts", "condiments_seasonings", "plant_based_alternatives", "snacks", "other"], "title": "FoodCategory"}, "FoodTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "items_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Items Per Serving"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "amount_volume": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Volume"}, "unit_volume": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit", "maxLength": 32}, {"type": "null"}]}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "amount_mass": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Mass"}, "unit_mass": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit", "maxLength": 32}, {"type": "null"}]}}, "type": "object", "required": ["name", "duration", "note", "brand", "rating", "items_per_serving", "consumed_amount", "amount_volume", "unit_volume", "calories", "flavor", "nutrients", "type", "category", "consumed_type", "amount_mass", "unit_mass"], "title": "FoodTemplatePayload"}, "FrequencyDistributionAPIOutput": {"properties": {"data": {"additionalProperties": {"additionalProperties": {"type": "integer"}, "type": "object"}, "propertyNames": {"$ref": "#/components/schemas/DocumentType"}, "type": "object", "title": "Data"}}, "type": "object", "required": ["data"], "title": "FrequencyDistributionAPIOutput"}, "FrequencyDistributionAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/DocumentTypedQueryAPI"}, "type": "array", "title": "Queries"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}}, "type": "object", "required": ["field_name"], "title": "FrequencyDistributionAPIRequestInput"}, "FrequencyDistributionAggregate": {"properties": {"aggregation_key": {"anyOf": [{"type": "number"}, {"type": "integer"}, {"type": "string"}], "title": "Aggregation Key"}, "document_count": {"type": "integer", "title": "Document Count"}}, "type": "object", "required": ["aggregation_key", "document_count"], "title": "FrequencyDistributionAggregate"}, "GroupTemplateAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "template_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Template Ids"}}, "type": "object", "required": ["id", "system_properties", "name", "tags", "archived_at", "template_ids"], "title": "GroupTemplateAPIOutput"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HeartRateAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "bpm_avg": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Avg"}, "bpm_max": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Max"}, "bpm_min": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Min"}, "bpm_detail": {"anyOf": [{"items": {"$ref": "#/components/schemas/HeartRateBpmDetail"}, "type": "array"}, {"type": "null"}], "title": "Bpm Detail"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "HeartRate", "title": "Type"}}, "type": "object", "required": ["timestamp", "bpm_avg", "bpm_max", "bpm_min", "_doc_id", "metadata", "type"], "title": "HeartRateAPIOutput"}, "HeartRateBpmDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "value": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Value"}, "confidence": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Confidence"}}, "type": "object", "required": ["timestamp", "value"], "title": "HeartRateBpmDetail"}, "ImageAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "ImageAPIOutput"}, "ImageCategory": {"type": "string", "enum": ["image"], "title": "ImageCategory"}, "ImageTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "ImageTemplatePayload"}, "InsertAudioInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertAudioInput"}, "InsertAudioInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertAudioInput"}, "InsertBloodGlucoseInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource", "default": "unknown"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note"], "title": "InsertBloodGlucoseInput"}, "InsertBloodGlucoseInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource", "default": "unknown"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note", "content_hash"], "title": "InsertBloodGlucoseInput"}, "InsertBloodPressureInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory", "default": "blood_pressure"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "systolic", "diastolic", "note"], "title": "InsertBloodPressureInput"}, "InsertBloodPressureInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory", "default": "blood_pressure"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "systolic", "diastolic", "note", "content_hash"], "title": "InsertBloodPressureInput"}, "InsertBodyMetricInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note"], "title": "InsertBodyMetricInput"}, "InsertBodyMetricInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note", "content_hash"], "title": "InsertBodyMetricInput"}, "InsertCardioInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertCardioInput"}, "InsertCardioInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertCardioInput"}, "InsertContentInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertContentInput"}, "InsertContentInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertContentInput"}, "InsertCoreEventInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertCoreEventInput"}, "InsertCoreEventInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertCoreEventInput"}, "InsertDrinkInput-Input": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type"], "title": "InsertDrinkInput"}, "InsertDrinkInput-Output": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type", "content_hash"], "title": "InsertDrinkInput"}, "InsertEmotionInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating"], "title": "InsertEmotionInput"}, "InsertEmotionInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating", "content_hash"], "title": "InsertEmotionInput"}, "InsertEventAPIRequestInput": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Input"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Input"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Input"}, {"$ref": "#/components/schemas/InsertAudioInput-Input"}, {"$ref": "#/components/schemas/InsertContentInput-Input"}, {"$ref": "#/components/schemas/InsertImageInput-Input"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Input"}, {"$ref": "#/components/schemas/InsertTextInput-Input"}, {"$ref": "#/components/schemas/InsertVideoInput-Input"}, {"$ref": "#/components/schemas/InsertCardioInput-Input"}, {"$ref": "#/components/schemas/InsertExerciseInput-Input"}, {"$ref": "#/components/schemas/InsertStrengthInput-Input"}, {"$ref": "#/components/schemas/InsertEmotionInput-Input"}, {"$ref": "#/components/schemas/InsertStressInput-Input"}, {"$ref": "#/components/schemas/InsertDrinkInput-Input"}, {"$ref": "#/components/schemas/InsertFoodInput-Input"}, {"$ref": "#/components/schemas/InsertSupplementInput-Input"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Input"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Input"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Input"}, {"$ref": "#/components/schemas/InsertNoteInput-Input"}, {"$ref": "#/components/schemas/InsertSymptomInput-Input"}, {"$ref": "#/components/schemas/InsertMedicationInput-Input"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/InsertAudioInput-Input", "blood_glucose": "#/components/schemas/InsertBloodGlucoseInput-Input", "blood_pressure": "#/components/schemas/InsertBloodPressureInput-Input", "body_metric": "#/components/schemas/InsertBodyMetricInput-Input", "cardio": "#/components/schemas/InsertCardioInput-Input", "content": "#/components/schemas/InsertContentInput-Input", "core_event": "#/components/schemas/InsertCoreEventInput-Input", "drink": "#/components/schemas/InsertDrinkInput-Input", "emotion": "#/components/schemas/InsertEmotionInput-Input", "event_group": "#/components/schemas/InsertEventGroupInput-Input", "exercise": "#/components/schemas/InsertExerciseInput-Input", "food": "#/components/schemas/InsertFoodInput-Input", "image": "#/components/schemas/InsertImageInput-Input", "interactive": "#/components/schemas/InsertInteractiveInput-Input", "medication": "#/components/schemas/InsertMedicationInput-Input", "note": "#/components/schemas/InsertNoteInput-Input", "sleep": "#/components/schemas/InsertSleepV3Input-Input", "strength": "#/components/schemas/InsertStrengthInput-Input", "stress": "#/components/schemas/InsertStressInput-Input", "supplement": "#/components/schemas/InsertSupplementInput-Input", "symptom": "#/components/schemas/InsertSymptomInput-Input", "text": "#/components/schemas/InsertTextInput-Input", "video": "#/components/schemas/InsertVideoInput-Input"}}}, "type": "array", "minItems": 1, "title": "Documents"}, "metadata": {"$ref": "#/components/schemas/EventMetadataInput"}}, "type": "object", "required": ["documents", "metadata"], "title": "InsertEventAPIRequestInput"}, "InsertEventGroupInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "events": {"items": {"anyOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Input"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Input"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Input"}, {"$ref": "#/components/schemas/InsertAudioInput-Input"}, {"$ref": "#/components/schemas/InsertContentInput-Input"}, {"$ref": "#/components/schemas/InsertImageInput-Input"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Input"}, {"$ref": "#/components/schemas/InsertTextInput-Input"}, {"$ref": "#/components/schemas/InsertVideoInput-Input"}, {"$ref": "#/components/schemas/InsertCardioInput-Input"}, {"$ref": "#/components/schemas/InsertExerciseInput-Input"}, {"$ref": "#/components/schemas/InsertStrengthInput-Input"}, {"$ref": "#/components/schemas/InsertEmotionInput-Input"}, {"$ref": "#/components/schemas/InsertStressInput-Input"}, {"$ref": "#/components/schemas/InsertDrinkInput-Input"}, {"$ref": "#/components/schemas/InsertFoodInput-Input"}, {"$ref": "#/components/schemas/InsertSupplementInput-Input"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Input"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Input"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Input"}, {"$ref": "#/components/schemas/InsertNoteInput-Input"}, {"$ref": "#/components/schemas/InsertSymptomInput-Input"}, {"$ref": "#/components/schemas/InsertMedicationInput-Input"}]}, "type": "array", "minItems": 1, "title": "Events"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "events"], "title": "InsertEventGroupInput"}, "InsertEventGroupInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "events": {"items": {"anyOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Output"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Output"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Output"}, {"$ref": "#/components/schemas/InsertAudioInput-Output"}, {"$ref": "#/components/schemas/InsertContentInput-Output"}, {"$ref": "#/components/schemas/InsertImageInput-Output"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Output"}, {"$ref": "#/components/schemas/InsertTextInput-Output"}, {"$ref": "#/components/schemas/InsertVideoInput-Output"}, {"$ref": "#/components/schemas/InsertCardioInput-Output"}, {"$ref": "#/components/schemas/InsertExerciseInput-Output"}, {"$ref": "#/components/schemas/InsertStrengthInput-Output"}, {"$ref": "#/components/schemas/InsertEmotionInput-Output"}, {"$ref": "#/components/schemas/InsertStressInput-Output"}, {"$ref": "#/components/schemas/InsertDrinkInput-Output"}, {"$ref": "#/components/schemas/InsertFoodInput-Output"}, {"$ref": "#/components/schemas/InsertSupplementInput-Output"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Output"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Output"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Output"}, {"$ref": "#/components/schemas/InsertNoteInput-Output"}, {"$ref": "#/components/schemas/InsertSymptomInput-Output"}, {"$ref": "#/components/schemas/InsertMedicationInput-Output"}]}, "type": "array", "minItems": 1, "title": "Events"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "events", "content_hash"], "title": "InsertEventGroupInput"}, "InsertEventTemplateInputBoundaryItem": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "document": {"oneOf": [{"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Input"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}], "title": "Document", "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Input", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}}, "type": "object", "required": ["name", "document"], "title": "InsertEventTemplateInputBoundaryItem"}, "InsertExerciseInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertExerciseInput"}, "InsertExerciseInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertExerciseInput"}, "InsertFoodInput-Input": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type"], "title": "InsertFoodInput"}, "InsertFoodInput-Output": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type", "content_hash"], "title": "InsertFoodInput"}, "InsertGroupTemplateInputBoundaryItem": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "templates": {"items": {"$ref": "#/components/schemas/InsertEventTemplateInputBoundaryItem"}, "type": "array", "minItems": 1, "title": "Templates"}}, "type": "object", "required": ["name", "templates"], "title": "InsertGroupTemplateInputBoundaryItem"}, "InsertImageInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertImageInput"}, "InsertImageInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertImageInput"}, "InsertInteractiveInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertInteractiveInput"}, "InsertInteractiveInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertInteractiveInput"}, "InsertMedicationInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit"], "title": "InsertMedicationInput"}, "InsertMedicationInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit", "content_hash"], "title": "InsertMedicationInput"}, "InsertNoteInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "note": {"type": "string", "maxLength": 8196, "minLength": 1, "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "note"], "title": "InsertNoteInput"}, "InsertNoteInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "note": {"type": "string", "maxLength": 8196, "minLength": 1, "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "note", "content_hash"], "title": "InsertNoteInput"}, "InsertPlanInput": {"properties": {"type": {"type": "string", "const": "plan", "title": "Type"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "streak": {"$ref": "#/components/schemas/PlanStreak", "default": {"streak": 0, "longest_streak": 0, "total_triggered": 0}}, "recurrence": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recurrence", "description": "Expects format of 'DTSTART:'tz_aware_iso8601}\n{OPTS}"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "priority": {"$ref": "#/components/schemas/Priority", "default": 0}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "next_scheduled_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Scheduled At"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["type", "name", "template_id", "is_urgent", "is_confirmation_required", "recurrence", "is_absolute_schedule", "prompt", "current_completed"], "title": "InsertPlanInput"}, "InsertPlansAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/InsertPlanInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "InsertPlansAPIRequestInput"}, "InsertRecordAPIRequestInput": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/InsertSleepRecordInput"}], "discriminator": {"propertyName": "type", "mapping": {"sleep_record": "#/components/schemas/InsertSleepRecordInput"}}}, "type": "array", "minItems": 1, "title": "Documents"}, "metadata": {"$ref": "#/components/schemas/EventMetadataInput"}}, "type": "object", "required": ["documents", "metadata"], "title": "InsertRecordAPIRequestInput"}, "InsertSeparateGroupTemplateInputBoundaryItem": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "template_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Template Ids"}}, "type": "object", "required": ["name", "template_ids"], "title": "InsertSeparateGroupTemplateInputBoundaryItem"}, "InsertSleepRecordInput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "type": {"type": "string", "const": "sleep_record", "title": "Type"}, "stage": {"$ref": "#/components/schemas/SleepStage"}}, "type": "object", "required": ["timestamp", "end_time", "type", "stage"], "title": "InsertSleepRecordInput"}, "InsertSleepV3Input-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score"], "title": "InsertSleepV3Input"}, "InsertSleepV3Input-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score", "content_hash"], "title": "InsertSleepV3Input"}, "InsertStrengthInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "count"], "title": "InsertStrengthInput"}, "InsertStrengthInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "count", "content_hash"], "title": "InsertStrengthInput"}, "InsertStressInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating"], "title": "InsertStressInput"}, "InsertStressInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating", "content_hash"], "title": "InsertStressInput"}, "InsertSupplementInput-Input": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type"], "title": "InsertSupplementInput"}, "InsertSupplementInput-Output": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type", "content_hash"], "title": "InsertSupplementInput"}, "InsertSymptomInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "body_parts"], "title": "InsertSymptomInput"}, "InsertSymptomInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "body_parts", "content_hash"], "title": "InsertSymptomInput"}, "InsertTemplateAPIRequestInput": {"properties": {"documents": {"items": {"anyOf": [{"$ref": "#/components/schemas/InsertSeparateGroupTemplateInputBoundaryItem"}, {"$ref": "#/components/schemas/InsertEventTemplateInputBoundaryItem"}, {"$ref": "#/components/schemas/InsertGroupTemplateInputBoundaryItem"}]}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "InsertTemplateAPIRequestInput"}, "InsertTextInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertTextInput"}, "InsertTextInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertTextInput"}, "InsertUseCaseAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/InsertUseCaseInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "InsertUseCaseAPIRequestInput"}, "InsertUseCaseInput": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["name"], "title": "InsertUseCaseInput"}, "InsertVideoInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertVideoInput"}, "InsertVideoInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertVideoInput"}, "InsertableOrigin": {"type": "string", "enum": ["amazon", "apple", "fitbit", "google", "netflix", "oura", "ll<PERSON>", "best_life"], "title": "InsertableOrigin"}, "InteractiveAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "InteractiveAPIOutput"}, "InteractiveCategory": {"type": "string", "enum": ["interactive", "game", "virtual_reality", "augmented_reality", "board_game", "card_game", "puzzle", "quiz", "app"], "title": "InteractiveCategory"}, "InteractiveTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "InteractiveTemplatePayload"}, "LocationAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "start_coordinates": {"$ref": "#/components/schemas/CoordinatesModel"}, "end_coordinates": {"anyOf": [{"$ref": "#/components/schemas/CoordinatesModel"}, {"type": "null"}]}, "average_coordinates": {"anyOf": [{"$ref": "#/components/schemas/CoordinatesModel"}, {"type": "null"}]}, "start_altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Start Altitude"}, "place_visit_details": {"anyOf": [{"$ref": "#/components/schemas/PlaceVisitDetails"}, {"type": "null"}]}, "waypoint_details": {"anyOf": [{"items": {"$ref": "#/components/schemas/WaypointDetails"}, "type": "array"}, {"type": "null"}], "title": "Waypoint Details"}, "end_altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "End Altitude"}, "average_altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Altitude"}, "distance": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Distance"}, "activity_type": {"anyOf": [{"$ref": "#/components/schemas/ActivityType"}, {"type": "null"}]}, "activity_type_probability": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Activity Type Probability"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "Location", "title": "Type"}}, "type": "object", "required": ["timestamp", "start_coordinates", "_doc_id", "metadata", "type"], "title": "LocationAPIOutput"}, "LocationSource": {"type": "string", "enum": ["gps", "wifi", "cell", "unknown", "visit_departure", "visit_arrival"], "title": "LocationSource"}, "MatchType": {"type": "string", "enum": ["fuzzy", "exact", "default"], "title": "MatchType"}, "MedicationAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit", "duration"], "title": "MedicationAPIOutput"}, "MedicationCategory": {"type": "string", "enum": ["analgesics", "antibiotics", "antivirals", "antifungals", "antiparasitics", "cardiovascular", "respiratory", "gastrointestinal", "endocrine", "neurological", "mental_health", "immunosuppressants", "oncology", "vaccines", "dermatological", "ophthalmic", "otic", "musculoskeletal", "hematological", "reproductive", "urinary", "emergency"], "title": "MedicationCategory"}, "MedicationDetails": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Brand"}, "generic_name": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Generic Name"}, "rx_cuid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rx C<PERSON>", "description": "External identifier from RxNorm"}, "administration": {"$ref": "#/components/schemas/Administration"}}, "type": "object", "required": ["brand", "generic_name", "rx_cuid", "administration"], "title": "MedicationDetails"}, "MedicationTemplatePayload-Input": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit"], "title": "MedicationTemplatePayload"}, "MedicationTemplatePayload-Output": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit"], "title": "MedicationTemplatePayload"}, "MetadataOutput": {"properties": {"organization": {"$ref": "#/components/schemas/Organization"}, "data_integrity": {"$ref": "#/components/schemas/DataIntegrity"}, "important": {"type": "boolean", "title": "Important", "default": false}, "urgent": {"type": "boolean", "title": "<PERSON><PERSON>", "default": false}, "service": {"anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/Service"}, {"type": "null"}], "title": "Service"}, "data_quality": {"anyOf": [{"$ref": "#/components/schemas/DataQuality"}, {"type": "null"}]}, "sync_software": {"anyOf": [{"$ref": "#/components/schemas/Application"}, {"type": "null"}]}, "sync_device": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sync Device"}, "sensor": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sensor"}, "data_proxy": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Data Proxy"}, "favorited_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Favorited At"}}, "type": "object", "required": ["organization", "data_integrity"], "title": "MetadataOutput"}, "ModifyEventAssetsAPIRequestInput": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ModifyEventAssetsInputBoundaryItem"}, "type": "array", "minItems": 1, "title": "Items"}}, "type": "object", "required": ["items"], "title": "ModifyEventAssetsAPIRequestInput"}, "ModifyEventAssetsInputBoundaryItem": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/EventV3Type"}, "assets_to_add": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets To Add"}, "asset_ids_to_remove": {"anyOf": [{"items": {"type": "string"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset Ids To Remove"}}, "type": "object", "required": ["id", "type", "assets_to_add", "asset_ids_to_remove"], "title": "ModifyEventAssetsInputBoundaryItem"}, "NoteAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "duration"], "title": "NoteAPIOutput"}, "NoteCategory": {"type": "string", "enum": ["note", "journal", "gratitude"], "title": "NoteCategory"}, "NoteTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}}, "type": "object", "required": ["name", "duration", "note", "type", "category"], "title": "NoteTemplatePayload"}, "Nutrients": {"properties": {"fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Fat"}, "saturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Saturated Fat"}, "polyunsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Polyunsaturated Fat"}, "monounsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Monounsaturated Fat"}, "trans_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Trans Fat"}, "cholesterol": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Cholesterol"}, "carbohydrates": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Carbohydrates"}, "fiber": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Fiber"}, "sugar": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Sugar"}, "protein": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "sodium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Sodium"}, "potassium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Potassium"}, "vitamin_a": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin A"}, "vitamin_c": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin C"}, "iron": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Iron"}, "calcium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calcium"}, "biotin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "caffeine": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Caffeine"}, "chloride": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Chloride"}, "chromium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Chromium"}, "copper": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Copper"}, "folate": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Folate"}, "iodine": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Iodine"}, "magnesium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Magnesium"}, "manganese": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Manganese"}, "molybdenum": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Molybdenum"}, "niacin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "pantothenic_acid": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Pantothenic Acid"}, "phosphorus": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Phosphorus"}, "riboflavin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Riboflavin"}, "selenium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Selenium"}, "thiamin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "vitamin_b6": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin B6"}, "vitamin_b12": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin B12"}, "vitamin_d": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "vitamin_e": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "vitamin_k": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "zinc": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Zinc"}}, "type": "object", "required": ["fat", "saturated_fat", "polyunsaturated_fat", "monounsaturated_fat", "trans_fat", "cholesterol", "carbohydrates", "fiber", "sugar", "protein", "sodium", "potassium", "vitamin_a", "vitamin_c", "iron", "calcium", "biotin", "caffeine", "chloride", "chromium", "copper", "folate", "iodine", "magnesium", "manganese", "molybdenum", "niacin", "pantothenic_acid", "phosphorus", "riboflavin", "selenium", "thiamin", "vitamin_b6", "vitamin_b12", "vitamin_d", "vitamin_e", "vitamin_k", "zinc"], "title": "Nutrients"}, "OSPlatform": {"type": "string", "enum": ["android", "ios"], "title": "OSPlatform"}, "Organization": {"type": "string", "enum": ["amazon", "apple", "facebook", "fitbit", "garmin", "google", "ll<PERSON>", "netflix", "oura", "best_life", "walmart", "third_party", "unknown"], "title": "Organization", "description": "Currently supported organisations which we can derive data from"}, "Origin": {"type": "string", "enum": ["amazon", "apple", "fitbit", "google", "netflix", "oura", "walmart", "ll<PERSON>", "best_life", "unknown"], "title": "Origin"}, "PatternQueryAPI": {"properties": {"type": {"type": "string", "const": "pattern", "title": "Type"}, "field_names": {"items": {"type": "string"}, "type": "array", "minItems": 1, "title": "Field Names"}, "pattern": {"type": "string", "title": "Pattern"}, "match_type": {"$ref": "#/components/schemas/MatchType", "default": "default"}, "operator": {"$ref": "#/components/schemas/QueryOperator", "default": "or"}}, "type": "object", "required": ["type", "field_names", "pattern"], "title": "PatternQueryAPI"}, "PlaceVisitDetails": {"properties": {"name": {"type": "string", "maxLength": 128, "minLength": 1, "title": "Name"}, "address": {"anyOf": [{"type": "string", "maxLength": 256, "minLength": 1}, {"type": "null"}], "title": "Address"}, "id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id"}, "confidence": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Confidence"}}, "type": "object", "required": ["name"], "title": "PlaceVisitDetails"}, "PlanAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "plan", "title": "Type"}, "metadata": {"$ref": "#/components/schemas/PlanMetadata"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "next_scheduled_at": {"type": "string", "format": "date-time", "title": "Next Scheduled At"}, "first_completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Completed At"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "streak": {"$ref": "#/components/schemas/PlanStreak"}, "recurrence": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Recurrence"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "priority": {"$ref": "#/components/schemas/Priority"}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "system_properties", "type", "metadata", "name", "template_id", "next_scheduled_at", "first_completed_at", "archived_at", "is_urgent", "is_confirmation_required", "is_absolute_schedule", "streak", "recurrence", "note", "priority", "prompt", "current_completed", "max_completed", "tags"], "title": "PlanAPIOutput"}, "PlanMetadata": {"properties": {"organization": {"$ref": "#/components/schemas/Organization", "default": "ll<PERSON>"}}, "type": "object", "title": "PlanMetadata"}, "PlanStreak": {"properties": {"streak": {"type": "integer", "minimum": 0.0, "title": "Streak"}, "longest_streak": {"type": "integer", "minimum": 0.0, "title": "Longest Streak"}, "total_triggered": {"type": "integer", "minimum": 0.0, "title": "Total Triggered"}}, "type": "object", "required": ["streak", "longest_streak", "total_triggered"], "title": "PlanStreak"}, "PostContentLookupRequestInput": {"properties": {"url": {"type": "string", "maxLength": 2083, "minLength": 1, "format": "uri", "title": "Url", "description": "url to resolve by the lookup"}}, "type": "object", "required": ["url"], "title": "PostContentLookupRequestInput"}, "Priority": {"type": "integer", "enum": [0, 1, 2, 3, 4], "title": "Priority"}, "QueryOperator": {"type": "string", "enum": ["and", "or"], "title": "QueryOperator"}, "RadiusQueryAPI": {"properties": {"type": {"type": "string", "const": "radius", "title": "Type"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}, "radius": {"type": "string", "minLength": 1, "pattern": "^\\d+(m|km|ft|yd|mi)$", "title": "<PERSON><PERSON>"}, "latitude": {"type": "number", "title": "Latitude"}, "longitude": {"type": "number", "title": "Longitude"}}, "type": "object", "required": ["type", "field_name", "radius", "latitude", "longitude"], "title": "RadiusQueryAPI"}, "RangeQueryAPI": {"properties": {"type": {"type": "string", "const": "range", "title": "Type"}, "field_name": {"type": "string", "title": "Field Name"}, "gte": {"anyOf": [{"type": "integer"}, {"type": "number"}, {"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Gte"}, "lte": {"anyOf": [{"type": "integer"}, {"type": "number"}, {"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["type", "field_name"], "title": "RangeQueryAPI"}, "RecordType": {"type": "string", "enum": ["sleep_record"], "title": "RecordType"}, "RestingHeartRateAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "bpm_avg": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Avg"}, "bpm_max": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Max"}, "bpm_min": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Min"}, "rhr_detail": {"anyOf": [{"items": {"$ref": "#/components/schemas/RestingHeartRateDetail"}, "type": "array"}, {"type": "null"}], "title": "Rhr Detail"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "RestingHeartRate", "title": "Type"}}, "type": "object", "required": ["timestamp", "bpm_avg", "bpm_max", "bpm_min", "_doc_id", "metadata", "type"], "title": "RestingHeartRateAPIOutput"}, "RestingHeartRateDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "value": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Value"}, "confidence": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Confidence"}}, "type": "object", "required": ["timestamp", "value"], "title": "RestingHeartRateDetail"}, "SearchPlansRequestInput": {"properties": {"sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "system_properties.created_at", "order": "descending"}}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "title": "SearchPlansRequestInput"}, "SearchTemplatesRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/TemplateTypedQueryAPI"}, "type": "array", "title": "Queries"}, "sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "system_properties.created_at", "order": "descending"}}}, "type": "object", "title": "SearchTemplatesRequestInput"}, "SearchUseCaseRequestInput": {"properties": {"sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "system_properties.created_at", "order": "descending"}}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "title": "SearchUseCaseRequestInput"}, "Service": {"type": "string", "enum": ["apple_health_kit", "alexa", "voice", "diary", "measure", "search", "google_fit"], "title": "Service", "description": "Supported third party services"}, "SimpleAggregationMethod": {"type": "string", "enum": ["sum", "min", "max", "avg"], "title": "SimpleAggregationMethod"}, "SingleDoseInformation": {"properties": {"amount": {"type": "number", "title": "Amount"}, "amount_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}], "title": "Amount Unit"}, "items_quantity": {"type": "number", "title": "Items Quantity"}}, "type": "object", "required": ["amount", "amount_unit", "items_quantity"], "title": "SingleDoseInformation"}, "SleepAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "sleep_events": {"items": {"$ref": "#/components/schemas/SleepEvent"}, "type": "array", "title": "Sleep Events"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 86400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "Sleep", "title": "Type"}}, "type": "object", "required": ["timestamp", "sleep_events", "end_time", "_doc_id", "metadata", "type"], "title": "SleepAPIOutput"}, "SleepDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "stage": {"$ref": "#/components/schemas/SleepStage"}}, "type": "object", "required": ["timestamp", "stage"], "title": "SleepDetail"}, "SleepEvent": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "sleep_detail": {"anyOf": [{"items": {"$ref": "#/components/schemas/SleepDetail"}, "type": "array"}, {"type": "null"}], "title": "Sleep Detail"}, "sleep_summary": {"$ref": "#/components/schemas/SleepSummary"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 86400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}}, "type": "object", "required": ["timestamp", "sleep_summary", "end_time"], "title": "SleepEvent"}, "SleepRecordAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "sleep_record", "title": "Type"}, "stage": {"$ref": "#/components/schemas/SleepStage"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "end_time", "id", "system_properties", "type", "stage", "metadata", "duration"], "title": "SleepRecordAPIOutput"}, "SleepStage": {"type": "string", "enum": ["asleep", "awake", "light", "deep", "rem", "restless", "unknown"], "title": "SleepStage"}, "SleepSummary": {"properties": {"efficiency": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Efficiency"}, "is_main_sleep": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Main Sleep"}, "events_count": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Events Count"}, "fall_asleep_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Fall Asleep Seconds"}, "after_wakeup_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "After Wakeup Seconds"}, "awake_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Awake Seconds"}, "asleep_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "<PERSON>lee<PERSON> Seconds"}, "in_bed_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "In Bed Seconds"}, "deep_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "restless_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Restless Seconds"}}, "type": "object", "title": "SleepSummary"}, "SleepV3APIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": -5.0}, {"type": "null"}], "title": "Rating"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score", "duration"], "title": "SleepV3APIOutput"}, "SleepV3Category": {"type": "string", "enum": ["Sleep"], "title": "SleepV3Category"}, "SleepV3TemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score"], "title": "SleepV3TemplatePayload"}, "SortOrder": {"type": "string", "enum": ["ascending", "descending"], "title": "SortOrder"}, "SortRequestInput": {"properties": {"field_name": {"type": "string", "title": "Field Name", "default": "timestamp"}, "order": {"$ref": "#/components/schemas/SortOrder", "default": "descending"}}, "type": "object", "title": "SortRequestInput"}, "SourceOS": {"type": "string", "enum": ["macos", "ios", "android", "ipados", "windows", "linux", "unknown"], "title": "SourceOS"}, "SourceService": {"type": "string", "enum": ["apple_health_kit", "google_fit", "google_health_connect", "amazon_alexa", "takeout", "fitbit", "best_life_app", "usda_food_db", "ai_photo", "ai_text"], "title": "SourceService"}, "StepsAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "steps": {"type": "integer", "minimum": 0.0, "title": "Steps"}, "step_details": {"items": {"$ref": "#/components/schemas/StepsDetail"}, "type": "array", "title": "Step Details"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "Steps", "title": "Type"}}, "type": "object", "required": ["timestamp", "steps", "step_details", "_doc_id", "metadata", "type"], "title": "StepsAPIOutput"}, "StepsDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "steps": {"type": "integer", "minimum": 0.0, "title": "Steps"}}, "type": "object", "required": ["timestamp", "steps"], "title": "StepsDetail"}, "StrengthAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "count", "weight", "rating", "duration"], "title": "StrengthAPIOutput"}, "StrengthCategory": {"type": "string", "enum": ["bodyweight", "free_weights", "resistance_bands", "suspension_training", "strength"], "title": "StrengthCategory"}, "StrengthTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "count", "weight", "rating"], "title": "StrengthTemplatePayload"}, "StressAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": -5.0, "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "StressAPIOutput"}, "StressCategory": {"type": "string", "enum": ["food_consumption", "liquid_consumption", "mental_activity", "physical_activity", "screen_time", "sleep", "social_activity", "stress", "sun_exposure"], "title": "StressCategory"}, "StressTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "StressTemplatePayload"}, "SuggestCorrelationParametersAPIOutput": {"properties": {"dependent": {"$ref": "#/components/schemas/CorrelationVariableInput-Output"}, "independent": {"$ref": "#/components/schemas/CorrelationVariableInput-Output"}, "temporal_options": {"$ref": "#/components/schemas/CorrelationTemporalOptions"}, "reasoning": {"type": "string", "title": "Reasoning"}}, "type": "object", "required": ["dependent", "independent", "temporal_options", "reasoning"], "title": "SuggestCorrelationParametersAPIOutput"}, "SuggestCorrelationParametersAPIRequestInput": {"properties": {"dependent_query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Dependent Query"}, "independent_query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Independent Query"}}, "type": "object", "required": ["dependent_query", "independent_query"], "title": "SuggestCorrelationParametersAPIRequestInput"}, "SuggestEventRequestInput": {"properties": {"query": {"type": "string", "title": "Query"}}, "type": "object", "required": ["query"], "title": "SuggestEventRequestInput"}, "SupplementAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "rating", "consumed_amount", "calories", "flavor", "type", "category", "consumed_type", "duration"], "title": "SupplementAPIOutput"}, "SupplementCategory": {"type": "string", "enum": ["sleep", "other", "heart_health", "joint_health", "mental_health", "immune_support", "digestive_health", "daily_essentials", "weight_management", "cognitive_function", "bone_and_muscle_health", "athletic_performance"], "title": "SupplementCategory"}, "SupplementTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "items_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Items Per Serving"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "amount_volume": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Volume"}, "unit_volume": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit", "maxLength": 32}, {"type": "null"}]}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "amount_mass": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Mass"}, "unit_mass": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit", "maxLength": 32}, {"type": "null"}]}}, "type": "object", "required": ["name", "duration", "note", "brand", "rating", "items_per_serving", "consumed_amount", "amount_volume", "unit_volume", "calories", "flavor", "nutrients", "type", "category", "consumed_type", "amount_mass", "unit_mass"], "title": "SupplementTemplatePayload"}, "SymptomAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "body_parts", "duration"], "title": "SymptomAPIOutput"}, "SymptomCategory": {"type": "string", "enum": ["pain_and_sensations_pain", "pain_and_sensations_burning", "pain_and_sensations_itching", "pain_and_sensations_scratchiness", "pain_and_sensations_tingling", "pain_and_sensations_numbness", "pain_and_sensations_warmth", "pain_and_sensations_tenderness", "pain_and_sensations_crawling", "pain_and_sensations_headache", "pain_and_sensations_genital_itching", "pain_and_sensations_burning_sensation", "pain_and_sensations_migraine", "pain_and_sensations_other", "motor_and_muscle_fine_motor_difficulty", "motor_and_muscle_jerking", "motor_and_muscle_cramping", "motor_and_muscle_muscle_fatigue", "motor_and_muscle_muscle_spasm", "motor_and_muscle_muscle_tension", "motor_and_muscle_paralysis", "motor_and_muscle_shaking", "motor_and_muscle_tremors", "motor_and_muscle_weakness", "motor_and_muscle_clicking", "motor_and_muscle_locking", "motor_and_muscle_soreness", "motor_and_muscle_stiffness", "motor_and_muscle_other", "skin_and_tissue_lump", "skin_and_tissue_rash", "skin_and_tissue_redness", "skin_and_tissue_swelling", "skin_and_tissue_excessive_sweating", "skin_and_tissue_wet", "skin_and_tissue_acne", "skin_and_tissue_blister", "skin_and_tissue_bruising", "skin_and_tissue_hair_growth", "skin_and_tissue_hair_loss", "skin_and_tissue_hives", "skin_and_tissue_pallor", "skin_and_tissue_rough", "skin_and_tissue_yellowing", "skin_and_tissue_discharge", "skin_and_tissue_dry_skin", "skin_and_tissue_bleeding", "skin_and_tissue_night_sweats", "skin_and_tissue_pale_skin", "skin_and_tissue_skin_peeling", "skin_and_tissue_swollen_glands", "skin_and_tissue_other", "systemic_fatigue", "systemic_fever", "systemic_chills", "systemic_loss_of_appetite", "systemic_other", "respiratory_shortness_of_breath", "respiratory_cough", "respiratory_wheezing", "respiratory_runny_nose", "respiratory_stuffy_nose", "respiratory_loss_of_smell", "respiratory_apnea", "respiratory_sneezing", "respiratory_chest_tightness", "respiratory_other", "circulatory_fast_heart_rhythm", "circulatory_slow_heart_rhythm", "circulatory_irregular_heart_rhythm", "circulatory_pounding_heart", "circulatory_palpitations", "circulatory_feeling_faint", "circulatory_cold_hands_feet", "circulatory_other", "digestive_bloating", "digestive_constipation", "digestive_diarrhea", "digestive_flatulence", "digestive_heartburn", "digestive_vomiting", "digestive_abdominal_pain", "digestive_difficulty_swallowing", "digestive_nausea", "digestive_loss_of_taste", "digestive_excessive_thirst", "digestive_dry_mouth", "digestive_bad_breath", "digestive_other", "neurological_seizures", "neurological_fainting", "neurological_dizziness", "neurological_vertigo", "neurological_clumsiness", "neurological_balance_issues", "neurological_other", "vision_blurry_vision", "vision_double_vision", "vision_floaters", "vision_halos", "vision_light_sensitivity", "vision_spots", "vision_watery_eyes", "vision_dryness", "vision_other", "hearing_muffled", "hearing_loss", "hearing_ringing_in_ears", "hearing_sensitivity_to_sound", "hearing_other", "speech_and_voice_slurred_speech", "speech_and_voice_hoarse_voice", "speech_and_voice_loss_of_voice", "speech_and_voice_other", "sleep_vivid_dreaming", "sleep_difficulty_sleeping", "sleep_sleepwalking", "sleep_snoring", "sleep_insomnia", "sleep_excessive_sleepiness", "sleep_other", "urinary_hesitancy", "urinary_intermittent_flow", "urinary_leaking", "urinary_spraying", "urinary_urgency", "urinary_weak_stream", "urinary_discolored_urine", "urinary_painful_urination", "urinary_frequent_urination", "urinary_blood_in_urine", "urinary_incontinence", "urinary_other", "vaginal_bleeding", "vaginal_clear_discharge", "vaginal_cloudy_discharge", "vaginal_sticky_discharge", "vaginal_dryness", "vaginal_irritation", "vaginal_itching", "vaginal_loss_of_sensation", "vaginal_odor", "vaginal_vaginal_discharge", "vaginal_other", "menstrual_cramp", "menstrual_clots", "menstrual_heavy_flow", "menstrual_spotting", "menstrual_ovulation_pain", "menstrual_menstrual_pain", "menstrual_hot_flashes", "menstrual_other", "pregnancy_contractions", "pregnancy_water_breaking", "pregnancy_braxton_hicks_contractions", "pregnancy_bloody_show", "pregnancy_cervical_effacement", "pregnancy_other", "sexual_function_delayed_orgasm", "sexual_function_inability_to_orgasm", "sexual_function_painful_orgasm", "sexual_function_lack_of_arousal", "sexual_function_painful_intercourse", "sexual_function_premature_orgasm", "sexual_function_other", "mental_behavioral_avoidance", "mental_behavioral_craving", "mental_behavioral_loss_of_interest", "mental_behavioral_fidgeting", "mental_behavioral_pacing", "mental_behavioral_anxiety", "mental_behavioral_depression", "mental_behavioral_irritability", "mental_behavioral_other", "mental_cognitive_brain_fog", "mental_cognitive_difficulty_concentrating", "mental_cognitive_confusion", "mental_cognitive_flashbacks", "mental_cognitive_memory_issues", "mental_cognitive_racing_thoughts", "mental_cognitive_ruminating_thoughts", "mental_cognitive_intrusive_thoughts", "mental_cognitive_hallucinations", "mental_cognitive_memory_loss", "mental_cognitive_other"], "title": "SymptomCategory"}, "SymptomTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating", "body_parts"], "title": "SymptomTemplatePayload"}, "SystemPropertiesSchema": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}}, "type": "object", "title": "SystemPropertiesSchema"}, "SystemPropertiesSchemaAPIOutput": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}}, "type": "object", "required": ["created_at"], "title": "SystemPropertiesSchemaAPIOutput"}, "TemplateType": {"type": "string", "enum": ["group_template", "event_template"], "title": "TemplateType"}, "TemplateTypedQueryAPI": {"properties": {"types": {"items": {"$ref": "#/components/schemas/TemplateType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "TemplateTypedQueryAPI"}, "TextAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "TextAPIOutput"}, "TextCategory": {"type": "string", "enum": ["text", "book", "blog", "article"], "title": "TextCategory"}, "TextTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "TextTemplatePayload"}, "TimeInput": {"properties": {"interval": {"type": "string", "minLength": 2, "pattern": "^(1[mhdwMqy]|\\d+(ms|s|m|h|d))$", "title": "Interval", "description": "Allowed intervals are NX where N is an integer and X equals ms, s, m, h, dOR 1X where X equals m, h, d, w, M, q, y"}, "time_gte": {"type": "string", "format": "date-time", "title": "Time Gte"}, "time_lte": {"type": "string", "format": "date-time", "title": "Time Lte"}}, "type": "object", "required": ["interval", "time_gte", "time_lte"], "title": "TimeInput"}, "TrendDetectionAPIOutput": {"properties": {"trend_result": {"$ref": "#/components/schemas/TrendResult", "default": "NO_TREND"}, "coeficient": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Coeficient"}, "intercept": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Intercept"}, "relative_slope": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Relative Slope"}}, "type": "object", "title": "TrendDetectionAPIOutput"}, "TrendDetectionAPIRequestInput": {"properties": {"data_series": {"items": {"type": "number"}, "type": "array", "minItems": 2, "title": "Data Series"}, "relative_slope_threshold": {"type": "number", "title": "Relative Slop<PERSON>hr<PERSON>old", "description": "Min absolute relative slope for a trend (abs(slope / intercept)).", "default": 0.01}, "r2_threshold": {"type": "number", "title": "R2 Threshold", "description": "Min R-squared for significant trend (model fit).", "default": 0.3}}, "type": "object", "required": ["data_series"], "title": "TrendDetectionAPIRequestInput"}, "TrendResult": {"type": "string", "enum": ["UPWARD_TREND", "DOWNWARD_TREND", "NO_TREND"], "title": "TrendResult"}, "UpdateAudioInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateAudioInput"}, "UpdateBloodGlucoseInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "value", "specimen_source", "note"], "title": "UpdateBloodGlucoseInput"}, "UpdateBloodPressureInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "systolic", "diastolic", "note"], "title": "UpdateBloodPressureInput"}, "UpdateBodyMetricInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "value", "note"], "title": "UpdateBodyMetricInput"}, "UpdateCardioInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "distance", "elevation", "rating", "note"], "title": "UpdateCardioInput"}, "UpdateContentInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateContentInput"}, "UpdateCoreEventInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateCoreEventInput"}, "UpdateDrinkInput": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}, {"type": "null"}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "id", "timestamp", "name", "plan_extension", "tags", "type", "category", "consumed_type"], "title": "UpdateDrinkInput"}, "UpdateEmotionInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateEmotionInput"}, "UpdateEventAPIRequestInput": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/UpdateBloodGlucoseInput"}, {"$ref": "#/components/schemas/UpdateBloodPressureInput"}, {"$ref": "#/components/schemas/UpdateBodyMetricInput"}, {"$ref": "#/components/schemas/UpdateAudioInput"}, {"$ref": "#/components/schemas/UpdateContentInput"}, {"$ref": "#/components/schemas/UpdateImageInput"}, {"$ref": "#/components/schemas/UpdateInteractiveInput"}, {"$ref": "#/components/schemas/UpdateTextInput"}, {"$ref": "#/components/schemas/UpdateVideoInput"}, {"$ref": "#/components/schemas/UpdateCardioInput"}, {"$ref": "#/components/schemas/UpdateExerciseInput"}, {"$ref": "#/components/schemas/UpdateStrengthInput"}, {"$ref": "#/components/schemas/UpdateEmotionInput"}, {"$ref": "#/components/schemas/UpdateStressInput"}, {"$ref": "#/components/schemas/UpdateDrinkInput"}, {"$ref": "#/components/schemas/UpdateFoodInput"}, {"$ref": "#/components/schemas/UpdateSupplementInput"}, {"$ref": "#/components/schemas/UpdateCoreEventInput"}, {"$ref": "#/components/schemas/UpdateSleepV3Input"}, {"$ref": "#/components/schemas/UpdateEventGroupInput"}, {"$ref": "#/components/schemas/UpdateNoteInput"}, {"$ref": "#/components/schemas/UpdateSymptomInput"}, {"$ref": "#/components/schemas/UpdateMedicationInput"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/UpdateAudioInput", "blood_glucose": "#/components/schemas/UpdateBloodGlucoseInput", "blood_pressure": "#/components/schemas/UpdateBloodPressureInput", "body_metric": "#/components/schemas/UpdateBodyMetricInput", "cardio": "#/components/schemas/UpdateCardioInput", "content": "#/components/schemas/UpdateContentInput", "core_event": "#/components/schemas/UpdateCoreEventInput", "drink": "#/components/schemas/UpdateDrinkInput", "emotion": "#/components/schemas/UpdateEmotionInput", "event_group": "#/components/schemas/UpdateEventGroupInput", "exercise": "#/components/schemas/UpdateExerciseInput", "food": "#/components/schemas/UpdateFoodInput", "image": "#/components/schemas/UpdateImageInput", "interactive": "#/components/schemas/UpdateInteractiveInput", "medication": "#/components/schemas/UpdateMedicationInput", "note": "#/components/schemas/UpdateNoteInput", "sleep": "#/components/schemas/UpdateSleepV3Input", "strength": "#/components/schemas/UpdateStrengthInput", "stress": "#/components/schemas/UpdateStressInput", "supplement": "#/components/schemas/UpdateSupplementInput", "symptom": "#/components/schemas/UpdateSymptomInput", "text": "#/components/schemas/UpdateTextInput", "video": "#/components/schemas/UpdateVideoInput"}}}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdateEventAPIRequestInput"}, "UpdateEventGroupInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "note"], "title": "UpdateEventGroupInput"}, "UpdateEventTemplateInputBoundaryItem": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "document": {"oneOf": [{"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Input"}], "title": "Document", "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Input", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}}, "type": "object", "required": ["id", "name", "tags", "document"], "title": "UpdateEventTemplateInputBoundaryItem"}, "UpdateExerciseInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateExerciseInput"}, "UpdateFoodInput": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}, {"type": "null"}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "id", "timestamp", "name", "plan_extension", "tags", "type", "category", "consumed_type"], "title": "UpdateFoodInput"}, "UpdateGroupTemplateInputBoundaryItem": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "template_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Template Ids"}}, "type": "object", "required": ["id", "name", "tags", "template_ids"], "title": "UpdateGroupTemplateInputBoundaryItem"}, "UpdateImageInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateImageInput"}, "UpdateInteractiveInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateInteractiveInput"}, "UpdateMedicationInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit", "note"], "title": "UpdateMedicationInput"}, "UpdateNoteInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "note": {"type": "string", "maxLength": 8196, "minLength": 1, "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "note"], "title": "UpdateNoteInput"}, "UpdatePlanInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"type": "string", "const": "plan", "title": "Type"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "next_scheduled_at": {"type": "string", "format": "date-time", "title": "Next Scheduled At"}, "streak": {"$ref": "#/components/schemas/PlanStreak"}, "recurrence": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recurrence", "description": "Expects format of 'DTSTART:'tz_aware_iso8601}\n{OPTS}"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "priority": {"$ref": "#/components/schemas/Priority", "default": 0}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "type", "name", "template_id", "next_scheduled_at", "streak", "recurrence", "is_absolute_schedule", "is_urgent", "is_confirmation_required", "prompt", "current_completed", "note", "max_completed", "tags"], "title": "UpdatePlanInput"}, "UpdatePlansAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/UpdatePlanInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdatePlansAPIRequestInput"}, "UpdateSleepV3Input": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score"], "title": "UpdateSleepV3Input"}, "UpdateStrengthInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "count", "weight", "rating", "note"], "title": "UpdateStrengthInput"}, "UpdateStressInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateStressInput"}, "UpdateSupplementInput": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}, {"type": "null"}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "id", "timestamp", "name", "plan_extension", "tags", "type", "category", "consumed_type"], "title": "UpdateSupplementInput"}, "UpdateSymptomInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note", "body_parts"], "title": "UpdateSymptomInput"}, "UpdateTemplateAPIRequestInput": {"properties": {"documents": {"items": {"anyOf": [{"$ref": "#/components/schemas/UpdateEventTemplateInputBoundaryItem"}, {"$ref": "#/components/schemas/UpdateGroupTemplateInputBoundaryItem"}]}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdateTemplateAPIRequestInput"}, "UpdateTextInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateTextInput"}, "UpdateUseCaseAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/UpdateUseCaseInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdateUseCaseAPIRequestInput"}, "UpdateUseCaseInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "name"], "title": "UpdateUseCaseInput"}, "UpdateVideoInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateVideoInput"}, "UseCaseAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "use_case", "title": "Type"}, "name": {"type": "string", "title": "Name"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "system_properties", "type", "name", "archived_at", "tags"], "title": "UseCaseAPIOutput"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "ValuesQueryAPI": {"properties": {"type": {"type": "string", "const": "values", "title": "Type"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}, "values": {"items": {"type": "string"}, "type": "array", "minItems": 1, "title": "Values"}}, "type": "object", "required": ["type", "field_name", "values"], "title": "ValuesQueryAPI"}, "VideoAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "VideoAPIOutput"}, "VideoCategory": {"type": "string", "enum": ["video", "movie", "tv_show", "livestream", "podcast", "music_video"], "title": "VideoCategory"}, "VideoTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "VideoTemplatePayload"}, "VolumeUnit": {"type": "string", "enum": ["ml", "l", "tsp", "tbsp", "fl_oz", "cup", "pt", "qt", "gal"], "title": "VolumeUnit"}, "WaypointDetails": {"properties": {"coordinates": {"anyOf": [{"$ref": "#/components/schemas/CoordinatesModel"}, {"type": "string"}], "title": "Coordinates"}, "altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Altitude"}, "velocity": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Velocity"}, "hor_acc": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Hor Acc"}, "ver_acc": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Ver Acc"}, "heading": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Heading"}, "source": {"anyOf": [{"$ref": "#/components/schemas/LocationSource"}, {"type": "null"}]}, "platform_type": {"anyOf": [{"$ref": "#/components/schemas/OSPlatform"}, {"type": "null"}]}, "timestamp": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}}, "type": "object", "required": ["coordinates"], "title": "WaypointDetails"}, "WeightUnit": {"type": "string", "enum": ["mcg", "mg", "g", "kg", "oz", "lb"], "title": "WeightUnit"}}, "securitySchemes": {"CustomHTTPBearer": {"type": "http", "scheme": "bearer"}}}}