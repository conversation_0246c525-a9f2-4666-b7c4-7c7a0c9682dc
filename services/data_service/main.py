from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from fastapi.encoders import ENCODERS_BY_TYPE
from fastapi_health import health
from fastapi_injector import attach_injector
from pydantic_core import PydanticUndefinedType

from services.base.api.deprecate_router import deprecate_router
from services.base.api.exception_handlers import set_default_exception_handlers
from services.base.api.health_endpoints import server_run_time
from services.base.api.middleware.cors_middleware import add_cors_middleware
from services.base.api.middleware.gzip_middleware import add_gzip_middleware
from services.base.api.set_logging import set_uvicorn_logging
from services.base.api.stable_openapi import add_stable_openapi
from services.base.telemetry.fastapi_instrumentor import instrument_app
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.data_service.api.aggregate_endpoints import aggregate_router
from services.data_service.api.ai_endpoints import ai_router
from services.data_service.api.analyse_endpoints import analyse_router
from services.data_service.api.assets_endpoints import assets_router
from services.data_service.api.document_endpoints import document_router
from services.data_service.api.event_endpoints import event_router
from services.data_service.api.lookup_endpoints import lookup_router
from services.data_service.api.plan_endpoints import plan_router
from services.data_service.api.record_endpoints import record_router
from services.data_service.api.template_endpoints import template_router
from services.data_service.api.use_case_endpoints import use_case_router
from services.data_service.dependency_bootstrapper import DependencyBootstrapper
from services.data_service.scheduler import Scheduler
from services.data_service.v02.api.activity_endpoints import activity_router
from services.data_service.v02.api.data_summary_endpoints import summary_router
from services.data_service.v02.api.delete_endpoints import delete_router
from services.data_service.v02.api.diary_endpoints import diary_router
from services.data_service.v02.api.fetch_endpoints import fetch_router
from services.data_service.v02.api.location_endpoints import location_router
from services.data_service.v02.api.measure_endpoints import measure_router
from services.data_service.v02.api.update_endpoints import update_router
from services.data_service.v1.api.data_crud_endpoints import data_crud_router
from services.data_service.v1.api.environment_endpoints import environment_router
from services.data_service.v1.api.extensions.extension_directory_endpoints import extension_directory_router
from services.data_service.v1.api.extensions.extension_providers_endpoints import extension_providers_router
from services.data_service.v1.api.extensions.extension_results_endpoints import extension_router
from services.data_service.v1.api.temp_plan_endpoints import temp_plan_router
from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL, RUN_ENV_PRODUCTION
from settings.app_secrets import secrets

# TODO: Fixes issues with list query params until [https://github.com/tiangolo/fastapi/discussions/10331] is resolved
ENCODERS_BY_TYPE[PydanticUndefinedType] = lambda o: None

# telemetry
set_uvicorn_logging()
TelemetryInstrumentor.initialize(service_name="data_service", settings=settings, secrets=secrets)


@asynccontextmanager
async def lifespan(_: FastAPI):
    # startup
    scheduler.start()

    yield

    # shutdown
    scheduler.shutdown()
    await bootstrapper.cleanup()


bootstrapper = DependencyBootstrapper().build()
scheduler = Scheduler(bootstrapper=bootstrapper)

app = FastAPI(
    root_path=None if settings.RUN_ENV == RUN_ENV_LOCAL else "/data",
    openapi_url="/openapi.json" if not settings.RUN_ENV == RUN_ENV_PRODUCTION else None,
    title="Data service",
    version="0.2 && 1.0",
    lifespan=lifespan,
)

instrument_app(app=app, settings=settings)
attach_injector(app=app, injector=bootstrapper.injector)
add_cors_middleware(app=app)
add_gzip_middleware(app=app)
set_default_exception_handlers(app=app)
add_stable_openapi(app=app)

# routes
app.add_api_route("/health", health([server_run_time]))

app.include_router(assets_router)
app.include_router(document_router)
app.include_router(event_router)
app.include_router(use_case_router)
app.include_router(aggregate_router)
app.include_router(ai_router)
app.include_router(record_router)
app.include_router(analyse_router)
app.include_router(template_router)
app.include_router(lookup_router)
app.include_router(plan_router)

# deprecated
app.include_router(deprecate_router(activity_router))
app.include_router(deprecate_router(data_crud_router))
app.include_router(deprecate_router(delete_router))
app.include_router(deprecate_router(diary_router))
app.include_router(deprecate_router(fetch_router))
app.include_router(deprecate_router(environment_router))
app.include_router(deprecate_router(extension_directory_router))
app.include_router(deprecate_router(extension_providers_router))
app.include_router(deprecate_router(extension_router))
app.include_router(deprecate_router(location_router))
app.include_router(deprecate_router(measure_router))
app.include_router(deprecate_router(summary_router))
app.include_router(deprecate_router(temp_plan_router))
app.include_router(deprecate_router(update_router))
