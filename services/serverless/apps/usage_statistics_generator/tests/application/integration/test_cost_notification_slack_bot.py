import io

import pytest
from slack_sdk.errors import SlackApiError

from services.base.application.retry import retry
from services.serverless.apps.usage_statistics_generator.application.slack_bot.slack_bot import SlackBot
from settings.app_config import settings


@pytest.mark.integration
async def test_post_message(cost_notification_slack_bot: SlackBot):
    @retry(max_times=2, delay=2)
    async def run_test(cost_notification_slack_bot: SlackBot):
        try:
            response = await cost_notification_slack_bot.post_message(
                message="Test message", channel_id=settings.COST_NOTIFICATION_CHANNEL_ID
            )
            assert response["ok"]
            # Delete the message from the channel
            delete_response = await cost_notification_slack_bot.delete_message(
                channel_id=settings.COST_NOTIFICATION_CHANNEL_ID, message_ts=response["ts"]
            )
            assert delete_response
        except SlackApiError as e:
            pytest.fail(f"Failed to post message: {e.response['error']}")

    await run_test(cost_notification_slack_bot=cost_notification_slack_bot)


@pytest.mark.integration
async def test_get_last_messages(cost_notification_slack_bot: SlackBot):
    @retry(max_times=2, delay=2)
    async def run_test(cost_notification_slack_bot: SlackBot):
        try:
            messages = await cost_notification_slack_bot.get_last_messages(
                channel_id=settings.COST_NOTIFICATION_CHANNEL_ID, count=3
            )
            assert isinstance(messages, list)
        except SlackApiError as e:
            pytest.fail(f"Failed to get last messages: {e.response['error']}")

    await run_test(cost_notification_slack_bot=cost_notification_slack_bot)


@pytest.mark.integration
async def test_upload_csv_with_message(cost_notification_slack_bot: SlackBot):
    @retry(max_times=2, delay=2)
    async def get_inserted_message(created_file_id: str):
        message = await cost_notification_slack_bot.get_last_messages(
            channel_id=settings.COST_NOTIFICATION_CHANNEL_ID, count=1
        )
        assert created_file_id == message[0]["files"][0]["id"]
        return message

    @retry(max_times=5, delay=2)
    async def run_test(cost_notification_slack_bot: SlackBot):
        csv_string = "column1,column2\nvalue1,value2\nvalue3,value4"
        try:
            csv_byte_array = io.BytesIO(csv_string.encode("utf-8"))
            response = await cost_notification_slack_bot.upload_csv_with_message(
                channel_id=settings.COST_NOTIFICATION_CHANNEL_ID,
                csv_byte_array=csv_byte_array,
                message="Test CSV upload",
                filename="testing.csv",
            )
            file_id = response.data["file"]["id"]
            message = await get_inserted_message(created_file_id=file_id)
            # Delete the message from the channel
            delete_response = await cost_notification_slack_bot.delete_message(
                settings.COST_NOTIFICATION_CHANNEL_ID, message[0]["ts"]
            )
            assert delete_response
        except SlackApiError as e:
            pytest.fail(f"Failed to upload CSV with message: {e.response['error']}")

    await run_test(cost_notification_slack_bot=cost_notification_slack_bot)
