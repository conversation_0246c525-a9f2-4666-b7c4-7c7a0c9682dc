from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.schemas.member_user.member_user_device import MemberUserDevice
from services.serverless.apps.notify_handler.application.notify_users_use_case import NotifyUsersUseCase
from services.serverless.apps.notify_handler.domain.builders.notification_input_builder import NotificationInputBuilder


@pytest.fixture
def member_user_device_repository():
    return AsyncMock(spec=MemberUserDeviceRepository)


@pytest.fixture
def push_notification_service():
    return MagicMock(spec=PushNotificationService)


async def test_notify_users_use_case_execute_async_should_pass(
    member_user_device_repository,
    push_notification_service,
):
    # Arrange
    member_user_device_repository.get_by_uuid.return_value = [
        MemberUserDevice(user_uuid=uuid4(), device_token=PrimitiveTypesGenerator.generate_random_string())
    ]
    notification_input = NotificationInputBuilder().build()
    # Act
    await NotifyUsersUseCase(
        member_user_device_repository=member_user_device_repository,
        push_notification_service=push_notification_service,
    ).execute_async(notification_inputs=[notification_input])
    # Assert
    if notification_input.is_urgent:
        push_notification_service.publish.assert_called_once()
        member_user_device_repository.get_by_uuid.assert_called_once()
    else:
        push_notification_service.publish.assert_not_called()
        member_user_device_repository.get_by_uuid.assert_not_called()
