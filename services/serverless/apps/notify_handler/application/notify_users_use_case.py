import asyncio
import logging
from typing import List, Sequence

from services.base.application.notifications.push_notification_models import (
    PushNotificationMessage,
)
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.schemas.member_user.member_user_device import MemberUserDevice
from services.serverless.apps.notify_handler.domain.notification_input import NotificationInput


class NotifyUsersUseCase:
    def __init__(
        self,
        member_user_device_repository: MemberUserDeviceRepository,
        push_notification_service: PushNotificationService,
    ):

        self._member_user_device_repository = member_user_device_repository
        self._push_notification_service = push_notification_service

    async def execute_async(self, notification_inputs: List[NotificationInput]) -> None:
        tasks: List[asyncio.Task] = []

        for notification in notification_inputs:
            if notification.is_urgent:
                tasks.append(asyncio.create_task(self.send_push_notification(notification_input=notification)))

        await asyncio.gather(*tasks)

    async def send_push_notification(self, notification_input: NotificationInput) -> None:
        # fetch all device tokens registered for the current user
        devices: Sequence[MemberUserDevice] = await self._member_user_device_repository.get_by_uuid(
            user_uuid=notification_input.destination.id
        )
        if not devices:
            logging.info(f"user: {notification_input.destination} has no registered push notification devices")
            return None

        device_tokens = [d.device_token for d in devices]
        message = PushNotificationMessage(
            title=notification_input.title,
            body=notification_input.summary,
            device_tokens=device_tokens,
        )

        response = self._push_notification_service.publish(messages=[message])
        logging.info(f"push notifications successfully sent: {response.successful}, failed: {response.failed}")
        return None
