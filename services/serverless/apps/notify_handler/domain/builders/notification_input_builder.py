from typing import Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.notification import NotificationType
from services.base.domain.schemas.identity import Identity
from services.base.tests.domain.builders.context_models_builder import ContextModelsBuilder
from services.base.tests.domain.builders.identity_builder import IdentityBuilder
from services.serverless.apps.notify_handler.domain.notification_input import NotificationInput


class NotificationInputBuilder:

    def __init__(self):
        self._destination: Identity | None = None

    def build(self) -> NotificationInput:
        return NotificationInput(
            sender=IdentityBuilder().build(),
            destination=self._destination or IdentityBuilder().build(),
            message=PrimitiveTypesGenerator.generate_random_string(),
            summary=PrimitiveTypesGenerator.generate_random_string(),
            title=PrimitiveTypesGenerator.generate_random_string(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=NotificationType),
            context=ContextModelsBuilder().build(),
            is_important=True,
        )

    def build_n(self, n: int | None = None) -> Sequence[NotificationInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_destination(self, destination: Identity):
        self._destination = destination
        return self
