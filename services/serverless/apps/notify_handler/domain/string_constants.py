# Welcome notifications
# TODO?
# To display the user's name we'd have to do a DB call here to fetch his info

WELCOME_TITLE_TEXT = "Hey! Welcome to the platform. Let's get started with these hints."
ACCOUNT_LINKED_TITLE_TEXT = "Your account was successfully linked"
WELCOME_DESCRIPTION_TEXT = "Welcome!"
WELCOME_MESSAGE_TEXT = (
    "\n\nGo to your [Diary](InputTab) where you can add diary events,"
    " ratings, and notes by tapping the date or using the floating button."
    " Events can be symptoms, pain, medicines, supplements, food, drink, etc."
    " Ratings include measures of mood and stress. Notes are to record ideas,"
    " thoughts, communications, etc. tagged by topic."
    "\n\nGo to your [Data Visuals](BrowseTab) to see your life at a glance including last night sleep"
    ", resting heart rate, current steps, etc."
    "\n\nGo to [Settings](ProfileStack) to make changes to your profile,"
    " units, theme, timezone, connected accounts, repeat the [Onboarding](Profile),"
    " or donate to help keep us going!"
)

TAKEOUT_FINISHED_TITLE_MESSAGE = "Your takeout is ready ✅"  # @TODO: Product needs to define
TAKEOUT_FINISHED_MESSAGE_WITH_NAME = "We finished processing your takeout of Best Life data, your takeout %s is now ready to be downloaded."  # @TODO: Product needs to define
