from opensearchpy import Async<PERSON>penSearch

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from settings.app_config import settings

_async_opensearch_client = AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)
_document_client = OpenSearchClient(client=_async_opensearch_client)
_search_service = OSDocumentSearchService(client=_document_client)


def get_search_service() -> DocumentSearchService:
    return _search_service
