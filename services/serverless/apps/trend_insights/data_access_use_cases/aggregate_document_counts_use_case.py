from dataclasses import dataclass
from typing import List, Optional, Tuple
from uuid import UUID

from pydantic import Field

from services.base.application.boundaries.time_input import TimeInput
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.schemas.diary_events import DiaryEventsFields
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import TimestampModel
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_time_aggregated_fields_async,
)


@dataclass(frozen=True)
class DocumentCountFields:
    TOTAL_LLIF_DOCUMENTS = "total_llif_documents"
    DIARY_EVENTS = "diary_events"
    EMOTION = "emotion"
    STRESS = "stress"
    NOTE = "note"
    CONTENT = "content"
    AUDIO = "audio"
    VIDEO = "video"
    INTERACTIVE = "interactive"
    IMAGE = "image"
    TEXT = "text"
    TASK_DOCUMENTS = f"{DiaryEventType.TASK.value}_documents"
    EVENT_DOCUMENTS = f"{DiaryEventType.EVENT.value}_documents"
    DRINK_DOCUMENTS = f"{DiaryEventType.DRINK.value}_documents"
    FOOD_DOCUMENTS = f"{DiaryEventType.FOOD.value}_documents"
    EXERCISE_DOCUMENTS = f"{DiaryEventType.EXERCISE.value}_documents"
    MEDICATION_DOCUMENTS = f"{DiaryEventType.MEDICATION.value}_documents"
    PAIN_DOCUMENTS = f"{DiaryEventType.PAIN.value}_documents"
    SUPPLEMENT_DOCUMENTS = f"{DiaryEventType.SUPPLEMENT.value}_documents"
    SYMPTOM_DOCUMENTS = f"{DiaryEventType.SYMPTOM.value}_documents"
    MEASUREMENT_DOCUMENTS = f"{DiaryEventType.MEASUREMENT.value}_documents"


class AggregateCountOutputItem(TimestampModel):
    count: int


class AggregateDocumentCountOutputItem(TimestampModel):
    total_llif_documents: int = Field(alias=DocumentCountFields.TOTAL_LLIF_DOCUMENTS)
    diary_events: int = Field(alias=DocumentCountFields.DIARY_EVENTS)
    note: int = Field(alias=DocumentCountFields.NOTE)
    emotion: int = Field(alias=DocumentCountFields.EMOTION)
    stress: int = Field(alias=DocumentCountFields.STRESS)
    content: int = Field(alias=DocumentCountFields.CONTENT)
    audio: int = Field(alias=DocumentCountFields.AUDIO)
    video: int = Field(alias=DocumentCountFields.VIDEO)
    interactive: int = Field(alias=DocumentCountFields.INTERACTIVE)
    image: int = Field(alias=DocumentCountFields.IMAGE)
    text: int = Field(alias=DocumentCountFields.TEXT)
    task_documents: int = Field(alias=DocumentCountFields.TASK_DOCUMENTS)
    event_documents: int = Field(alias=DocumentCountFields.EVENT_DOCUMENTS)
    drink_documents: int = Field(alias=DocumentCountFields.DRINK_DOCUMENTS)
    food_documents: int = Field(alias=DocumentCountFields.FOOD_DOCUMENTS)
    exercise_documents: int = Field(alias=DocumentCountFields.EXERCISE_DOCUMENTS)
    medication_documents: int = Field(alias=DocumentCountFields.MEDICATION_DOCUMENTS)
    pain_documents: int = Field(alias=DocumentCountFields.PAIN_DOCUMENTS)
    supplement_documents: int = Field(alias=DocumentCountFields.SUPPLEMENT_DOCUMENTS)
    symptom_documents: int = Field(alias=DocumentCountFields.SYMPTOM_DOCUMENTS)
    measurement_documents: int = Field(alias=DocumentCountFields.MEASUREMENT_DOCUMENTS)


DocumentCountFilters: List[Tuple[DataType, str, Optional[ValuesQuery]]] = [
    (DataType.DiaryEvents, DocumentCountFields.DIARY_EVENTS, None),
    (DataType.Emotion, DocumentCountFields.EMOTION, None),
    (DataType.Stress, DocumentCountFields.STRESS, None),
    (DataType.Note, DocumentCountFields.NOTE, None),
    (DataType.Content, DocumentCountFields.CONTENT, None),
    (DataType.Audio, DocumentCountFields.CONTENT, None),
    (DataType.Video, DocumentCountFields.VIDEO, None),
    (DataType.Image, DocumentCountFields.IMAGE, None),
    (DataType.Interactive, DocumentCountFields.INTERACTIVE, None),
    (DataType.Text, DocumentCountFields.TEXT, None),
    (
        DataType.DiaryEvents,
        DocumentCountFields.TASK_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.TASK.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.EVENT_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.EVENT.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.DRINK_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.DRINK.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.FOOD_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.FOOD.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.EXERCISE_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.EXERCISE.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.MEDICATION_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.MEDICATION.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.PAIN_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.PAIN.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.SUPPLEMENT_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.SUPPLEMENT.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.SYMPTOM_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.SYMPTOM.value]),
    ),
    (
        DataType.DiaryEvents,
        DocumentCountFields.MEASUREMENT_DOCUMENTS,
        ValuesQuery(field_name=DiaryEventsFields.TYPE, values=[DiaryEventType.MEASUREMENT.value]),
    ),
]


class AggregateDocumentCountUseCase:

    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: TimeInput,
    ) -> List[AggregateDocumentCountOutputItem]:
        result_dict = {}
        user_uuid_query = CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid)
        timestamp_query = CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte)

        for data_type, aggregate_type_name, values_query in DocumentCountFilters:
            if values_query:
                and_query = AndQuery(queries=[user_uuid_query, timestamp_query, values_query])
            else:
                and_query = AndQuery(queries=[user_uuid_query, timestamp_query])
            query = Query(type_queries=[TypeQuery(domain_types=[data_type.to_domain_model()], query=and_query)])
            results = await get_time_aggregated_fields_async(
                time_gte=time_input.time_gte,
                time_lte=time_input.time_lte,
                interval=time_input.interval,
                query=query,
            )
            result_dict[aggregate_type_name] = self._parse_results_from_buckets(results)

        result_list = []
        for i in range(len(result_dict[DocumentCountFields.DIARY_EVENTS])):
            timestamp = result_dict[DocumentCountFields.DIARY_EVENTS][i].timestamp
            diary_events = result_dict[DocumentCountFields.DIARY_EVENTS][i].count
            note = result_dict[DocumentCountFields.NOTE][i].count
            emotion = result_dict[DocumentCountFields.EMOTION][i].count
            stress = result_dict[DocumentCountFields.STRESS][i].count
            audio = result_dict[DocumentCountFields.AUDIO][i].count
            video = result_dict[DocumentCountFields.VIDEO][i].count
            interactive = result_dict[DocumentCountFields.INTERACTIVE][i].count
            image = result_dict[DocumentCountFields.IMAGE][i].count
            text = result_dict[DocumentCountFields.TEXT][i].count
            content = result_dict[DocumentCountFields.CONTENT][i].count
            total_llif_documents = (
                diary_events + emotion + stress + note + content + audio + video + interactive + image + text
            )
            task_documents = result_dict[DocumentCountFields.TASK_DOCUMENTS][i].count
            event_documents = result_dict[DocumentCountFields.EVENT_DOCUMENTS][i].count
            drink_documents = result_dict[DocumentCountFields.DRINK_DOCUMENTS][i].count
            food_documents = result_dict[DocumentCountFields.FOOD_DOCUMENTS][i].count
            exercise_documents = result_dict[DocumentCountFields.EXERCISE_DOCUMENTS][i].count
            medication_documents = result_dict[DocumentCountFields.MEDICATION_DOCUMENTS][i].count
            pain_documents = result_dict[DocumentCountFields.PAIN_DOCUMENTS][i].count
            supplement_documents = result_dict[DocumentCountFields.SUPPLEMENT_DOCUMENTS][i].count
            symptom_documents = result_dict[DocumentCountFields.SYMPTOM_DOCUMENTS][i].count
            measurement_documents = result_dict[DocumentCountFields.MEASUREMENT_DOCUMENTS][i].count
            result_list.append(
                AggregateDocumentCountOutputItem(
                    timestamp=timestamp,
                    total_llif_documents=total_llif_documents,
                    diary_events=diary_events,
                    note=note,
                    emotion=emotion,
                    stress=stress,
                    content=content,
                    audio=audio,
                    video=video,
                    interactive=interactive,
                    image=image,
                    text=text,
                    task_documents=task_documents,
                    event_documents=event_documents,
                    drink_documents=drink_documents,
                    food_documents=food_documents,
                    exercise_documents=exercise_documents,
                    medication_documents=medication_documents,
                    pain_documents=pain_documents,
                    supplement_documents=supplement_documents,
                    symptom_documents=symptom_documents,
                    measurement_documents=measurement_documents,
                )
            )

        return result_list

    @staticmethod
    def _parse_results_from_buckets(results) -> List[AggregateCountOutputItem]:
        output_results: List[AggregateCountOutputItem] = []
        for bucket in results["aggregations"]["requested_histogram"]["buckets"]:
            curr_timestamp = bucket["key_as_string"]
            doc_count = bucket["doc_count"]
            output_results.append(AggregateCountOutputItem(timestamp=curr_timestamp, count=doc_count))

        return output_results
