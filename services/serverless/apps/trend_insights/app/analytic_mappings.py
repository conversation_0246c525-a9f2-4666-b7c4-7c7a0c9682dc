from statistics import mean
from typing import Any, Dict, List

from services.base.domain.schemas.diary_events import DiaryEventsConsumablesExtensionFields
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucoseFields
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressureFields
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetricFields
from services.base.domain.schemas.events.feeling.emotion import EmotionFields
from services.base.domain.schemas.events.feeling.stress import StressFields
from services.base.domain.schemas.heart_rate import HeartRateFields
from services.base.domain.schemas.resting_heart_rate import RestingHeartRateFields
from services.base.domain.schemas.sleep import SleepSummaryFields
from services.base.domain.schemas.steps import StepsFields
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType
from services.serverless.apps.trend_insights.data_access_use_cases.aggregate_document_counts_use_case import (
    DocumentCountFields,
)
from services.serverless.apps.trend_insights.data_access_use_cases.aggregate_heart_rate_use_case import (
    AggregateHeartRateUseCase,
)
from services.serverless.apps.trend_insights.data_access_use_cases.aggregate_nutrition_use_case import (
    AggregateNutritionRateUseCase,
)
from services.serverless.apps.trend_insights.data_access_use_cases.aggregate_resting_heart_rate_use_case import (
    AggregateRestingHeartRateUseCase,
)
from services.serverless.apps.trend_insights.data_access_use_cases.aggregate_sleep_use_case import (
    AggregateSleepUseCase,
)
from services.serverless.apps.trend_insights.data_access_use_cases.aggregate_steps_use_case import (
    AggregateStepsUseCase,
)

AnalyticTypeToAggregationUseCaseMapping: Dict[AnalyticType, Any] = {
    AnalyticType.RestingHeartRate: AggregateRestingHeartRateUseCase,
    AnalyticType.HeartRate: AggregateHeartRateUseCase,
    AnalyticType.Sleep: AggregateSleepUseCase,
    AnalyticType.Steps: AggregateStepsUseCase,
    AnalyticType.DiaryEventsNutrition: AggregateNutritionRateUseCase,
    # AnalyticType.DocumentCount: AggregateDocumentCountUseCase,
}

AnalyticTypeToSeriesMapping: Dict[AnalyticType, List[str]] = {
    AnalyticType.RestingHeartRate: [RestingHeartRateFields.BPM_AVG],
    AnalyticType.HeartRate: [HeartRateFields.BPM_AVG],
    AnalyticType.Sleep: [SleepSummaryFields.ASLEEP_SECONDS],
    AnalyticType.Steps: [StepsFields.STEPS],
    AnalyticType.BloodPressure: [BloodPressureFields.SYSTOLIC],
    AnalyticType.BloodGlucose: [BloodGlucoseFields.VALUE],
    AnalyticType.BodyMetric: [BodyMetricFields.VALUE],
    AnalyticType.Stress: [StressFields.RATING],
    AnalyticType.Emotion: [EmotionFields.RATING],
    AnalyticType.DiaryEventsNutrition: [
        DiaryEventsConsumablesExtensionFields.CALORIES,
        DiaryEventsConsumablesExtensionFields.FAT,
        DiaryEventsConsumablesExtensionFields.CARBOHYDRATES,
        DiaryEventsConsumablesExtensionFields.PROTEIN,
        DiaryEventsConsumablesExtensionFields.SODIUM,
    ],
}

"""
AnalyticType.DocumentCount: [
    DocumentCountFields.TOTAL_LLIF_DOCUMENTS,
    DocumentCountFields.EMOTION,
    DocumentCountFields.STRESS,
    DocumentCountFields.DIARY_EVENTS,
    DocumentCountFields.NOTE,
    DocumentCountFields.CONTENT,
    DocumentCountFields.DRINK_DOCUMENTS,
    DocumentCountFields.FOOD_DOCUMENTS,
    DocumentCountFields.EXERCISE_DOCUMENTS,
    DocumentCountFields.TASK_DOCUMENTS,
    DocumentCountFields.EVENT_DOCUMENTS,
    DocumentCountFields.MEDICATION_DOCUMENTS,
    DocumentCountFields.PAIN_DOCUMENTS,
    DocumentCountFields.SUPPLEMENT_DOCUMENTS,
    DocumentCountFields.SYMPTOM_DOCUMENTS,
    DocumentCountFields.MEASUREMENT_DOCUMENTS,
],
"""

AnalyticSeriesToAggregationFunction: Dict[str, str] = {
    # TODO How to handle generic series from v3 types?
    RestingHeartRateFields.BPM_AVG: "mean",
    HeartRateFields.BPM_AVG: "mean",
    SleepSummaryFields.ASLEEP_SECONDS: "mean",
    StepsFields.STEPS: "mean",
    BloodPressureFields.SYSTOLIC: "mean",
    BloodGlucoseFields.VALUE: "mean",
    BodyMetricFields.VALUE: "mean",
    DiaryEventsConsumablesExtensionFields.CALORIES: "sum",
    DiaryEventsConsumablesExtensionFields.FAT: "sum",
    DiaryEventsConsumablesExtensionFields.CARBOHYDRATES: "sum",
    DiaryEventsConsumablesExtensionFields.PROTEIN: "sum",
    DiaryEventsConsumablesExtensionFields.SODIUM: "sum",
    DocumentCountFields.TOTAL_LLIF_DOCUMENTS: "sum",
    DocumentCountFields.EMOTION: "sum",
    DocumentCountFields.STRESS: "sum",
    DocumentCountFields.DIARY_EVENTS: "sum",
    DocumentCountFields.NOTE: "sum",
    DocumentCountFields.CONTENT: "sum",
    DocumentCountFields.DRINK_DOCUMENTS: "sum",
    DocumentCountFields.FOOD_DOCUMENTS: "sum",
    DocumentCountFields.EXERCISE_DOCUMENTS: "sum",
    DocumentCountFields.TASK_DOCUMENTS: "sum",
    DocumentCountFields.EVENT_DOCUMENTS: "sum",
    DocumentCountFields.MEDICATION_DOCUMENTS: "sum",
    DocumentCountFields.PAIN_DOCUMENTS: "sum",
    DocumentCountFields.SUPPLEMENT_DOCUMENTS: "sum",
    DocumentCountFields.SYMPTOM_DOCUMENTS: "sum",
    DocumentCountFields.MEASUREMENT_DOCUMENTS: "sum",
}

AggregationFunctionToMethod = {
    "mean": lambda x: mean(x) if x else None,
    "sum": lambda x: sum(x) if x else None,
    "max": lambda x: max(x) if x else None,
}
