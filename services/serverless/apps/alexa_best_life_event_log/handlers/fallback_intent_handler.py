from typing import Union

import ask_sdk_core.utils as ask_utils
from ask_sdk_core.dispatch_components import AbstractRe<PERSON><PERSON>and<PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_model import Response

from services.serverless.apps.alexa_best_life_event_log.domain.intent_names import IntentName
from services.serverless.apps.alexa_best_life_event_log.domain.phrases import EXAMPLE_COMMAND_USAGE
from services.serverless.apps.alexa_best_life_event_log.domain.request_types import RequestType


class FallbackIntentHandler(AbstractRequestHandler):
    """FallbackIntent triggers when a customer says something that doesn’t map to any intents in your skill
    It must also be defined in the language model (if the locale supports it)
    This handler can be safely added but will be ingnored in locales that do not support it yet
    """

    def can_handle(self, handler_input: HandlerInput) -> bool:
        is_intent_request: bool = ask_utils.is_request_type(RequestType.INTENT_REQUEST.value)(handler_input)
        is_fallback_intent_name: bool = ask_utils.is_intent_name(IntentName.FALLBACK_INTENT.value)(handler_input)
        return is_intent_request and is_fallback_intent_name

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        speak_output = f"Best Life could not recognize this. Try saying {EXAMPLE_COMMAND_USAGE}"
        return handler_input.response_builder.speak(speak_output).response
