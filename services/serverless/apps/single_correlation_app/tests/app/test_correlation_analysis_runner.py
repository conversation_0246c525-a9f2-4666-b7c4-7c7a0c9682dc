from datetime import timedelta
from uuid import uuid4

from services.base.domain.enums.event_type import EventType
from services.serverless.apps.single_correlation_app.app.correlation_analysis_runner import CorrelationAnalysisRunner
from services.serverless.apps.single_correlation_app.app.enums.enums import CorrelationInputTypes
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    SingleCorrelationSingleInput,
)


class TestCorrelationRunner:
    async def test_fetch_documents_historical_offset_last_ten_days_results_empty(
        self, correlation_runner: CorrelationAnalysisRunner
    ):
        input_object = SingleCorrelationSingleInput(
            event_type=EventType.DiaryEvents,
            name="this is event name",
            input_type=CorrelationInputTypes.OUTCOME,
        )
        historical_offset = timedelta(days=10)

        response = await correlation_runner._fetch_documents(
            input_object=input_object,
            user_id=uuid4(),
            historical_offset=historical_offset,
        )
        assert response.timestamps is None

    async def test_fetch_documents_historical_offset_as_none_results_not_empty(
        self, correlation_runner: CorrelationAnalysisRunner
    ):
        input_object = SingleCorrelationSingleInput(
            event_type=EventType.DiaryEvents,
            name="this is event name",
            input_type=CorrelationInputTypes.OUTCOME,
        )
        historical_offset = None

        response = await correlation_runner._fetch_documents(
            input_object=input_object,
            user_id=uuid4(),
            historical_offset=historical_offset,
        )
        if response.timestamps:
            assert len(response.timestamps) > 0
