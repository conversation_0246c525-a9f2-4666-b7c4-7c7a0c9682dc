import logging
from typing import Sequence
from uuid import UUID

from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.serverless.apps.single_correlation_app.app.enums.enums import (
    CorrelationConfidence,
    CorrelationInputTypes,
)
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    SINGLE_CORRELATION_TYPES,
    OutcomeTriggersObject,
    SingleCorrelationSingleInput,
)


class CorrelationPreparer:
    class EventNamesOutput(BaseDataModel):
        high_reliability_outcomes: Sequence[str]
        low_reliability_outcomes: Sequence[str]
        triggers: Sequence[str]

    def __init__(self, search_service: DocumentSearchService, agg_service: AggregationService):
        self._search_service = search_service
        self._agg_service = agg_service

    async def get_user_triggers(self, user_id: UUID) -> list[OutcomeTriggersObject]:
        logging.info(f"Getting user triggers for user: {user_id}")
        event_names_output = await self._get_event_names(user_id=user_id)
        if not event_names_output.triggers:
            return []

        outcome_triggers_input = []

        if event_names_output.high_reliability_outcomes:
            outcome_triggers_input.extend(
                self._create_events_outcome_trigger_object(
                    outcome_list=event_names_output.high_reliability_outcomes,
                    trigger_list=event_names_output.triggers,
                    confidence=CorrelationConfidence.HIGH_CONFIDENCE,
                )
            )
        if event_names_output.low_reliability_outcomes:
            outcome_triggers_input.extend(
                self._create_events_outcome_trigger_object(
                    outcome_list=event_names_output.low_reliability_outcomes,
                    trigger_list=event_names_output.triggers,
                    confidence=CorrelationConfidence.LOW_CONFIDENCE,
                )
            )

        return outcome_triggers_input

    async def _get_event_names(self, user_id: UUID) -> EventNamesOutput:
        adjusted_field_name = self._agg_service.adjust_frequency_distribution_field(
            field_name=EventFields.NAME,
            domain_types=[DiaryEvents],
        )
        aggregates = await self._agg_service.frequency_distribution_by_query(
            query=Query(
                type_queries=[
                    TypeQuery(
                        domain_types=[DiaryEvents],
                        query=AndQuery(
                            queries=[
                                CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_id),
                                ValuesQuery(
                                    field_name=EventFields.TYPE,
                                    values=SINGLE_CORRELATION_TYPES,
                                ),
                            ]
                        ),
                    ),
                    # TypeQuery(
                    #     domain_types=[Symptom],  # Add V3 types
                    #     query=CommonLeafQueries.owner_id_value_query(user_uuid=user_id),
                    # ),
                ]
            ),
            field_name=adjusted_field_name,
            size=100_000,
        )
        high_reliability_outcomes = []
        low_reliability_outcomes = []
        triggers = []

        for agg in aggregates:
            if agg.document_count > 10:
                triggers.append(agg.aggregation_key)
                if agg.document_count > 30:
                    high_reliability_outcomes.append(agg.aggregation_key)
                else:
                    low_reliability_outcomes.append(agg.aggregation_key)
        return self.EventNamesOutput(
            high_reliability_outcomes=high_reliability_outcomes,
            low_reliability_outcomes=low_reliability_outcomes,
            triggers=triggers,
        )

    @staticmethod
    def _create_events_outcome_trigger_object(
        outcome_list: Sequence[str], trigger_list: Sequence[str], confidence: CorrelationConfidence
    ) -> Sequence[OutcomeTriggersObject]:
        outcome_triggers_input = []
        trigger_set = set(trigger_list)

        for outcome_event_name in outcome_list:
            filtered_triggers = trigger_set - {outcome_event_name}
            if not filtered_triggers:
                continue

            outcome_object = SingleCorrelationSingleInput(
                event_type=EventType.DiaryEvents,
                name=outcome_event_name,
                confidence=confidence,
                input_type=CorrelationInputTypes.OUTCOME,
            )

            trigger_objects = [
                SingleCorrelationSingleInput(
                    event_type=EventType.DiaryEvents,
                    name=trigger_name,
                    input_type=CorrelationInputTypes.TRIGGER,
                )
                for trigger_name in filtered_triggers
            ]

            if not trigger_objects:
                continue

            outcome_triggers_input.append(OutcomeTriggersObject(outcome=outcome_object, triggers=trigger_objects))

        return outcome_triggers_input
