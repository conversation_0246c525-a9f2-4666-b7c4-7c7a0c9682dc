import logging
from typing import Sequence

from opensearchpy import NotFoundError

from services.base.application.retry import retry
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client


class OpenSearchInitializerV3:
    _client = get_async_default_os_client()

    @classmethod
    async def initialize(cls, index_models: Sequence[OpenSearchIndex]) -> None:
        for im in index_models:
            await cls.initialize_index_model(index_model=im)

    @classmethod
    async def initialize_index_model(cls, index_model: OpenSearchIndex) -> None:
        """
        Initialize the full lifecycle of an index model:
        - Index template
        - Index creation
        - Alias setup
        - Rollover configuration
        """
        logging.info(f"Initializing index model {index_model.name}")
        await cls._initialize_index_state_management(index_model=index_model)
        await cls._initialize_index_template(index_model=index_model)

        await cls._initialize_first_index(index_model=index_model)
        await cls._initialize_alias(index_model=index_model)

        # Always update mapping of existing indices
        await OpenSearchUtils.update_index_mapping(index_model=index_model)

    @classmethod
    async def _initialize_index_state_management(cls, index_model: OpenSearchIndex) -> None:
        """ISM ensures the all newly created indices matching index patterns get rollovered when conditions are met"""
        policy_name = f"{index_model.name}_ism_policy"
        policy_body = {
            "policy": {
                "description": f"Auto rollover policy for {index_model.name}",
                "default_state": "default",
                "states": [
                    {"name": "default", "actions": [{"rollover": index_model.rollover_conditions}], "transitions": []},
                ],
                "ism_template": {"index_patterns": [f"{index_model.name}*"], "priority": 100},
            }
        }
        url = f"/_plugins/_ism/policies/{policy_name}"
        try:
            # If the policy already exists, we want it updated, need to pass in seq number and primary term
            response = await cls._client.transport.perform_request(
                method="GET", url=f"/_plugins/_ism/policies/{policy_name}"
            )
            url = url + f"?if_seq_no={response["_seq_no"]}&if_primary_term={response["_primary_term"]}"
        except NotFoundError:
            ...

        @retry(max_times=5, delay=1)
        async def _initialize(url: str):
            await cls._client.transport.perform_request(method="PUT", url=url, body=policy_body)

        logging.info(f"Initializing ism with url {url}")
        await _initialize(url=url)

    @classmethod
    async def _initialize_first_index(cls, index_model: OpenSearchIndex) -> None:
        # Initial index based on rollover pattern
        index_name = f"{index_model.name}-{ALIAS_FIRST_INDEX_POINTER}"
        if not await cls._client.indices.exists(index=index_name):
            await cls._client.indices.create(index=index_name)

    @classmethod
    async def _initialize_index_template(cls, index_model: OpenSearchIndex) -> None:
        """Index templates ensures any index created under same pattern will inherit defined configuration"""
        await cls._initialize_component_template(index_model=index_model)
        logging.debug(f"Initializing index template {index_model.name}")
        await cls._client.indices.put_index_template(
            name=f"{index_model.name}_index_template",
            body={
                "index_patterns": [f"{index_model.name}*"],
                "composed_of": [f"{index_model.name}_component_template"],
            },
        )

    @classmethod
    async def _initialize_component_template(cls, index_model: OpenSearchIndex) -> None:
        logging.debug(f"Initializing component template {index_model.name}")
        await cls._client.cluster.put_component_template(
            name=f"{index_model.name}_component_template",
            body={
                "template": {
                    "settings": index_model.settings,
                    "mappings": index_model.mappings,
                }
            },
        )

    @classmethod
    async def _initialize_alias(cls, index_model: OpenSearchIndex) -> None:
        """Alias can group multiple indices for searches, it also defines single write index pointer for inserts"""
        index_name = f"{index_model.name}-{ALIAS_FIRST_INDEX_POINTER}"
        logging.debug(f"Initializing alias for index {index_model.name}")

        # Initialize in case the alias does not exist
        if index_model.aliases:
            for alias in index_model.aliases:
                if not await cls._client.indices.exists_alias(name=alias):
                    await cls._client.indices.put_alias(index=index_name, name=alias, body={"is_write_index": True})
