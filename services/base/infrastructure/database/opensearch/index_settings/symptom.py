from typing import Any, Dict

from opensearchpy import Integer, Keyword

from services.base.domain.schemas.events.symptom import SymptomFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    SYMPTOM_INDEX,
    OpenSearchIndex,
)


def get_symptom_mapping() -> Dict[str, Any]:
    pain = {
        SymptomFields.BODY_PARTS: Keyword(),
        SymptomFields.RATING: Integer(),
    }
    return convert_dsl_mapping_to_dict(custom_mapping=pain | get_base_event_mapping(), strict_mapping=True)


def get_symptom_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": SYMPTOM_INDEX,
    }


SymptomIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=SYMPTOM_INDEX,
    mappings=get_symptom_mapping(),
    settings=get_symptom_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[SYMPTOM_INDEX],
)
