from typing import Any, Dict

from opensearchpy import Date, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.templates.template import TemplateFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_document_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    TEMPLATE_INDEX,
    OpenSearchIndex,
)
from settings.app_config import settings


def get_template_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            TemplateFields.NAME: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
            TemplateFields.RBAC: Object(
                properties={
                    DocumentLabels.OWNER_ID: Keyword(),
                }
            ),
            TemplateFields.ARCHIVED_AT: Date(),
            TemplateFields.DOCUMENT: Text(copy_to=OS_LABEL_CATCH_ALL),
            TemplateFields.DOCUMENT_NAME: Text(
                fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL
            ),
            TemplateFields.DOCUMENT_TYPE: Keyword(),
            TemplateFields.TEMPLATE_IDS: Keyword(),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
        }
        | get_document_mapping()
    )


def get_template_settings():
    return {
        "default_pipeline": None,
        "number_of_shards": settings.OS_PRIMARY_SHARDS_COUNT,
        "number_of_replicas": settings.OS_REPLICA_SHARDS_COUNT,
    }


TemplateIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=TEMPLATE_INDEX,
    mappings=get_template_mapping(),
    settings=get_template_settings(),
    is_splittable=False,
)
