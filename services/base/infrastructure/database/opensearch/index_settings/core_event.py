from typing import Any, Dict

from opensearchpy import <PERSON><PERSON><PERSON>, Integer

from services.base.domain.schemas.events.core_event import Core<PERSON><PERSON>Fields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    CORE_EVENT_INDEX,
    OpenSearchIndex,
)


def get_core_event_mapping() -> Dict[str, Any]:
    core_event_mapping = {
        CoreEventFields.RATING: Integer(),
        CoreEventFields.IS_GROUP: Boolean(),
    }
    return convert_dsl_mapping_to_dict(core_event_mapping | get_base_event_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": CORE_EVENT_INDEX,
    }


CoreEventIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=CORE_EVENT_INDEX,
    mappings=get_core_event_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[CORE_EVENT_INDEX],
)
