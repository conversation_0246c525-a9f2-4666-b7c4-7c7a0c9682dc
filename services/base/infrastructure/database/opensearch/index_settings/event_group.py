from typing import Any

from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_base_event_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    EVENT_GROUP_INDEX,
    OpenSearchIndex,
)


def get_event_group_mapping() -> dict[str, Any]:
    return convert_dsl_mapping_to_dict(get_base_event_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": EVENT_GROUP_INDEX,
    }


EventGroupIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=EVENT_GROUP_INDEX,
    mappings=get_event_group_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[EVENT_GROUP_INDEX],
)
