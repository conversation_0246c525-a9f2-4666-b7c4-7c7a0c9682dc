from opensearchpy import AsyncOpenSearch, OpenSearch

from settings.app_config import settings

_client = OpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)
_async_client = AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True)


def get_default_os_client() -> OpenSearch:
    return _client


def get_async_default_os_client() -> AsyncOpenSearch:
    return _async_client
