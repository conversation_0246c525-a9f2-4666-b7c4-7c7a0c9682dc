from __future__ import annotations

from collections import defaultdict
from typing import Any, Optional, Sequence

from services.base.application.database.models.sorts import Sort
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils


class OpenSearchRequestBuilder:
    def __init__(self):
        self._request_body: dict[str, Any] = {}

    def build(self):
        return self._request_body

    def with_query(self, query: dict) -> OpenSearchRequestBuilder:
        self._request_body |= {"query": query}
        return self

    def with_query_v2(self, query: Optional[dict]) -> OpenSearchRequestBuilder:
        if query:
            self._request_body |= query
        return self

    def with_aggs(self, aggs: dict) -> OpenSearchRequestBuilder:
        self._request_body |= {"aggs": aggs}
        return self

    def with_from(self, offset: int) -> OpenSearchRequestBuilder:
        self._request_body |= {"from": offset}
        return self

    def with_size(self, size: int) -> OpenSearchRequestBuilder:
        self._request_body |= {"size": size}
        return self

    def with_search_after(self, search_after: Optional[Sequence[str]]) -> OpenSearchRequestBuilder:
        if not search_after:
            return self

        self._request_body |= {"search_after": search_after}
        return self

    def with_sorts(self, sorts: Optional[Sequence[Sort]]) -> OpenSearchRequestBuilder:
        if not sorts:
            return self

        sorts_body: list[str | dict[str, Any]] = []

        for sort in sorts:
            if sort.name == DocumentLabels.SORT_SCORE:
                sorts_body.append(sort.name)
                continue

            query_part = defaultdict(dict)

            if sort.order:
                query_part[sort.name].update({"order": OpenSearchUtils.map_sort_order(sort.order)})

            if not query_part[sort.name]:
                continue

            sorts_body.append(dict(query_part))

        self._request_body |= {"sort": sorts_body}
        return self
