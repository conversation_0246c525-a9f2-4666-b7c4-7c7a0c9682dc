import logging
from collections import defaultdict
from typing import Sequence, Union

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.infrastructure.database.opensearch.opensearch_constants import SINGLE_VALUE_AGG_TYPES
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_ID


def filter_results_from_aggregation(
    in_results: dict,
    requested_fields_and_agg: Sequence[tuple[str, str] | str],
    time_field_name: str = DocumentLabels.TIMESTAMP,
) -> list[dict]:
    """Extracts a given set of fields from an ES list of documents, and formats
       the result in the structure expected by the front end.
    Args:
        in_results (dict): ES search results
        requested_fields_and_agg (list): list of strings containing field names
    Returns:
        list: filtered results
    """
    response_values: list[dict] = []

    for bucket in in_results.get("aggregations", {}).get("requested_histogram", {}).get("buckets", []):
        entry = defaultdict()
        # Append timestamp
        entry[time_field_name] = bucket.get("key_as_string")
        for requested_field, agg_type in requested_fields_and_agg:
            value = {}
            # skip timestamp
            if requested_field == time_field_name:
                continue
            # add aggregations values
            if agg_type in SINGLE_VALUE_AGG_TYPES:
                bucket_name = f"{requested_field}_{agg_type}"
                value = bucket.get(bucket_name).get("value")
            elif agg_type in ["terms"]:
                bucket_name = f"{requested_field}_{agg_type}"
                value_bucket = bucket.get(bucket_name).get("buckets", [])
                value = value_bucket
            elif agg_type in ["geo_centroid"]:
                bucket_name = f"{requested_field}_{agg_type}"
                value_bucket = bucket.get(bucket_name).get("location", {})
                value = value_bucket
            else:
                logging.error("Unhandled agg_type: %s", agg_type)
            entry[requested_field] = value
        # append entry to values list
        response_values.append(dict(entry))

    return response_values


def filter_results_from_hits(in_results: dict, requested_fields: list, get_doc_id: bool = True) -> list[dict]:
    """Extracts a given set of fields from an ES list of documents in

    Args:
        in_results (dict): ES search results
        requested_fields (list): list of strings containing field names
        get_doc_id: parameter to also include document _ids in the response
    Returns:
        list: filtered results
    """
    response_values: list[dict] = []

    for hit in in_results.get("hits", {}).get("hits", []):
        entry = defaultdict(list)
        values = hit.get("_source")
        if get_doc_id:
            entry[OS_LABEL_ID] = hit.get(OS_LABEL_ID)
            entry[DocumentLabels.DOC_ID] = values.get(DocumentLabels.DOC_ID)
        for requested_field in requested_fields:
            entry[requested_field] = values.get(requested_field)
        response_values.append(dict(entry))

    return response_values


def get_os_task_id_from_results(results: dict) -> Union[str, None]:
    return results.get("task", None)
