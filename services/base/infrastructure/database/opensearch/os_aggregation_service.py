import json
import logging
from datetime import datetime
from typing import Any, Sequence

from opensearchpy import Async<PERSON>penSearch

from services.base.application.boundaries.aggregates import (
    DateHistogramAggregate,
    DateHistogramFieldAggregate,
    FrequencyDistributionAggregate,
)
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.utils.serializers import serialize_with_datetime
from services.base.application.utils.time import TimeUtils
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.aggregations import (
    AggregationMethod,
    BucketAggregationMethod,
    DateHistogramAggregation,
    DateHistogramFieldAggregation,
)
from services.base.domain.schemas.query.query import Query
from services.base.infrastructure.database.opensearch.opensearch_aggs_builder import OpenSearchAggsBuilder
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataSchemaToIndexModelMapping,
)
from services.base.infrastructure.database.opensearch.opensearch_request_builder import OpenSearchRequestBuilder
from services.base.infrastructure.database.opensearch.query_translator.field_refiner import Field<PERSON><PERSON>iner
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator


class OSAggregationService(AggregationService):
    def __init__(self, client: AsyncOpenSearch):
        self._client = client

    async def close(self):
        await self._client.close()

    async def date_histogram_by_query(
        self,
        query: Query,
        aggregation: DateHistogramAggregation,
    ) -> Sequence[DateHistogramAggregate]:
        query_translation_result = QueryTranslator.translate(query=query)

        query_as_dict = query_translation_result.query_as_dict
        indices = query_translation_result.indices

        histogram_field_aggregations = self._build_histogram_field_aggregations(aggregation)

        aggs_builder = OpenSearchAggsBuilder().with_date_histogram_agg(
            field=DocumentLabels.TIMESTAMP,
            histogram_field_aggregations=histogram_field_aggregations,
            interval=aggregation.interval,
            timezone=aggregation.timezone,
        )
        request_builder = (
            OpenSearchRequestBuilder()
            .with_query_v2(query=query_as_dict)
            .with_aggs(aggs=aggs_builder.build())
            .with_size(0)
        )
        body = request_builder.build()

        logging.info(f"aggregating date histogram, query: {json.dumps(body, default=serialize_with_datetime)}")
        response = await self._client.search(
            body=body,
            index=[i + "*" for i in indices],
        )
        return self._to_date_histogram_aggregate(
            response=response,
            histogram_field_aggregations=aggregation.histogram_field_aggregations,
            default_aggregation_method=aggregation.default_aggregation_method,
            aggregation_interval=aggregation.interval,
        )

    async def frequency_distribution_by_query(
        self,
        query: Query,
        field_name: str,
        size: int,
    ) -> Sequence[FrequencyDistributionAggregate]:
        query_translation_result = QueryTranslator.translate(query=query)

        query_as_dict = query_translation_result.query_as_dict
        indices = query_translation_result.indices

        aggs_builder = OpenSearchAggsBuilder().with_frequency_distribution_agg(field_name=field_name, size=size)
        request_builder = (
            OpenSearchRequestBuilder()
            .with_query_v2(query=query_as_dict)
            .with_aggs(aggs=aggs_builder.build())
            .with_size(0)
        )
        body = request_builder.build()

        logging.info(f"aggregating frequency distribution, query: {json.dumps(body, default=serialize_with_datetime)}")
        response = await self._client.search(
            body=body,
            index=[i + "*" for i in indices],
        )
        return self._to_frequency_distribution_aggregate(response=response)

    def adjust_frequency_distribution_field(self, domain_types: Sequence[type[Document]], field_name: str) -> str:
        refined_field_name = FieldRefiner.adjust_field_name(field_name=field_name)

        field_mappings = set()
        for tp in domain_types:
            index_model = DataSchemaToIndexModelMapping[tp]
            field_mapping = FieldRefiner.get_field_mapping(
                index_mapping=index_model.mappings, field_name=refined_field_name
            )
            field_mappings.add(json.dumps(field_mapping))
        if len(field_mappings) != 1:
            raise ValueError("Field mappings are inconsistent across domain types.")
        field_mapping = json.loads(field_mappings.pop())

        field_type = field_mapping.get("type")
        if field_type == "text":
            if FieldRefiner.has_keyword_field(field_mapping=field_mapping):
                return refined_field_name + ".keyword"
        elif field_type is None:
            raise ValueError("Field mapping does not specify a type.")
        return refined_field_name

    def _to_frequency_distribution_aggregate(
        self,
        response: dict,
    ) -> Sequence[FrequencyDistributionAggregate]:
        results: list[FrequencyDistributionAggregate] = []

        buckets: Sequence[dict[str, Any]] = response["aggregations"]["aggregation"]["buckets"]
        for bucket in buckets:
            results.append(
                FrequencyDistributionAggregate(
                    aggregation_key=bucket["key"],
                    document_count=bucket["doc_count"],
                )
            )
        return results

    def _to_date_histogram_aggregate(
        self,
        response: dict,
        histogram_field_aggregations: Sequence[DateHistogramFieldAggregation],
        default_aggregation_method: AggregationMethod,
        aggregation_interval: str,
    ) -> Sequence[DateHistogramAggregate]:
        results: list[DateHistogramAggregate] = []

        buckets: Sequence[dict[str, Any]] = response["aggregations"]["requested_histogram"]["buckets"]
        for i, bucket in enumerate(buckets):
            aggregates: list[DateHistogramFieldAggregate] = []
            timestamp = datetime.fromisoformat(bucket["key_as_string"])
            for agg in histogram_field_aggregations:
                agg_method = (agg.aggregation_method or default_aggregation_method).value
                field_name = agg.field_name

                if agg_method == AggregationMethod.STATS:
                    aggregated_field = f"{field_name}:extended_stats"
                    stats = bucket[aggregated_field]
                    for k, v in stats.items():
                        if k == "std_deviation_bounds":
                            continue
                        value = v if v != "NaN" else None
                        aggregates.append(DateHistogramFieldAggregate(agg_method=k, field=field_name, value=value))
                else:
                    aggregated_field = f"{field_name}:{agg_method}"
                    aggregates.append(
                        DateHistogramFieldAggregate(
                            agg_method=agg_method, field=field_name, value=bucket[aggregated_field]["value"]
                        )
                    )
                for bucket_agg in agg.bucket_aggregation:
                    agg_method = bucket_agg.aggregation_method.value
                    agg_key = f"{aggregated_field}:{agg_method}"
                    value = bucket[agg_key]["value"] if agg_key in bucket else None
                    aggregates.append(DateHistogramFieldAggregate(agg_method=agg_method, field=field_name, value=value))

            # Calculate the end_time based on the next bucket
            if i + 1 < len(buckets):
                end_time = datetime.fromisoformat(buckets[i + 1]["key_as_string"])
            else:
                interval = TimeUtils.get_relativedelta_from_aggregation_interval(
                    aggregation_interval=aggregation_interval
                )
                end_time = timestamp + interval

            results.append(
                DateHistogramAggregate(
                    timestamp=timestamp,
                    end_time=end_time,
                    aggregates=aggregates,
                    doc_count=bucket["doc_count"],
                )
            )
        return results

    def _build_histogram_field_aggregations(self, aggregation: DateHistogramAggregation) -> dict:
        histogram_field_aggregations = {}
        for agg in aggregation.histogram_field_aggregations:
            agg_method = (agg.aggregation_method or aggregation.default_aggregation_method).value
            if agg_method == "stats":
                agg_method = "extended_stats"
            agg_key = f"{agg.field_name}:{agg_method}"
            sub_agg: dict = {
                agg_key: {agg_method: {"field": agg.field_name}},
            }
            histogram_field_aggregations |= sub_agg

            # Bucket aggregations
            for bucket_agg in agg.bucket_aggregation:
                agg_method = bucket_agg.aggregation_method.value
                bucket_agg_key = f"{agg_key}:{agg_method}"
                sub_agg = {bucket_agg_key: {agg_method: {"buckets_path": agg_key}}}
                if agg_method == BucketAggregationMethod.MOVING_AVG.value:
                    sub_agg[bucket_agg_key][agg_method]["window"] = bucket_agg.window
                histogram_field_aggregations |= sub_agg
        return histogram_field_aggregations
