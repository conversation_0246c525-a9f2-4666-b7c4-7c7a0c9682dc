from typing import Any, Dict, Sequence, Type
from uuid import UUID

from services.base.application.boundaries.documents import (
    SearchDocumentsOutputBoundary,
    SearchDocumentsOutputListItem,
)
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.enums.bulk_result import bulk_operation_to_bulk_result_mapping
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.shared import IdentifiableModel
from services.base.infrastructure.database.opensearch.continuation_token_utils import ContinuationTokenUtils


class OSResponseParser:
    @staticmethod
    async def to_domain_from_search[T: Document](data_schema: Type[T], response: dict) -> Sequence[T]:
        result: list[T] = []
        hits = response["hits"]["hits"]
        for hit in hits:
            source = hit["_source"]
            model = data_schema(**source, id=hit["_id"])
            result.append(model)
        return result

    @staticmethod
    def _get_models_from_search_response[T: Document](
        data_schema: Type[T],
        response: Dict,
    ) -> SearchDocumentsOutputBoundary[T]:
        output: list[SearchDocumentsOutputListItem] = []
        hits: Sequence[Dict[str, Any]] = response["hits"]["hits"]
        sort: Sequence[int] | None = None
        if hits:
            sort = hits[-1].get("sort", None)
            for hit in hits:
                internal_id = hit["_id"]
                source = hit["_source"]
                if issubclass(data_schema, IdentifiableModel):
                    source |= {DocumentLabels.ID: source[DocumentLabels.DOC_ID]}
                else:
                    source |= {DocumentLabels.ID: hit["_id"]}
                output.append(
                    SearchDocumentsOutputListItem(
                        document=data_schema(**source),
                        id=internal_id,
                    )
                )
        return SearchDocumentsOutputBoundary(
            results=output,
            continuation_token=(
                ContinuationTokenUtils.encode_continuation_token(search_sort_mark=[str(sort) for sort in sort])
                if sort
                else None
            ),
        )

    @staticmethod
    async def to_domain_from_mget[T: Document](data_schema: Type[T], response: dict) -> Sequence[T]:
        result: list[T] = []
        for doc in response["docs"]:
            if doc["found"]:
                source = doc["_source"]
                model = data_schema(**source, id=doc["_id"])
                result.append(model)

        return result

    @staticmethod
    async def get_doc_ids_from_bulk_response(bulk_response: dict, action: BulkOperation) -> Sequence[UUID]:
        uuids = []
        for item in bulk_response["items"]:
            expected_bulk_result = bulk_operation_to_bulk_result_mapping[action]
            result = item[action.value]
            if result["result"] == expected_bulk_result.value:
                uuids.append(result["_id"])

        return uuids
