import logging
from typing import Sequence
from zoneinfo import ZoneInfo

from opensearchpy import AsyncOpenSearch, OpenSearchException

from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.retry import retry
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.environment_repository import EnvironmentRepository
from services.base.domain.repository.models.environment_inputs import EnvironmentInputs
from services.base.domain.schemas.environment import Environment
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataSchemaToIndexModelMapping,
    OpenSearchIndexToDocumentModelMapping,
)
from services.base.type_resolver import TypeResolver
from settings.app_config import settings


class OSEnvironmentRepository(EnvironmentRepository):
    _os_client: AsyncOpenSearch

    def __init__(self, client: AsyncOpenSearch):
        self._os_client = client

    @retry(exceptions=OpenSearchException)
    async def insert(
        self, environment_documents: Sequence[EnvironmentInputs], force_strong_consistency: bool = False
    ) -> Sequence[Environment]:
        insert_requests: list[dict] = []
        for env_input in environment_documents:
            env_input.timestamp = env_input.timestamp.astimezone(ZoneInfo("UTC"))
            action = {
                BulkOperation.Create.value: {
                    "_index": self._get_index_from_env_input_boundary(env_input=env_input).split("-")[0],
                }
            }
            insert_requests.append(action)
            insert_requests.append(env_input.model_dump(exclude={DocumentLabels.ID}, by_alias=True))

            # TODO: what if response contains errors?
        refresh = "wait_for" if force_strong_consistency else "false"
        os_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        if os_response["errors"]:
            logging.error(f"There were errors during insertion of environment. Response: {os_response}")

        docs = await self._do_search_by_id(
            ids_and_indices=[
                (
                    (item["create"]["_id"]),
                    item["create"]["_index"],
                )
                for item in os_response["items"]
            ]
        )

        return docs

    @retry(exceptions=OpenSearchException)
    async def search_by_id(self, ids_and_types: Sequence[tuple[str, type[Environment]]]) -> Sequence[Environment]:
        return await self._do_search_by_id(
            ids_and_indices=[
                (
                    doc_id,
                    self._get_index_from_id_and_type(doc_id=doc_id, domain_type=doc_type),
                )
                for doc_id, doc_type in ids_and_types
            ]
        )

    async def _do_search_by_id(self, ids_and_indices: Sequence[tuple[str, str]]) -> Sequence[Environment]:
        response = await self._os_client.mget(
            body={
                "docs": [
                    {
                        "_id": doc_id,
                        "_index": index,
                    }
                    for doc_id, index in ids_and_indices
                ]
            }
        )
        return await self._to_domain_from_mget_dynamic(response=response)

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(self, ids_and_types: Sequence[tuple[str, type[Environment]]]) -> Sequence[str]:
        os_response = await self._os_client.bulk(
            body=[
                {
                    BulkOperation.Delete.value: {
                        "_id": str(doc_id),
                        "_index": self._get_index_from_id_and_type(doc_id=doc_id, domain_type=doc_type),
                    }
                }
                for doc_id, doc_type in ids_and_types
            ]
        )
        if os_response["errors"]:
            logging.error(f"There were errors during deletion of environment. Response: {os_response}")
        return [item["delete"]["_id"] for item in os_response["items"]]

    @staticmethod
    async def _to_domain_from_mget_dynamic(response: dict) -> Sequence[Environment]:
        result: list = []
        for doc in response["docs"]:
            if doc["found"]:
                document_type = OpenSearchIndexToDocumentModelMapping[doc["_index"].split("-")[0]]
                source = doc["_source"]
                model = document_type(**source, id=doc["_id"])
                result.append(model)

        return result

    @staticmethod
    def _get_index_from_env_input_boundary(env_input: EnvironmentInputs) -> str:
        domain_type = TypeResolver.get_environment(type_id=env_input.type_id())
        timestamp = env_input.timestamp
        index_name = DataSchemaToIndexModelMapping[domain_type].name
        doc_year, doc_month = timestamp.year, timestamp.month

        index = f"{index_name}-{doc_year}"
        if settings.SPLIT_INDEX_POSTFIX_FORMAT == "yyyy-MM":
            index += f"-{doc_month}"

        return index

    @staticmethod
    def _get_index_from_id_and_type(doc_id: str, domain_type: type[Environment]) -> str:
        index_name = DataSchemaToIndexModelMapping[domain_type].name
        id_date_part = doc_id.rsplit(".", 1)[-1]
        doc_year, doc_month = id_date_part.split("-")[:2]

        index = f"{index_name}-{doc_year}"
        if settings.SPLIT_INDEX_POSTFIX_FORMAT == "yyyy-MM":
            index += f"-{doc_month}"

        return index
