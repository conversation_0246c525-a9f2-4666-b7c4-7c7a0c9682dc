import asyncio
import logging
from datetime import datetime, timezone
from typing import Optional, Sequence
from uuid import UUID

from opensearchpy import Async<PERSON><PERSON>Search, OpenSearchException
from pydantic import ValidationError

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.sorts import CommonSorts, Sort
from services.base.application.exceptions import (
    IncorrectOperationException,
    RuntimeException,
)
from services.base.application.retry import retry
from services.base.application.utils.size_splitter import SizeSplitter
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.doc_id_refiner import DocIdRefiner
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataSchemaToIndexModelMapping
from services.base.type_resolver import TypeResolver


class OSRecordRepository(RecordRepository):

    def __init__(self, client: AsyncOpenSearch, search_service: DocumentSearchService):
        self._os_client = client
        self._search_service: DocumentSearchService = search_service

    @retry(exceptions=OpenSearchException)
    async def insert(
        self, records: Sequence[TypeResolver.RECORD_UNION], force_strong_consistency: bool = False
    ) -> Sequence[TypeResolver.RECORD_UNION]:
        unique_types = {type(e) for e in records}

        tasks = (self._get_current_index(domain_type=dt) for dt in unique_types)
        type_to_index_map: dict[type[TypeResolver.RECORD_UNION], str] = {
            dt: index for dt, index in await asyncio.gather(*tasks)
        }

        records = DocIdRefiner.refine_doc_ids(docs=records, type_to_index_map=type_to_index_map)
        result: list[TypeResolver.RECORD_UNION] = []
        # split by size
        chunked_docs = SizeSplitter.split(data=records)
        refresh = "wait_for" if force_strong_consistency else "false"

        for chunk in chunked_docs:
            insert_requests: list[dict] = []
            for doc in chunk:
                action = {BulkOperation.Create.value: {"_index": type_to_index_map[type(doc)], "_id": str(doc.id)}}
                insert_requests.append(action)
                insert_requests.append(doc.model_dump(exclude={DocumentLabels.ID}))

                # TODO: what if response contains errors?

            os_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
            if os_response["errors"]:
                logging.error(f"There were errors during insertion of docs. Response: {os_response}")
            records = await self._do_search_by_id(
                ids_and_indices=[
                    (
                        UUID(item["create"]["_id"]),
                        item["create"]["_index"],
                    )
                    for item in os_response["items"]
                ]
            )
            result.extend(records)

        return result

    @retry(exceptions=OpenSearchException)
    async def update(self, records: Sequence[TypeResolver.RECORD_UNION]) -> Sequence[TypeResolver.RECORD_UNION]:
        result: list[TypeResolver.RECORD_UNION] = []
        # split by size
        chunked_docs = SizeSplitter.split(data=records)

        for chunk in chunked_docs:
            update_requests: list[dict] = []
            for doc in chunk:
                doc.system_properties.updated_at = datetime.now(timezone.utc)
                action = {
                    BulkOperation.Update.value: {
                        "_index": self._get_index_from_id_and_type(
                            doc_id=doc.id, domain_type=type(doc)  # pyright: ignore
                        ),
                        "_id": str(doc.id),
                    }
                }
                request = {
                    "doc": {
                        **doc.model_dump(
                            by_alias=True,
                            exclude={
                                DocumentLabels.ID: True,
                                DocumentLabels.RBAC: {DocumentLabels.OWNER_ID},
                                DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT},
                            },
                        )
                    }
                }

                update_requests.append(action)
                update_requests.append(request)

            # TODO: what if response contains errors?
            os_response = await self._os_client.bulk(body=update_requests)
            if os_response["errors"]:
                logging.error(f"There were errors during the update of docs. Response: {os_response}")
            result.extend(
                await self._do_search_by_id(
                    ids_and_indices=[
                        (
                            UUID(item["update"]["_id"]),
                            item["update"]["_index"],
                        )
                        for item in os_response["items"]
                    ]
                )
            )
        return result

    @retry(exceptions=OpenSearchException)
    async def search_by_id(
        self, ids_and_types: Sequence[tuple[UUID, type[TypeResolver.RECORD_UNION]]]
    ) -> Sequence[TypeResolver.RECORD_UNION]:
        return await self._do_search_by_id(
            ids_and_indices=[
                (
                    doc_id,
                    self._get_index_from_id_and_type(doc_id=doc_id, domain_type=doc_type),
                )
                for doc_id, doc_type in ids_and_types
            ]
        )

    async def _do_search_by_id(
        self, ids_and_indices: Sequence[tuple[UUID, str]]
    ) -> Sequence[TypeResolver.RECORD_UNION]:
        response = await self._os_client.mget(
            body={"docs": [{"_id": doc_id, "_index": index} for doc_id, index in ids_and_indices]}
        )
        return await self._to_domain_from_mget_dynamic(response=response)

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(
        self, ids_and_types: Sequence[tuple[UUID, type[TypeResolver.RECORD_UNION]]]
    ) -> Sequence[UUID]:
        os_response = await self._os_client.bulk(
            body=[
                {
                    BulkOperation.Delete.value: {
                        "_id": str(doc_id),
                        "_index": self._get_index_from_id_and_type(doc_id=doc_id, domain_type=doc_type),
                    }
                }
                for doc_id, doc_type in ids_and_types
            ]
        )
        if os_response["errors"]:
            logging.error(f"There were errors during deletion of docs. Response: {os_response}")
        return [UUID(item["delete"]["_id"]) for item in os_response["items"]]

    async def search_by_single_query[T: TypeResolver.RECORD_UNION](
        self,
        size: int,
        query: SingleDocumentTypeQuery[T],
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        sorts = sorts if sorts else CommonSorts.created_at_and_internal_id()
        return await self._search_service.search_documents_by_single_query(
            query=query, size=size, continuation_token=continuation_token, sorts=sorts
        )

    @staticmethod
    def _get_index_from_id_and_type(doc_id: UUID, domain_type: type[TypeResolver.RECORD_UNION]):
        current_index = int(doc_id.hex[0:2], 16)
        return f"{DataSchemaToIndexModelMapping[domain_type].name}-{current_index:06d}"

    @staticmethod
    async def _to_domain_from_mget_dynamic(response: dict) -> Sequence[TypeResolver.RECORD_UNION]:
        result: list = []
        not_found_ids = []
        for doc in response["docs"]:
            if err := doc.get("error"):
                if err["type"] == "index_not_found_exception":
                    not_found_ids.append(doc["_id"])
                    continue
                logging.error(f"Mget search error: {err["reason"]}, id: {doc["_id"]}")
                raise RuntimeException(message=f"Error while searching document {doc["_id"]}")
            if doc["found"]:
                source = doc["_source"]
                try:
                    model = DataType(source["type"]).to_domain_model()(**source, id=doc["_id"])
                    result.append(model)
                except ValidationError as err:
                    logging.error(
                        "failed to deserialize doc",
                        extra={
                            "document_id": doc["_id"],
                            "index": doc["_index"],
                            "source": source,
                            "error": str(err),
                        },
                    )

        if not_found_ids:
            raise IncorrectOperationException(message=f"Documents not found: {not_found_ids}")

        return result

    async def _get_current_index(
        self, domain_type: type[TypeResolver.RECORD_UNION]
    ) -> tuple[type[TypeResolver.RECORD_UNION], str]:
        alias_name = DataSchemaToIndexModelMapping[domain_type].name
        alias_info: dict = await self._os_client.indices.get_alias(name=alias_name)

        sorted_indices = dict(sorted(alias_info.items(), key=lambda i: int(i[0].split(sep="-")[1]), reverse=True))
        for index, alias_data in sorted_indices.items():
            if alias_name in alias_data["aliases"]:
                if alias_data["aliases"][alias_name]["is_write_index"]:
                    return domain_type, index
        raise ShouldNotReachHereException(
            f"No write index found for alias {alias_name}, domain_type: {domain_type.__name__}"
        )
