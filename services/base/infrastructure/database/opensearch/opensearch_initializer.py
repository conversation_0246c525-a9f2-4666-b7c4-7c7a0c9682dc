import asyncio
import logging
from asyncio import TaskGroup
from typing import Sequence

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.infrastructure.database.opensearch.opensearch_index_constants import OpenSearchIndex
from services.base.infrastructure.database.opensearch.opensearch_index_mappings import (
    get_events_index_models,
    get_os_index_models,
    get_records_index_models,
)
from services.base.infrastructure.database.opensearch.opensearch_initializer_v3 import OpenSearchInitializerV3
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    CONTENT_HASH_PIPELINE,
    DOC_ID_CONTENT_HASHING_SCRIPT,
    DOC_ID_ENV_HASHING_SCRIPT,
    DOC_ID_UUID_HASHING_SCRIPT,
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
    SPLIT_INDEX_ENV_HASH_PIPELINE,
    SPLIT_INDEX_PIPELINE,
    SPLIT_INDEX_SEPARATOR,
    SPLIT_INDEX_UUID_HASH_PIPELINE,
    UUID_HASH_PIPELINE,
)
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client
from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL

OS_CONNECTION_TIMEOUT = 60
OS_MAX_CONNECTIONS_PER_NODE = 30


# THIS WAS FAILING TO CREATE RANDOM INDEXES: len(OS_HOSTS) * OS_MAX_CONNECTIONS_PER_NODE


class OpenSearchInitializer:
    _client = get_async_default_os_client()

    @classmethod
    async def initialize(cls, recreate_indices: bool = False) -> None:
        """Legacy indices are not expected to have settings and are created directly.
        Common indices names are expected to be defined as datatype and are instead setup through index templates."""
        if settings.RUN_ENV != RUN_ENV_LOCAL and recreate_indices:
            logging.exception(f"Trying to remove all data from remote {settings.RUN_ENV} cluster.")
            exit(1)

        v3_index_models = [*get_events_index_models(), *get_records_index_models()]
        index_models = get_os_index_models()
        if recreate_indices:
            await OpenSearchUtils.delete_ism(index_models=index_models)
            await OpenSearchUtils.delete_aliases(index_models=index_models)
            await OpenSearchUtils.delete_indices(index_models=index_models)
        await cls._initialize_scripts()
        await cls._initialize_ingest_pipelines()

        logging.info("Initializing standard indices.")
        async with TaskGroup() as grp:
            for im in [im for im in index_models if im not in v3_index_models]:
                grp.create_task(cls.initialize_index_model(index_model=im))

        # Initialize events V3 indices
        await OpenSearchInitializerV3.initialize(index_models=v3_index_models)

        await cls._client.close()

    @classmethod
    async def initialize_index_model(cls, index_model: OpenSearchIndex) -> None:
        await cls._initialize_index_components(index_model)
        await OpenSearchUtils.update_index_mapping(index_model=index_model)

    @classmethod
    async def _initialize_index_components(cls, index_model: OpenSearchIndex) -> None:
        logging.info("Initializing index %s", index_model.name)
        if index_model.is_splittable:
            await cls._initialize_component_template(index_model)
            await cls._initialize_index_template(index_model)
        else:
            await cls._initialize_index(index_model)

    @classmethod
    async def _initialize_ingest_pipelines(cls) -> None:
        logging.info("Initializing ingest pipelines")
        INDEX_SPLITTING_PROCESSOR = {
            "date_index_name": {
                "field": DocumentLabels.TIMESTAMP,
                "index_name_prefix": "{{ _index }}" + SPLIT_INDEX_SEPARATOR,
                "date_rounding": "d",
                "index_name_format": settings.SPLIT_INDEX_POSTFIX_FORMAT,
                "date_formats": ["ISO8601"],
            }
        }
        UUID_DOC_ID_HASH_PROCESSOR = {
            "script": {
                "ignore_failure": False,
                "id": DOC_ID_UUID_HASHING_SCRIPT,
                "params": {
                    "timestamp": DocumentLabels.TIMESTAMP,
                    "uuid": DocumentLabels.USER_UUID,
                    "provider": DocumentLabels.PROVIDER + f".{DocumentLabels.ENTITY}",
                },
            }
        }
        DOC_ID_CONTENT_HASH_PROCESSOR = {
            "script": {
                "ignore_failure": False,
                "id": DOC_ID_CONTENT_HASHING_SCRIPT,
                "params": {
                    "timestamp": DocumentLabels.TIMESTAMP,
                    "metadata": DocumentLabels.METADATA,
                },
            }
        }

        # TODO(jaja): Currently, provider injection is not working
        ENVIRONMENT_DOC_ID_HASH_PROCESSOR = {
            "script": {
                "ignore_failure": False,
                "id": DOC_ID_ENV_HASHING_SCRIPT,
                "params": {
                    "timestamp": DocumentLabels.TIMESTAMP,
                    "coordinates": DocumentLabels.COORDINATES,
                    "provider": f"{DocumentLabels.METADATA}.{DocumentLabels.PROVIDER}",
                },
            }
        }

        await cls._client.ingest.put_pipeline(
            id=SPLIT_INDEX_CONTENT_HASH_PIPELINE,
            body={
                "description": "Ensures all documents are indexed into timestamp postfixed index AND hashes doc id using the whole document body",
                "processors": [INDEX_SPLITTING_PROCESSOR, DOC_ID_CONTENT_HASH_PROCESSOR],
            },
        )

        await cls._client.ingest.put_pipeline(
            id=SPLIT_INDEX_PIPELINE,
            body={
                "description": "Ensures all documents are indexed into timestamp postfixed index.",
                "processors": [INDEX_SPLITTING_PROCESSOR],
            },
        )

        await cls._client.ingest.put_pipeline(
            id=CONTENT_HASH_PIPELINE,
            body={
                "description": "Hashes doc id using the whole document body",
                "processors": [DOC_ID_CONTENT_HASH_PROCESSOR],
            },
        )

        await cls._client.ingest.put_pipeline(
            id=SPLIT_INDEX_UUID_HASH_PIPELINE,
            body={
                "description": "Ensures all documents are indexed into timestamp postfixed index AND hashes doc id based on timestamp, uuid and provider fields.",
                "processors": [INDEX_SPLITTING_PROCESSOR, UUID_DOC_ID_HASH_PROCESSOR],
            },
        )

        await cls._client.ingest.put_pipeline(
            id=UUID_HASH_PIPELINE,
            body={
                "description": "Hashes doc id based on timestamp, uuid and provider fields.",
                "processors": [UUID_DOC_ID_HASH_PROCESSOR],
            },
        )

        await cls._client.ingest.put_pipeline(
            id=SPLIT_INDEX_ENV_HASH_PIPELINE,
            body={
                "description": "Ensures all documents are indexed into timestamp postfixed index AND hashes doc id based on timestamp, coordinates and provider fields.",
                "processors": [INDEX_SPLITTING_PROCESSOR, ENVIRONMENT_DOC_ID_HASH_PROCESSOR],
            },
        )

    @classmethod
    async def _initialize_index_template(cls, index: OpenSearchIndex) -> None:
        index_name = index.name
        logging.info("Initializing index template %s", index_name)
        await cls._client.indices.put_index_template(
            name=f"{index_name}_index_template",
            body={
                "index_patterns": [f"{index_name}*"],
                "composed_of": [f"{index_name}_component_template"],
            },
        )

    @classmethod
    async def _initialize_component_template(cls, index: OpenSearchIndex) -> None:
        index_name = index.name
        logging.info("Initializing component template %s", index_name)
        await cls._client.cluster.put_component_template(
            name=f"{index_name}_component_template",
            body={
                "template": {
                    "settings": index.settings,
                    "mappings": index.mappings,
                }
            },
        )

    @classmethod
    async def _delete_indices(cls, index_models: Sequence[OpenSearchIndex]) -> None:
        logging.info("Deleting existing indices.")
        async with asyncio.TaskGroup() as task_group:
            for index in index_models:
                task_group.create_task(cls._client.indices.delete(f"{index.name}*"))

    @classmethod
    async def _initialize_index(cls, index: OpenSearchIndex):
        if not await cls._client.indices.exists(index=index.name):
            await cls._client.indices.create(
                index=index.name,
                body={
                    "settings": index.settings,
                    "mappings": index.mappings,
                },
            )

    @classmethod
    async def _initialize_scripts(cls):
        logging.info("Initializing custom scripts.")
        await cls._client.put_script(
            id=DOC_ID_UUID_HASHING_SCRIPT,
            body={
                "script": {
                    "lang": "painless",
                    "source": """
                            String getUUID(def str) {
                              def res = null;
                              char[] buffer = str.toCharArray();
                              byte[] b = new byte[buffer.length];
                              for (int i = 0; i < b.length; i++) {
                              b[i] = (byte) buffer[i];
                              } 
                              res = UUID.nameUUIDFromBytes(b).toString();   
                              return res;
                            }
                            ctx._id = getUUID(ctx[params.timestamp] + ctx[params.uuid] + ctx[params.provider]) + "." +
                            ctx[params.timestamp].substring(0, 10);
                        """,
                }
            },
        )
        await cls._client.put_script(
            id=DOC_ID_CONTENT_HASHING_SCRIPT,
            body={
                "script": {
                    "lang": "painless",
                    "source": """
                            String getUUIDFromString(String src) {
                                char[] buffer = src.toCharArray();
                                byte[] b = new byte[buffer.length];
                                for (int i = 0; i < b.length; i++) {
                                    b[i] = (byte) buffer[i];
                                }
                                return UUID.nameUUIDFromBytes(b).toString();
                            }

                            String getSourceHashString(def content) {
                                String result = "";
                                if (content instanceof HashMap) {
                                    for (key in content.keySet()) {
                                        if (
                                            key.startsWith("_") ||
                                            key == "created_at" ||
                                            key == "updated_at" ||
                                            key == "deleted_at"
                                        ) {
                                            continue;
                                        }
                                        result += key + ":" + getSourceHashString(content[key]) + ";";
                                    }
                                }
                                else if (content instanceof ArrayList) {
                                    for (item in content) {
                                        result += getSourceHashString(item) + ";";
                                    }
                                }
                                else {
                                    result += content;
                                }
                                return result;
                            }

                            def metadata = ctx.metadata.clone();
                            
                            if (ctx.containsKey("timestamp")) {
                                ctx._id = getUUIDFromString(getSourceHashString(ctx)) + "." + ctx[params.timestamp].substring(0, 10);
                            }
                            else {
                                ctx._id = getUUIDFromString(getSourceHashString(ctx));
                            }
                            ctx.metadata = metadata;
                        """,
                }
            },
        )
        await cls._client.put_script(
            id=DOC_ID_ENV_HASHING_SCRIPT,
            body={
                "script": {
                    "lang": "painless",
                    "source": """
                            String getUUID(def str) {
                              def res = null;
                              char[] buffer = str.toCharArray();
                              byte[] b = new byte[buffer.length];
                              for (int i = 0; i < b.length; i++) {
                              b[i] = (byte) buffer[i];
                              } 
                              res = UUID.nameUUIDFromBytes(b).toString();   
                              return res;
                            }
                            ctx._id = getUUID(ctx[params.timestamp] + ctx[params.coordinates] + ctx[params.provider]) +
                            "." + ctx[params.timestamp].substring(0, 10);
                        """,
                }
            },
        )


if __name__ == "__main__":
    asyncio.run(OpenSearchInitializer.initialize(recreate_indices=True))
