import asyncio
import importlib.util
import logging
import os
import sys
from os import path
from os.path import isfile
from pathlib import Path
from typing import Callable, Sequence

from typer import Option, Typer, confirm

from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_document_count_async,
)
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client

app = Typer()
RUNNER_PATH = Path(__file__).resolve(strict=True).parent


def is_dry_run() -> bool:
    # Setting DRY_RUN=1 while calling the script will enable dry run (does not save data to the database)
    return os.getenv("DRY_RUN", "0") == "1"


@app.command("list")
def cli_list_scripts():
    scripts_path = path.join(RUNNER_PATH, "scripts")
    files = sorted([file for file in os.listdir(scripts_path) if isfile(path.join(scripts_path, file))])
    print("Existing migration scripts:\n", *files, sep="\n\t")


@app.command("migrate")
def cli_migrate(
    script_file_name: str = Option(..., "--name", "-n"),
    proceed_automatically: bool = Option(False, "--yes_to_all", "-y"),
):
    script_path = path.join(RUNNER_PATH, "scripts", script_file_name)
    spec = importlib.util.spec_from_file_location("migration_script", script_path)
    assert spec, "file not found"
    module = importlib.util.module_from_spec(spec)
    sys.modules["migration_script"] = module
    assert spec.loader, "loader not initialized"
    spec.loader.exec_module(module)

    asyncio.run(
        run_migration(
            data_types=module.TARGET_DATA_TYPES,
            migration_entrypoint=module.run_migration,
            script_name=script_file_name,
            proceed_automatically=proceed_automatically,
        )
    )


@app.command("reindex")
def cli_reindex(
    data_type: DataType = Option(..., "--data_type", "-d"),
    target_prefix: str = Option(None, "--target_prefix", "-t"),
    requests_per_second: int = Option(None, "--requests_per_second", "-rps"),
):
    client = get_async_default_os_client()
    if target_prefix:
        return asyncio.run(
            OSMigrationWrapper.reindex_indices(
                client=client,
                data_type=data_type,
                target_index_prefix=target_prefix,
                requests_per_second=requests_per_second,
            )
        )
    else:
        return asyncio.run(
            OSMigrationWrapper.reindex_data_type(
                client=client, data_type=data_type, requests_per_second=requests_per_second
            )
        )


async def run_migration(
    data_types: Sequence[DataType],
    migration_entrypoint: Callable,
    script_name: str,
    proceed_automatically: bool = False,
):
    """Runs the migration for all uuid indices to convert provider field to metadata field."""
    client = get_async_default_os_client()
    for data_type in data_types:
        # Count before
        original_count = await get_document_count_async(data_type=data_type, client=client)

        prompt = f'Are you sure you want to run a "{script_name}" migration for {data_type.value} (count: {original_count}) ?'

        should_proceed = proceed_automatically or confirm(text=prompt, default=True)
        if not should_proceed:
            continue

        try:
            await migration_entrypoint(data_type=data_type)
        except Exception as error:
            logging.exception(f"Unhandled error encountered when during migration, {error}.")
            should_proceed = proceed_automatically or confirm(text="Do you wish to proceed?")
            if not should_proceed:
                await client.close()
                exit()

        # Count after
        final_count = await get_document_count_async(data_type=data_type, client=client)
        logging.info(f"Document count after the migration: {final_count}, before: {original_count}")

        if final_count == 0:
            logging.info(f"Number of lost documents (# / %) : {original_count} / 100 %.")
        else:
            logging.info(
                f"Number of lost documents (# / %) : {original_count - final_count} / {final_count / original_count * 100}."
            )
    logging.info(f"Migration {script_name} finished.")
    await client.close()


if __name__ == "__main__":
    app()
