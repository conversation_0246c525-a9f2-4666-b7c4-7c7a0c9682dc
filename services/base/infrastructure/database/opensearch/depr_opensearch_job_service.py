import logging

from opensearchpy import As<PERSON><PERSON><PERSON><PERSON>earch, OpenSearchException

from services.base.application.database.depr_job_service import DeprJobService


class DeprOpenSearchJobService(DeprJobService):
    def __init__(self, client: AsyncOpenSearch):
        self._client = client

    async def get_task_detail(self, os_task_id: str) -> dict | None:
        try:
            return await self._client.tasks.get(task_id=os_task_id)
        except OpenSearchException as err:
            logging.exception(err)
            return None
