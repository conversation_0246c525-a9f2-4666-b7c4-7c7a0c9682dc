from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.index_settings.shared_type_indices import SHARED_TYPE_INDICES
from services.base.infrastructure.database.opensearch.opensearch_index_constants import OpenSearchIndex
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataSchemaToIndexModelMapping


class TypeQueryRefiner:
    @staticmethod
    def refine(query: Query) -> Query:
        type_queries: list[TypeQuery] = []
        for q in query.type_queries:
            index_models: list[OpenSearchIndex] = []
            shared_types = []
            non_shared_types = []
            for tp in q.domain_types:
                index_model = DataSchemaToIndexModelMapping[tp]
                index_models.append(index_model)
                if index_model in SHARED_TYPE_INDICES:
                    shared_types.append(tp)
                else:
                    non_shared_types.append(tp)

            if not shared_types:
                type_queries.append(q)
                continue

            # Create separate type queries for shared types
            for tp in shared_types:
                type_value_query = ValuesQuery(
                    field_name="type",
                    values=[tp.type_id()],
                )
                if not q.query:
                    type_query = TypeQuery(domain_types=[tp], query=type_value_query)
                else:
                    and_query = AndQuery(queries=[type_value_query, q.query])
                    type_query = TypeQuery(domain_types=[tp], query=and_query)

                type_queries.append(type_query)

            # Create a new TypeQuery object for the non-shared types
            if non_shared_types:
                non_shared_type_query = TypeQuery(domain_types=non_shared_types, query=q.query)
                type_queries.append(non_shared_type_query)

        return Query(type_queries=type_queries)
