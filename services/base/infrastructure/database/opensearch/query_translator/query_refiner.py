from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.query_translator.type_query_refiner import Type<PERSON><PERSON>y<PERSON>efiner


class QueryRefiner:

    @staticmethod
    def refine(query: Query) -> Query:
        query = TypeQueryRefiner.refine(query=query)

        if len(query.type_queries) == 1:
            return query

        # Combine queries if they are the same but with different data types
        if QueryRefiner._are_all_type_queries_same(query=query):
            domain_types: list[type[Document]] = []
            for type_query in query.type_queries:
                domain_types.extend(type_query.domain_types)

            new_type_query = TypeQuery(domain_types=domain_types, query=query.type_queries[0].query)
            query.type_queries = [new_type_query]
            query = query.model_validate(query)
        return query

    @staticmethod
    def _are_all_type_queries_same(query: Query):
        return all((query.type_queries[0].query == q.query for q in query.type_queries[1:]))
