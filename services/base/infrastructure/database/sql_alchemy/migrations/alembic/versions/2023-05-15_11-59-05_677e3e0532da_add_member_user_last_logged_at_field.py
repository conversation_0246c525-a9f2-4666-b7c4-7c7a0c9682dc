"""add member user last logged at field

Revision ID: 677e3e0532da
Revises: c1df6321ef1f
Create Date: 2023-05-15 11:59:05.504288+00:00

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "677e3e0532da"
down_revision = "c1df6321ef1f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "member_user",
        sa.Column("last_logged_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("member_user", "last_logged_at")
    # ### end Alembic commands ###
