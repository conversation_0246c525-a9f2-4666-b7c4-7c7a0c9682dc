"""member_user_os_tasks_table

Revision ID: 804e17d4c754
Revises: 501df8c9a3b0
Create Date: 2021-12-15 00:43:00.594632+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "804e17d4c754"
down_revision = "501df8c9a3b0"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "member_user_os_tasks",
        sa.Column("user_uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("task_id", sa.Text(), primary_key=True),
        sa.Column("task_type", sa.String(length=100), nullable=False),
        sa.Column("state", sa.String(length=100), nullable=False),
        # timestamp columns
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["user_uuid"], ["member_user.user_uuid"], ondelete="CASCADE"),
    )
    # optimization
    op.create_index(op.f("ix_member_user_os_tasks_user_uuid"), "member_user_os_tasks", ["user_uuid"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_member_user_os_tasks_user_uuid"), table_name="member_user_os_tasks")
    op.drop_table("member_user_os_tasks")
