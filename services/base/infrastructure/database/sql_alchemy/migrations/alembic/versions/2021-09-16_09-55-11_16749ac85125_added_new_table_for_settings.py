"""added new table for settings

Revision ID: 16749ac85125
Revises: 3abe8a22cb0e
Create Date: 2021-09-16 09:55:11.200417+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "16749ac85125"
down_revision = "3abe8a22cb0e"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "user_settings",
        sa.Column("user_uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("general", sa.Text(), nullable=True),
        sa.Column("privacy", sa.Text(), nullable=True),
        sa.Column("profile", sa.Text(), nullable=True),
        sa.Column("favorites", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.<PERSON>umn("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["user_uuid"], ["member_user.user_uuid"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_uuid"),
    )
    op.create_index(op.f("ix_user_settings_user_uuid"), "user_settings", ["user_uuid"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_user_settings_user_uuid"), table_name="user_settings")
    op.drop_table("user_settings")
