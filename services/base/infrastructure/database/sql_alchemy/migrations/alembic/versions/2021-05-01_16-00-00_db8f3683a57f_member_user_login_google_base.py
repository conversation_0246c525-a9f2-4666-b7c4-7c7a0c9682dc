"""member user login google base

Revision ID: db8f3683a57f
Revises: 16394976dfce
Create Date: 2021-05-05 14:40:43.626578+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "db8f3683a57f"
down_revision = "16394976dfce"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(  # pylint:disable=no-member
        "login_google",
        sa.Column("google_id", sa.Text(), nullable=False),
        sa.PrimaryKeyConstraint("google_id"),
        sa.Column("refresh_token", sa.Text(), nullable=True),
        sa.Column("user_data", sa.Text(), server_default="{}", nullable=True),
        sa.Column("user_uuid", UUID(as_uuid=True), nullable=False, unique=False),
        sa.ForeignKeyConstraint(["user_uuid"], ["member_user.user_uuid"], ondelete="CASCADE"),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
    )
    op.create_index(  # pylint:disable=no-member
        # pylint:disable=no-member
        op.f("ix_login_google_user_uuid"),
        "login_google",
        ["user_uuid"],
        unique=False,
    )


# pylint:disable=no-member
def downgrade():
    op.drop_index(op.f("ix_login_google_user_uuid"), table_name="login_google")
    op.drop_table("login_google")
