from logging.config import fileConfig

# from sqlalchemy import pool
from alembic import context

from services.base.infrastructure.database.sql_alchemy.db_state_manager import db_state_manager

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
assert config.config_file_name, "config not properly set"
fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
# --- PROJECT: ---
# Example how to autogenerate some migrations:
# 1) This env must "see" the models to --autogenerate them properly

# 2) Add the base model metadata subclass
from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity  # noqa

# 3) ! ALWAYS CHECK THE OUTPUT ! - it may NOT be complete
#    + IT ALSO ADDS REMOVAL OF EXISTING STUFF IN DB THAT WAS NOT DETECTED!
target_metadata = BaseEntity.metadata
# target_metadata = None

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    context.configure(
        url=db_state_manager.db_url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    with db_state_manager.engine.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
