from uuid import UUID

from sqlalchemy import Foreign<PERSON>ey, Text
from sqlalchemy.orm import Mapped, backref, mapped_column, relationship
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class LoginAppleEntity(BaseEntity):
    __tablename__ = "login_apple"

    apple_id: Mapped[str] = mapped_column(Text(), primary_key=True)
    user_data: Mapped[str] = mapped_column(Text(), server_default="{}", doc="all data extracted from id_token")
    user_uuid: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("member_user.user_uuid", ondelete="CASCADE"),
        index=True,
        nullable=False,
        unique=False,
    )
    member_user = relationship(
        "MemberUserEntity", lazy="joined", backref=backref("login_apple", uselist=False, passive_deletes=True)
    )
