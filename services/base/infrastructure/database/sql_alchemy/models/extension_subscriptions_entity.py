from datetime import datetime
from uuid import UUID

from sqlalchemy import DateTime, Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class ExtensionSubscriptionsEntity(BaseEntity):
    __tablename__ = "extension_subscriptions"

    extension_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("extension_detail.extension_id", ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
        index=True,
    )

    user_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("member_user.user_uuid", ondelete="CASCADE"),
        nullable=False,
        index=True,
        primary_key=True,
    )
    unsubscribed_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
