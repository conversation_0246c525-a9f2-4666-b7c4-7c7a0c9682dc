from datetime import datetime
from uuid import UUID, uuid4

from sqlalchemy import DateTime, String, Text
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class ExtensionProviderEntity(BaseEntity):
    __tablename__ = "extension_provider"

    provider_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        primary_key=True,
        default=uuid4,
        nullable=False,
        index=True,
    )

    domain: Mapped[str] = mapped_column(String(length=256), nullable=False)
    name: Mapped[str] = mapped_column(String(length=128), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    logo_url: Mapped[str] = mapped_column(Text, nullable=True)
    archived_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
