from typing import Callable, Optional, Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail
from services.base.infrastructure.database.sql_alchemy.models.extension_detail_entity import ExtensionDetailEntity
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.repository.sql_repositories_helper import (
    SqlAlchRepositoriesHelper,
)
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchExtensionDetailRepository(ExtensionDetailRepository):
    def __init__(self, session_maker: Callable[..., AsyncSession]):
        self._session_maker = session_maker

    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[ExtensionDetail]:
        return await SqlAlchRepositoriesHelper.get(
            session_maker=self._session_maker,
            wrapper=wrapper,
            domain_type=ExtensionDetail,
            entity_type=ExtensionDetailEntity,
        )

    async def delete(self, extension_details: Sequence[ExtensionDetail]) -> None:
        entities = SqlAlchMapper.to_entity(domain_models=extension_details, entity_type=ExtensionDetailEntity)
        await SqlAlchemyAsyncCommons.delete_from_database(session_maker=self._session_maker, entities=entities)

    async def get_by_extension_id(self, extension_id: UUID) -> Optional[ExtensionDetail]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker, entity_type=ExtensionDetailEntity, primary_keys=[str(extension_id)]
            ),
            domain_type=ExtensionDetail,
        )
        return next(iter(models), None)

    async def upsert(self, extension_details: Sequence[ExtensionDetail]) -> Sequence[ExtensionDetail]:
        entities = SqlAlchMapper.to_entity(domain_models=extension_details, entity_type=ExtensionDetailEntity)
        return SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.merge(session_maker=self._session_maker, entities=entities),
            domain_type=ExtensionDetail,
        )
