from typing import Callable, Optional, Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user_oauth2 import MemberUserOAuth2
from services.base.infrastructure.database.sql_alchemy.models.member_user_oauth2_entity import MemberUserOAuth2Entity
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_mapper import SqlAlchMapper
from services.base.infrastructure.database.sql_alchemy.repository.sql_repositories_helper import (
    SqlAlchRepositoriesHelper,
)
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class SqlAlchMemberUserOAuth2Repository(MemberUserOAuth2Repository):

    def __init__(self, session_maker: Callable[..., AsyncSession]):
        self._session_maker = session_maker

    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUserOAuth2]:
        return await SqlAlchRepositoriesHelper.get(
            session_maker=self._session_maker,
            wrapper=wrapper,
            domain_type=MemberUserOAuth2,
            entity_type=MemberUserOAuth2Entity,
        )

    async def get_by_primary_key(self, user_uuid: UUID, provider: str) -> Optional[MemberUserOAuth2]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker,
                entity_type=MemberUserOAuth2Entity,
                primary_keys=[
                    {
                        MemberUserOAuth2Entity.user_uuid.name: user_uuid,
                        MemberUserOAuth2Entity.provider.name: provider,
                    }
                ],
            ),
            domain_type=MemberUserOAuth2,
        )

        return next(iter(models), None)

    async def insert_or_update(self, oauth2_data: MemberUserOAuth2) -> Optional[MemberUserOAuth2]:
        models = SqlAlchMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.merge(
                session_maker=self._session_maker,
                entities=SqlAlchMapper.to_entity(domain_models=[oauth2_data], entity_type=MemberUserOAuth2Entity),
            ),
            domain_type=MemberUserOAuth2,
        )

        return next(iter(models), None)

    async def delete(self, oauth2_data: MemberUserOAuth2):
        entities = SqlAlchMapper.to_entity(domain_models=[oauth2_data], entity_type=MemberUserOAuth2Entity)
        await SqlAlchemyAsyncCommons.delete_from_database(session_maker=self._session_maker, entities=entities)
