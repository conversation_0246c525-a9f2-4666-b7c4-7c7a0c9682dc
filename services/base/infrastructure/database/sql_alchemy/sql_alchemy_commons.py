# type: ignore
from typing import Any, Dict, Optional, Type, Union

from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import Session
from sqlalchemy.schema import Constraint

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class SqlAlchemyCommons:
    @staticmethod
    def commit_to_database(db_session: Session, db_object: BaseEntity):
        db_session.add(db_object)
        db_session.commit()
        return db_object

    @staticmethod
    def read_database_by_primary_key(db_session: Session, db_class: Type[BaseEntity], primary_key: str):
        record = db_session.query(db_class).get(primary_key)
        return record

    @staticmethod
    def update_in_database(
        db_session: Session, db_class: Type[BaseEntity], primary_key: Dict[str, Any], values: Dict[str, Any]
    ):
        table_object = db_class.__table__
        statement = table_object.update()

        for pk_column in table_object.primary_key.columns:
            statement = statement.where(pk_column == primary_key[pk_column.name])

        statement = statement.values(values)
        db_session.execute(statement)
        db_session.commit()

    @staticmethod
    def merge(db_session: Session, db_object: BaseEntity):
        output = db_session.merge(instance=db_object)
        db_session.commit()
        return output

    @staticmethod
    def insert_or_update(
        db_session: Session,
        db_class: Type[BaseEntity],
        values: dict,
        on_conflict_update: dict,
        constraint: Optional[Union[Constraint, str]] = None,
    ):
        """Inserts&commits [values] of [db_class] into DB, or on conflict(violated [constraint]),
        updates only items in [on_conflict_update]."""
        statement = (
            insert(db_class.__table__)  # currently, only TableClause is documented
            .values(values)
            .on_conflict_do_update(
                constraint=constraint or db_class.__table__.primary_key,
                set_=on_conflict_update,
            )
        )

        db_session.execute(statement)
        db_session.commit()
