import logging

from botocore.exceptions import ClientError
from mypy_boto3_sns import SNSClient, SNSServiceResource
from mypy_boto3_sns.service_resource import Topic

from services.base.application.message_broker_client import MessageBrokerClient
from services.base.application.retry import retry


class SNSWrapper(MessageBrokerClient):

    def __init__(self, sns_service: SNSServiceResource, sns_client: SNSClient):
        self._sns_service = sns_service
        self._sns_client = sns_client

    def publish_topic(self, topic_name: str, message_body: str, message_attributes: dict | None = None):
        """
        Publishes message to all endpoints subscribed to the topic
        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/sns.html#SNS.Client.publish
            :param topic_name: The topic in which the message will be published
            :param message_body: The body text of the message.
            :param message_attributes: Custom attributes of the message with specific format:
        return: The response from SNS that contains the assigned message ID.
        """
        if message_attributes is None:
            message_attributes = {}
        topic = self.create_or_get_topic(topic_name=topic_name)
        try:
            logging.info(
                "Publishing message %s with attributes %s to topic %s", message_body, message_attributes, topic_name
            )
            topic.publish(Message=message_body, MessageAttributes=message_attributes)
        except ClientError as error:
            logging.exception("Publishing message failed: %s", message_body)
            raise error

    @retry()
    def create_or_get_topic(self, topic_name: str) -> Topic:
        logging.info("Creating topic: %s", topic_name)
        return self._sns_service.create_topic(Name=topic_name)

    def subscribe_to_topic(self, topic_name: str, protocol: str, endpoint: str, attributes: dict | None = None):
        if attributes is None:
            attributes = {}
        topic = self.create_or_get_topic(topic_name=topic_name)
        try:
            logging.info(f"Subscribing topic {topic_name} to endpoint {endpoint}.")
            topic.subscribe(Protocol=protocol, Endpoint=endpoint, Attributes=attributes)
        except Exception as error:
            logging.exception("Could not get specified topic: %s", repr(error))

    def get_topic_identifier(self, topic_name: str) -> str:
        topic: Topic = self.create_or_get_topic(topic_name=topic_name)
        return topic.arn
