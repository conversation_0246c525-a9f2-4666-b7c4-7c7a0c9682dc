from uuid import uuid4

from services.base.application.event_models.analytics_scheduled_event_model import AnalyticsScheduledEventModel
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator


class AnalyticsScheduledEventModelBuilder:
    def build(self) -> AnalyticsScheduledEventModel:
        return AnalyticsScheduledEventModel(
            provider_id=uuid4(),
            extension_id=uuid4(),
            timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(),
            user_uuid=uuid4(),
        )
