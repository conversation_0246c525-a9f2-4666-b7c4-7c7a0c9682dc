from abc import ABC, abstractmethod
from typing import Callable, Sequence

from services.base.message_queue.message_wrapper import MessageWrapper


class MessageQueueClient(ABC):
    @abstractmethod
    def consume_messages_from_topics(
        self,
        queue_name: str,
        max_number_of_messages: int,
        polling_time: int,
        message_handler: Callable[[MessageWrapper], None],
        message_attributes: Sequence[str],
    ): ...
    @abstractmethod
    def get_queue_identifier(self, queue_name: str) -> str: ...

    def publish_to_queue(self, queue_name: str, message_body: dict, message_attributes: dict | None = None): ...
