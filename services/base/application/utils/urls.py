from typing import Optional
from urllib import parse


def join_as_url(base_url: str, query_params: Optional[dict], quote_via=parse.quote_plus) -> str:
    """Helper to build URL with query params from dict"""
    if not query_params:
        return base_url

    return "".join(
        [
            base_url,
            "?",
            parse.urlencode(
                {key: value for key, value in query_params.items() if value is not None},
                quote_via=quote_via,
                doseq=True,
            ),
        ]
    )
