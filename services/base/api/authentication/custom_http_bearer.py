from fastapi.security import HTT<PERSON>uthorizationCredentials, <PERSON>TT<PERSON><PERSON>earer
from fastapi.security.utils import get_authorization_scheme_param
from starlette.requests import Request

from services.base.application.exceptions import BadRequestException


class CustomHTTPBearer(HTTPBearer):
    async def __call__(self, request: Request) -> HTTPAuthorizationCredentials | None:
        if "Authorization" not in request.headers:
            return None

        authorization: str | None = request.headers.get("Authorization")
        if not authorization:
            raise BadRequestException("Authorization header was provided, but is empty")

        scheme, credentials = get_authorization_scheme_param(authorization)
        if not scheme or not credentials:
            raise BadRequestException(f'Authorization header has invalid structure: "{authorization}"')

        if not (authorization and scheme and credentials) or scheme.lower() != "bearer":
            raise BadRequestException(f'Authorization token provided with bad value: "{authorization}"')

        return HTTPAuthorizationCredentials(scheme=scheme, credentials=credentials)
