from dataclasses import dataclass

from services.base.application.exceptions import DefaultException


@dataclass
class AuthorizationErrorMessages:
    INVALID_ACCESS_TOKEN = "invalid bearer access token provided"
    MISSING_ACCESS_TOKEN = "no bearer access token provided"
    INVALID_REFRESH_TOKEN = "invalid refresh token provide"
    MISSING_REFRESH_TOKEN = "no refresh token provided"
    EXPIRED_CREDENTIALS = "login has expired"
    LOGIN_AGAIN = "please login again"


class InvalidCredentialsException(DefaultException): ...


class InvalidAccessTokenException(InvalidCredentialsException):
    def __init__(self, message: str = AuthorizationErrorMessages.INVALID_ACCESS_TOKEN):
        super().__init__(message)


class InvalidRefreshTokenException(InvalidCredentialsException):
    def __init__(self, message: str = AuthorizationErrorMessages.INVALID_REFRESH_TOKEN):
        super().__init__(message)


class MissingAccessTokenException(InvalidCredentialsException):
    def __init__(self, message: str = AuthorizationErrorMessages.MISSING_ACCESS_TOKEN):
        super().__init__(message)


class MissingRefreshTokenException(InvalidCredentialsException):
    def __init__(self, message: str = AuthorizationErrorMessages.MISSING_REFRESH_TOKEN):
        super().__init__(message)


class ExpiredCredentialsException(InvalidCredentialsException):
    def __init__(self, message: str = AuthorizationErrorMessages.EXPIRED_CREDENTIALS):
        super().__init__(message)
