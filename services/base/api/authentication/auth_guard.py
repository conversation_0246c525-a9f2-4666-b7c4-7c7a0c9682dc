import logging
from datetime import datetime, timedelta, timezone
from typing import Optional
from uuid import UUID

from fastapi import <PERSON><PERSON>, Depends
from fastapi.security import HTTPAuthorizationCredentials
from jose import JWTError, jwt
from jose.exceptions import ExpiredSignatureError
from starlette.requests import Request
from starlette.responses import Response

from services.base.api.authentication.custom_http_bearer import CustomHTTPBearer
from services.base.api.authentication.exceptions import (
    ExpiredCredentialsException,
    InvalidAccessTokenException,
    InvalidCredentialsException,
    InvalidRefreshTokenException,
    MissingAccessTokenException,
    MissingRefreshTokenException,
)
from services.base.api.authentication.request_handling import get_refresh_token
from services.base.api.authentication.token_handling import (
    generate_access_token,
    get_uuid_from_token,
    rotate_refresh_token,
)
from services.base.application.authorization_encryption import decrypt
from services.base.application.constants import UserTokenKeys
from services.base.application.exceptions import BadRequestException
from services.base.application.jwt_handling import (
    decode_jwt,
)
from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from settings.app_config import settings
from settings.app_secrets import secrets


class AuthGuard:
    def __init__(self):
        self._member_user_repo = bootstrapper.get(interface=MemberUserRepository)

    async def get_tenant_uuid_from_request(
        self,
        response: Response,
        api_refresh_token: Optional[str] = None,
        bearer_token: Optional[str] = None,
    ) -> UUID:
        decoded_token = await self.validate_request_authorization(
            response=response, bearer_token=bearer_token, api_refresh_token=api_refresh_token
        )
        token_uuid = get_uuid_from_token(decoded_token)
        if TelemetryInstrumentor.is_initialized:
            TelemetryInstrumentor.add_telemetry_user(user_id=token_uuid)
        return token_uuid

    async def validate_request_authorization(
        self,
        response: Response,
        api_refresh_token: Optional[str] = None,
        bearer_token: Optional[str] = None,
    ) -> dict[str, str | int]:
        """Validates incoming request authentication credentials"""
        access_token_expired = True
        if not bearer_token:
            raise MissingAccessTokenException()
        try:
            decoded_token = decode_jwt(token=bearer_token, key=secrets.ACCESS_TOKEN_SECRET)
            access_token_expired = False
        except ExpiredSignatureError:
            decoded_token = self.validate_refresh_token(api_refresh_token=api_refresh_token, bearer_token=bearer_token)
        except (JWTError, Exception) as e:
            logging.info(f"Invalid bearer detected, bearer: {bearer_token}, refresh: {api_refresh_token}")
            raise InvalidAccessTokenException() from e

        if access_token_expired:
            user_uuid = get_uuid_from_token(decoded_token)

            _ = await self.validate_and_regenerate_credentials(
                user_uuid=user_uuid,
                expires_at=float(decoded_token[UserTokenKeys.EXPIRATION_TIME]),
                issued_at=int(decoded_token[UserTokenKeys.ISSUED_AT]),
                response=response,
            )

        return decoded_token

    async def validate_and_regenerate_credentials(
        self, response: Response, user_uuid: UUID, issued_at: int, expires_at: float
    ) -> Response:
        user = await self._member_user_repo.get_by_uuid(user_uuid=user_uuid)
        if user is None:
            logging.warning("User was not found while regenerating bearer token.")
            raise InvalidCredentialsException(message=f"User {user_uuid} not found")

        # validate whether the refresh token is newer than last expire
        self.validate_expired_datetime(user=user, token_issued_at_timestamp=issued_at)

        # Update user last logged at field
        await self.update_last_logged_at(user=user)

        # update the response object with new credentials
        return await self.create_authorization_response(
            user=user,
            refresh_expiration_timestamp=expires_at,
            response=response,
        )

    async def create_authorization_response(
        self, user: MemberUser, refresh_expiration_timestamp: float, response: Response
    ) -> Response:
        """Creates new authorization response containing access_token in header and refresh_token in cookie"""
        new_api_refresh_token = rotate_refresh_token(
            user_id=user.user_uuid, refresh_expiration_timestamp=refresh_expiration_timestamp
        )

        new_api_access_token = generate_access_token(
            user_uuid=user.user_uuid, time_delta=timedelta(minutes=settings.API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES)
        )
        response.headers.update({UserTokenKeys.API_ACCESS_TOKEN_HEADER: new_api_access_token})

        if new_api_refresh_token:
            response.set_cookie(
                key=UserTokenKeys.API_REFRESH_TOKEN,
                value=new_api_refresh_token,
                secure=settings.USE_SSL,
                httponly=True,
                samesite="lax",
            )
        return response

    def validate_refresh_token(
        self, api_refresh_token: Optional[str], bearer_token: Optional[str] = None
    ) -> dict[str, str | int]:
        """Validates refresh token and returns decoded representation. Cross validates with bearer token if provided."""

        if not api_refresh_token:
            raise MissingRefreshTokenException()
        try:
            refresh_token = api_refresh_token.replace(f"{UserTokenKeys.API_REFRESH_TOKEN}=", "").split(",")[0]
            decoded_refresh_token = decode_jwt(token=refresh_token, key=secrets.REFRESH_TOKEN_SECRET)
        except ExpiredSignatureError as e:
            raise ExpiredCredentialsException() from e
        except (JWTError, Exception) as e:
            logging.info(f"Invalid refresh detected, bearer: {bearer_token}, refresh: {api_refresh_token}")
            raise InvalidRefreshTokenException() from e
        if bearer_token:
            self.validate_access_and_refresh_match(
                decoded_refresh_token=decoded_refresh_token, bearer_token=bearer_token
            )
        return decoded_refresh_token

    async def update_last_logged_at(self, user: MemberUser):
        user.last_logged_at = datetime.now(timezone.utc)
        return await self._member_user_repo.insert_or_update(user=user)

    def validate_access_and_refresh_match(self, decoded_refresh_token: dict, bearer_token: str) -> None:
        # If bearer token exists, validate whether it belongs to the same user
        refresh_user_uuid = get_uuid_from_token(decoded_token=decoded_refresh_token)
        refresher_iat = decoded_refresh_token[UserTokenKeys.ISSUED_AT]
        bearer_user_uuid = UUID(decrypt(token=jwt.get_unverified_claims(token=bearer_token)[DocumentLabels.USER_UUID]))
        # Ensure bearer is not older than the refresher
        bearer_iat = jwt.get_unverified_claims(token=bearer_token)[UserTokenKeys.ISSUED_AT]
        if (bearer_user_uuid != refresh_user_uuid) or (bearer_iat < refresher_iat):
            logging.warning(
                "Non matching credentials detected",
                extra={"decoded_refresh_token": decoded_refresh_token, "bearer_token": bearer_token},
            )
            raise InvalidCredentialsException("Non matching credentials provided")

    def validate_expired_datetime(self, user: MemberUser, token_issued_at_timestamp: int) -> None:
        if user.expiration_datetime:
            if user.expiration_datetime.timestamp() >= token_issued_at_timestamp:
                raise ExpiredCredentialsException(message="Token was invalidated")


async def get_current_uuid(
    response: Response,
    api_refresh_token: Optional[str] = Cookie(default=None),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(CustomHTTPBearer()),
) -> UUID:
    """Returns current user uuid"""
    return await AuthGuard().get_tenant_uuid_from_request(
        response=response,
        bearer_token=credentials.credentials if credentials else None,
        api_refresh_token=api_refresh_token,
    )


async def get_current_uuid_or_none(request: Request, response: Response) -> Optional[UUID]:
    """Tries to validate request authorization, returns None on failure."""
    refresh_token: str | None = get_refresh_token(request)
    bearer_token: str | None = None

    try:
        credentials = await CustomHTTPBearer().__call__(request)
        bearer_token = credentials.credentials if credentials else None
    except BadRequestException as error:
        # Only raise bad request if we cannot get bearer token while there is no refresh token
        if not refresh_token:
            raise error

    try:
        return await AuthGuard().get_tenant_uuid_from_request(
            response=response,
            api_refresh_token=refresh_token,
            bearer_token=bearer_token,
        )
    except Exception as error:
        logging.info(f"could not validate request authorization: {error}")
        return None
