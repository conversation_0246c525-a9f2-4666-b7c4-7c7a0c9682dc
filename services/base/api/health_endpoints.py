import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi_injector import Injected

from services.base.tasks.worker_orchestrator import WorkerOrchestrator

SERVER_START_TIME = datetime.now(timezone.utc)
WORKER_POOL_NOT_RESPONDING_SINCE = None


def server_run_time() -> dict[str, timedelta]:
    return {"server_run_time": datetime.now(timezone.utc) - SERVER_START_TIME}


async def _inner_worker_orchestrator_async_test_call():
    await asyncio.sleep(0.1)
    return True


def broken_executor(
    worker_orchestrator: WorkerOrchestrator = Injected(WorkerOrchestrator),
) -> dict[str, bool | Optional[datetime]]:
    global WORKER_POOL_NOT_RESPONDING_SINCE
    result = worker_orchestrator.submit_task(_inner_worker_orchestrator_async_test_call).result()
    logging.info(f"health: process pool async test call result: {result}")
    responding = True if worker_orchestrator.submit_task(_inner_worker_orchestrator_async_test_call).result() else False
    if not responding and not WORKER_POOL_NOT_RESPONDING_SINCE:
        WORKER_POOL_NOT_RESPONDING_SINCE = datetime.now(timezone.utc)
    return {
        "broken_executor": worker_orchestrator.is_broken and responding,
        "worker_pool_not_responding_since": WORKER_POOL_NOT_RESPONDING_SINCE,
    }
