def deprecate_router(router):
    """
    Mark all endpoints in the router as deprecated in OpenAPI,
    and add the 'deprecated' tag for UI grouping.
    """
    for route in router.routes:
        if hasattr(route, "deprecated"):
            route.deprecated = True
        # Add 'deprecated' tag for UI grouping
        if hasattr(route, "tags"):
            if route.tags is None:
                route.tags = ["deprecated"]
            elif "deprecated" not in route.tags:
                route.tags.append("deprecated")
        else:
            route.tags = ["deprecated"]
    return router
