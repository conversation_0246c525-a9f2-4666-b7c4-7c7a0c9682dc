from fastapi import FastAP<PERSON>
from fastapi.openapi.utils import get_openapi
from starlette.responses import JSONResponse


def add_stable_openapi(app: FastAPI):
    @app.get("/openapi-stable.json", include_in_schema=False)
    def openapi_stable():
        schema = get_openapi(
            title="Data Service (Stable)",
            version="v3",
            routes=[route for route in app.routes if not getattr(route, "deprecated", False)],
        )
        return JSONResponse(schema)

    from fastapi.openapi.docs import get_swagger_ui_html

    @app.get("/stable", include_in_schema=False)
    async def swagger_stable():
        return get_swagger_ui_html(openapi_url="/openapi-stable.json", title="Stable API Docs")

    from fastapi.openapi.docs import get_redoc_html

    @app.get("/re_stable", include_in_schema=False)
    async def redoc_stable():
        return get_redoc_html(openapi_url="/openapi-stable.json", title="Stable API ReDoc")
