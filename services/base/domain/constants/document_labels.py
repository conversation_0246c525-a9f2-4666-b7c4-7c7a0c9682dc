from dataclasses import dataclass


@dataclass
class DocumentLabels:
    ## Document Identifiers
    DOC_ID = "_doc_id"
    ID = "id"
    UNDERSCORE_ID = "_id"
    CONTENT_HASH = "content_hash"

    ## User and Organization
    USER_UUID = "user_uuid"
    USER_ID = "user_id"
    OWNER_ID = "owner_id"
    ORGANIZATION = "organization"
    ORIGIN = "origin"

    ## Time-related
    TIMESTAMP = "timestamp"
    START_TIME = "start_time"
    END_TIME = "end_time"
    DURATION = "duration"
    UPDATED_AT = "updated_at"
    CREATED_AT = "created_at"
    FAVORITED_AT = "favorited_at"
    DELETED_AT = "deleted_at"
    ARCHIVED_AT = "archived_at"
    TIME_GTE = "time_gte"
    TIME_LTE = "time_lte"

    ## Location and Coordinates
    COORDINATES = "coordinates"
    USER_COORDINATES = "user_coordinates"
    LATITUDE = "lat"
    LONGITUDE = "lon"
    DISTANCE = "distance"

    ## Metadata and Properties
    METADATA = "metadata"
    PROPERTIES = "properties"
    SYSTEM_PROPERTIES = "system_properties"
    VALUE = "value"
    CATEGORY = "category"
    TYPE = "type"
    DATA = "data"
    ENTITY = "entity"

    ## Tags and Classification
    TAGS = "tags"
    TAG = "tag"

    ## Assets
    ASSETS = "assets"
    ASSET_TYPE = "asset_type"
    ASSET_ID = "asset_id"
    ASSET_REFERENCES = "asset_references"

    ## Service and Provider
    SERVICE = "service"
    PROVIDER = "provider"
    BACKFILL = "backfill"

    ## Importance and Urgency
    IMPORTANT = "important"
    URGENT = "urgent"

    ## Query and Results
    RESULTS = "results"
    GTE = "gte"
    LTE = "lte"
    INTERVAL = "interval"
    SORT = "sort"
    SORT_SCORE = "_score"

    ## Groups and Templates
    GROUP_ID = "group_id"
    IS_GROUP = "is_group"
    TEMPLATE_ID = "template_id"
    SUBMISSION_ID = "submission_id"

    ## Miscellaneous
    RBAC = "rbac"
    CATCH_ALL = "catch_all"
    CONFIDENCE = "confidence"
