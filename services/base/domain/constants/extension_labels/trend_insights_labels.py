from dataclasses import dataclass


@dataclass
class TrendInsightsLabels:
    ANALYTIC_TYPE = "analytic_type"
    ANALYTIC_SERIES = "analytic_series"
    TIME_INPUT = "time_input"
    SERIES_ANALYSIS_RESULTS = "series_analysis_results"
    EVALUATION_INPUT = "evaluation_input"
    EVALUATION_OUTPUT = "evaluation_output"
    AGGREGATION_INTERVAL = "aggregation_interval"
    VIABLE_SERIES = "viable_series"
    TREND = "trend"
    STATISTICS = "statistics"
    PERCENTAGE_OF_MISSING_DATA_POINTS = "percentage_of_missing_data_points"
    CONSECUTIVE_TREND = "consecutive_trend"
    DIFFERENCE_FROM_PREVIOUS = "difference_from_previous"
    INDEPENDENT_VARIABLES_MEAN = "independent_variables_mean"
    ABSOLUTE_DIFFERENCE_FROM_PREVIOUS_BUCKET = "absolute_difference_from_previous_bucket"
    ABSOLUTE_DIFFERENCE_FROM_AGGREGATED_BUCKETS = "absolute_difference_from_aggregated_buckets"
    PERCENTAGE_DIFFERENCE_FROM_AGGREGATED_BUCKETS = "percentage_difference_from_aggregated_buckets"
    PERCENTAGE_DIFFERENCE_FROM_PREVIOUS_BUCKET = "percentage_difference_from_previous_bucket"
    PERCENTAGE_DIFFERENCE = "percentage_difference"
    MAX = "max"
    MIN = "min"
    MEAN = "mean"
    STD = "std"
    QUARTILE_UPPER = "quartile_upper"
    QUARTILE_LOWER = "quartile_lower"
    SUM = "sum"
    RESULTS = "results"
    RESULT_STATUS = "result_status"
    MESSAGE = "message"
    SUMMARY = "summary"
