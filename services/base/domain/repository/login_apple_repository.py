from abc import ABC, abstractmethod
from typing import Optional, Sequence

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.login_apple import LoginApple


class LoginAppleRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[LoginApple]:
        pass

    @abstractmethod
    async def get_by_apple_id(self, apple_id: str) -> Optional[LoginApple]:
        pass

    @abstractmethod
    async def insert_or_update(self, login_apple: LoginApple) -> Optional[LoginApple]:
        pass

    @abstractmethod
    async def delete(self, login_apple: LoginApple) -> None:
        pass
