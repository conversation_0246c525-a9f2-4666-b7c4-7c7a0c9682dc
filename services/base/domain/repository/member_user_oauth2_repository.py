from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user_oauth2 import MemberUserOAuth2


class MemberUserOAuth2Repository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUserOAuth2]:
        pass

    @abstractmethod
    async def get_by_primary_key(self, user_uuid: UUID, provider: str) -> Optional[MemberUserOAuth2]:
        pass

    @abstractmethod
    async def insert_or_update(self, oauth2_data: MemberUserOAuth2) -> Optional[MemberUserOAuth2]:
        pass

    @abstractmethod
    async def delete(self, oauth2_data: MemberUserOAuth2):
        pass
