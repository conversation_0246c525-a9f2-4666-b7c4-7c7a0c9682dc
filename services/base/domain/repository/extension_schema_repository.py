from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.domain.schemas.extensions.extension_schema import ExtensionSchema


class ExtensionSchemaRepository(ABC):
    @abstractmethod
    async def get_by_extension_and_version(self, extension_id: UUID, version: str) -> Optional[ExtensionSchema]:
        pass

    @abstractmethod
    async def list_extension_versions(self, extension_id: UUID) -> Sequence[ExtensionSchema]:
        pass

    @abstractmethod
    async def upsert(self, schemas: Sequence[ExtensionSchema]) -> Sequence[ExtensionSchema]:
        pass
