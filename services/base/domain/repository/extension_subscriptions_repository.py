from abc import ABC, abstractmethod
from typing import Sequence
from uuid import U<PERSON><PERSON>

from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_subscriptions import ExtensionSubscriptions


class ExtensionSubscriptionsRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[ExtensionSubscriptions]:
        pass

    @abstractmethod
    async def get_user_subscribed_extension(self, extension_id: UUID, user_id: UUID) -> ExtensionSubscriptions | None:
        pass

    @abstractmethod
    async def list_extensions_user_is_subscribed_to(self, user_id: UUID) -> Sequence[ExtensionSubscriptions]:
        pass

    @abstractmethod
    async def list_users_subscribed_to_extension(self, extension_id: UUID) -> Sequence[ExtensionSubscriptions]:
        pass

    @abstractmethod
    async def upsert(
        self, extension_subscriptions: Sequence[ExtensionSubscriptions]
    ) -> Sequence[ExtensionSubscriptions]:
        pass
