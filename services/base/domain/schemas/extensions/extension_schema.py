from uuid import UUID

from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.value_limits.extension_schema import ExtensionSchemaValueLimit


class ExtensionSchema(BaseDataModel):
    extension_id: UUID = Field(description="Immutable extension identifier")
    version: str = Field(
        min_length=1,
        max_length=ExtensionSchemaValueLimit.VERSION_MAX_LENGTH,
        description="Extension versioning major.minor.maintenance.",
        pattern=r"^\d+\.\d+\.\d+$",
    )
    input_schema: dict
    output_schema: dict
