from dataclasses import dataclass
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.application import Application
from services.base.domain.enums.metadata import DataIntegrity, DataProxy, DataQuality, Organization, Service
from services.base.domain.schemas.shared import BaseDataModel, IdentifiableModel


@dataclass(frozen=True)
class MetadataFields:
    ORGANIZATION = "organization"
    SERVICE = "service"
    DATA_INTEGRITY = "data_integrity"
    DATA_QUALITY = "data_quality"
    DATA_PROXY = "data_proxy"
    SYNC_DEVICE = "sync_device"
    SYNC_SOFTWARE = "sync_software"
    SENSOR = "sensor"
    TAGS = DocumentLabels.TAGS
    LABEL_EXPLANATION = "explanation"
    IMPORTANT = "important"
    URGENT = "urgent"
    FAVORITED_AT = "favorited_at"


class Metadata(BaseDataModel):
    user_uuid: UUID = Field(alias=DocumentLabels.USER_UUID)
    organization: Organization = Field(alias=MetadataFields.ORGANIZATION)
    data_integrity: DataIntegrity = Field(alias=MetadataFields.DATA_INTEGRITY)
    important: bool = Field(alias=MetadataFields.IMPORTANT, default=False)
    urgent: bool = Field(alias=MetadataFields.URGENT, default=False)
    service: Service | NonEmptyStr | None = Field(alias=MetadataFields.SERVICE, default=None)
    data_quality: DataQuality | None = Field(alias=MetadataFields.DATA_QUALITY, default=None)
    sync_software: Application | None = Field(alias=MetadataFields.SYNC_SOFTWARE, default=None)
    sync_device: NonEmptyStr | None = Field(alias=MetadataFields.SYNC_DEVICE, default=None)
    sensor: NonEmptyStr | None = Field(alias=MetadataFields.SENSOR, default=None)
    data_proxy: DataProxy | NonEmptyStr | None = Field(alias=MetadataFields.DATA_PROXY, default=None)
    favorited_at: SerializableAwareDatetime | None = Field(alias=MetadataFields.FAVORITED_AT, default=None)


class MetadataModel(BaseDataModel):
    metadata: Metadata = Field(alias=DocumentLabels.METADATA)


class DeprIdentifiableMetadataModel(MetadataModel, IdentifiableModel): ...
