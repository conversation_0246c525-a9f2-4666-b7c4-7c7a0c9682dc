from dataclasses import dataclass

from pydantic import Field

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class NutrientsFields:
    FAT: str = "fat"
    SATURATED_FAT: str = "saturated_fat"
    POLYUNSATURATED_FAT: str = "polyunsaturated_fat"
    MONOUNSATURATED_FAT: str = "monounsaturated_fat"
    TRANS_FAT: str = "trans_fat"
    CHOLESTEROL: str = "cholesterol"
    CARBOHYDRATES: str = "carbohydrates"
    FIBER: str = "fiber"
    SUGAR: str = "sugar"
    PROTEIN: str = "protein"

    SODIUM: str = "sodium"
    POTASSIUM: str = "potassium"
    VITAMIN_A: str = "vitamin_a"
    VITAMIN_C: str = "vitamin_c"
    IRON: str = "iron"
    CALCIUM: str = "calcium"

    BIOTIN: str = "biotin"
    CAFFEINE: str = "caffeine"
    CHLORIDE: str = "chloride"
    CHROMIUM: str = "chromium"
    COPPER: str = "copper"
    FOLATE: str = "folate"
    IODINE: str = "iodine"
    MAGNESIUM: str = "magnesium"
    MANGANESE: str = "manganese"
    MOLYBDENUM: str = "molybdenum"
    NIACIN: str = "niacin"
    PANTOTHENIC_ACID: str = "pantothenic_acid"
    PHOSPHORUS: str = "phosphorus"
    RIBOFLAVIN: str = "riboflavin"
    SELENIUM: str = "selenium"
    THIAMIN: str = "thiamin"
    VITAMIN_B6: str = "vitamin_b6"
    VITAMIN_B12: str = "vitamin_b12"
    VITAMIN_D: str = "vitamin_d"
    VITAMIN_E: str = "vitamin_e"
    VITAMIN_K: str = "vitamin_k"
    ZINC: str = "zinc"


@dataclass(frozen=True)
class NutrientsValueLimits:
    MAX_FAT = 10000
    MIN_FAT = 0
    MAX_SATURATED_FAT = 10000
    MIN_SATURATED_FAT = 0
    MAX_POLYUNSATURATED_FAT = 10000
    MIN_POLYUNSATURATED_FAT = 0
    MAX_MONOUNSATURATED_FAT = 10000
    MIN_MONOUNSATURATED_FAT = 0
    MAX_TRANS_FAT = 10000
    MIN_TRANS_FAT = 0
    MAX_CHOLESTEROL = 10000
    MIN_CHOLESTEROL = 0
    MAX_CARBOHYDRATES = 10000
    MIN_CARBOHYDRATES = 0
    MAX_FIBER = 10000
    MIN_FIBER = 0
    MAX_SUGAR = 10000
    MIN_SUGAR = 0
    MAX_PROTEIN = 10000
    MIN_PROTEIN = 0

    MIN_SODIUM = 0
    MAX_SODIUM = 10000
    MIN_POTASSIUM = 0
    MAX_POTASSIUM = 10000
    MIN_VITAMIN_A = 0
    MAX_VITAMIN_A = 10000
    MIN_VITAMIN_C = 0
    MAX_VITAMIN_C = 10000
    MIN_IRON = 0
    MAX_IRON = 10000
    MIN_CALCIUM = 0
    MAX_CALCIUM = 10000

    MIN_BIOTIN = 0
    MAX_BIOTIN = 10000
    MIN_CAFFEINE = 0
    MAX_CAFFEINE = 10000
    MIN_CHLORIDE = 0
    MAX_CHLORIDE = 10000
    MIN_CHROMIUM = 0
    MAX_CHROMIUM = 10000
    MIN_COPPER = 0
    MAX_COPPER = 10000
    MIN_FOLATE = 0
    MAX_FOLATE = 10000
    MIN_IODINE = 0
    MAX_IODINE = 10000
    MIN_MAGNESIUM = 0
    MAX_MAGNESIUM = 10000
    MIN_MANGANESE = 0
    MAX_MANGANESE = 10000
    MIN_MOLYBDENUM = 0
    MAX_MOLYBDENUM = 10000
    MIN_NIACIN = 0
    MAX_NIACIN = 10000
    MIN_PANTOTHENIC_ACID = 0
    MAX_PANTOTHENIC_ACID = 10000
    MIN_PHOSPHORUS = 0
    MAX_PHOSPHORUS = 10000
    MIN_RIBOFLAVIN = 0
    MAX_RIBOFLAVIN = 10000
    MIN_SELENIUM = 0
    MAX_SELENIUM = 10000
    MIN_THIAMIN = 0
    MAX_THIAMIN = 10000
    MIN_VITAMIN_B6 = 0
    MAX_VITAMIN_B6 = 10000
    MIN_VITAMIN_B12 = 0
    MAX_VITAMIN_B12 = 10000
    MIN_VITAMIN_D = 0
    MAX_VITAMIN_D = 10000
    MIN_VITAMIN_E = 0
    MAX_VITAMIN_E = 10000
    MIN_VITAMIN_K = 0
    MAX_VITAMIN_K = 10000
    MIN_ZINC = 0
    MAX_ZINC = 10000


class Nutrients(BaseDataModel):
    fat: Rounded6Float | None = Field(
        alias=NutrientsFields.FAT,
        ge=NutrientsValueLimits.MIN_FAT,
        le=NutrientsValueLimits.MAX_FAT,
    )
    saturated_fat: Rounded6Float | None = Field(
        alias=NutrientsFields.SATURATED_FAT,
        ge=NutrientsValueLimits.MIN_SATURATED_FAT,
        le=NutrientsValueLimits.MAX_SATURATED_FAT,
    )
    polyunsaturated_fat: Rounded6Float | None = Field(
        alias=NutrientsFields.POLYUNSATURATED_FAT,
        ge=NutrientsValueLimits.MIN_POLYUNSATURATED_FAT,
        le=NutrientsValueLimits.MAX_POLYUNSATURATED_FAT,
    )
    monounsaturated_fat: Rounded6Float | None = Field(
        alias=NutrientsFields.MONOUNSATURATED_FAT,
        ge=NutrientsValueLimits.MIN_MONOUNSATURATED_FAT,
        le=NutrientsValueLimits.MAX_MONOUNSATURATED_FAT,
    )

    trans_fat: Rounded6Float | None = Field(
        alias=NutrientsFields.TRANS_FAT,
        ge=NutrientsValueLimits.MIN_TRANS_FAT,
        le=NutrientsValueLimits.MAX_TRANS_FAT,
    )
    cholesterol: Rounded6Float | None = Field(
        alias=NutrientsFields.CHOLESTEROL,
        ge=NutrientsValueLimits.MIN_CHOLESTEROL,
        le=NutrientsValueLimits.MAX_CHOLESTEROL,
    )

    carbohydrates: Rounded6Float | None = Field(
        alias=NutrientsFields.CARBOHYDRATES,
        ge=NutrientsValueLimits.MIN_CARBOHYDRATES,
        le=NutrientsValueLimits.MAX_CARBOHYDRATES,
    )
    fiber: Rounded6Float | None = Field(
        alias=NutrientsFields.FIBER,
        ge=NutrientsValueLimits.MIN_FIBER,
        le=NutrientsValueLimits.MAX_FIBER,
    )
    sugar: Rounded6Float | None = Field(
        alias=NutrientsFields.SUGAR,
        ge=NutrientsValueLimits.MIN_SUGAR,
        le=NutrientsValueLimits.MAX_SUGAR,
    )
    protein: Rounded6Float | None = Field(
        alias=NutrientsFields.PROTEIN,
        ge=NutrientsValueLimits.MIN_PROTEIN,
        le=NutrientsValueLimits.MAX_PROTEIN,
    )

    sodium: Rounded6Float | None = Field(
        alias=NutrientsFields.SODIUM,
        ge=NutrientsValueLimits.MIN_SODIUM,
        le=NutrientsValueLimits.MAX_SODIUM,
    )
    potassium: Rounded6Float | None = Field(
        alias=NutrientsFields.POTASSIUM,
        ge=NutrientsValueLimits.MIN_POTASSIUM,
        le=NutrientsValueLimits.MAX_POTASSIUM,
    )
    vitamin_a: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_A,
        ge=NutrientsValueLimits.MIN_VITAMIN_A,
        le=NutrientsValueLimits.MAX_VITAMIN_A,
    )
    vitamin_c: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_C,
        ge=NutrientsValueLimits.MIN_VITAMIN_C,
        le=NutrientsValueLimits.MAX_VITAMIN_C,
    )
    iron: Rounded6Float | None = Field(
        alias=NutrientsFields.IRON,
        ge=NutrientsValueLimits.MIN_IRON,
        le=NutrientsValueLimits.MAX_IRON,
    )
    calcium: Rounded6Float | None = Field(
        alias=NutrientsFields.CALCIUM,
        ge=NutrientsValueLimits.MIN_CALCIUM,
        le=NutrientsValueLimits.MAX_CALCIUM,
    )
    biotin: Rounded6Float | None = Field(
        alias=NutrientsFields.BIOTIN,
        ge=NutrientsValueLimits.MIN_BIOTIN,
        le=NutrientsValueLimits.MAX_BIOTIN,
    )
    caffeine: Rounded6Float | None = Field(
        alias=NutrientsFields.CAFFEINE,
        ge=NutrientsValueLimits.MIN_CAFFEINE,
        le=NutrientsValueLimits.MAX_CAFFEINE,
    )
    chloride: Rounded6Float | None = Field(
        alias=NutrientsFields.CHLORIDE,
        ge=NutrientsValueLimits.MIN_CHLORIDE,
        le=NutrientsValueLimits.MAX_CHLORIDE,
    )
    chromium: Rounded6Float | None = Field(
        alias=NutrientsFields.CHROMIUM,
        ge=NutrientsValueLimits.MIN_CHROMIUM,
        le=NutrientsValueLimits.MAX_CHROMIUM,
    )
    copper: Rounded6Float | None = Field(
        alias=NutrientsFields.COPPER,
        ge=NutrientsValueLimits.MIN_COPPER,
        le=NutrientsValueLimits.MAX_COPPER,
    )
    folate: Rounded6Float | None = Field(
        alias=NutrientsFields.FOLATE,
        ge=NutrientsValueLimits.MIN_FOLATE,
        le=NutrientsValueLimits.MAX_FOLATE,
    )
    iodine: Rounded6Float | None = Field(
        alias=NutrientsFields.IODINE,
        ge=NutrientsValueLimits.MIN_IODINE,
        le=NutrientsValueLimits.MAX_IODINE,
    )
    magnesium: Rounded6Float | None = Field(
        alias=NutrientsFields.MAGNESIUM,
        ge=NutrientsValueLimits.MIN_MAGNESIUM,
        le=NutrientsValueLimits.MAX_MAGNESIUM,
    )
    manganese: Rounded6Float | None = Field(
        alias=NutrientsFields.MANGANESE,
        ge=NutrientsValueLimits.MIN_MANGANESE,
        le=NutrientsValueLimits.MAX_MANGANESE,
    )
    molybdenum: Rounded6Float | None = Field(
        alias=NutrientsFields.MOLYBDENUM,
        ge=NutrientsValueLimits.MIN_MOLYBDENUM,
        le=NutrientsValueLimits.MAX_MOLYBDENUM,
    )
    niacin: Rounded6Float | None = Field(
        alias=NutrientsFields.NIACIN,
        ge=NutrientsValueLimits.MIN_NIACIN,
        le=NutrientsValueLimits.MAX_NIACIN,
    )
    pantothenic_acid: Rounded6Float | None = Field(
        alias=NutrientsFields.PANTOTHENIC_ACID,
        ge=NutrientsValueLimits.MIN_PANTOTHENIC_ACID,
        le=NutrientsValueLimits.MAX_PANTOTHENIC_ACID,
    )
    phosphorus: Rounded6Float | None = Field(
        alias=NutrientsFields.PHOSPHORUS,
        ge=NutrientsValueLimits.MIN_PHOSPHORUS,
        le=NutrientsValueLimits.MAX_PHOSPHORUS,
    )
    riboflavin: Rounded6Float | None = Field(
        alias=NutrientsFields.RIBOFLAVIN,
        ge=NutrientsValueLimits.MIN_RIBOFLAVIN,
        le=NutrientsValueLimits.MAX_RIBOFLAVIN,
    )
    selenium: Rounded6Float | None = Field(
        alias=NutrientsFields.SELENIUM,
        ge=NutrientsValueLimits.MIN_SELENIUM,
        le=NutrientsValueLimits.MAX_SELENIUM,
    )
    thiamin: Rounded6Float | None = Field(
        alias=NutrientsFields.THIAMIN,
        ge=NutrientsValueLimits.MIN_THIAMIN,
        le=NutrientsValueLimits.MAX_THIAMIN,
    )
    vitamin_b6: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_B6,
        ge=NutrientsValueLimits.MIN_VITAMIN_B6,
        le=NutrientsValueLimits.MAX_VITAMIN_B6,
    )
    vitamin_b12: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_B12,
        ge=NutrientsValueLimits.MIN_VITAMIN_B12,
        le=NutrientsValueLimits.MAX_VITAMIN_B12,
    )
    vitamin_d: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_D,
        ge=NutrientsValueLimits.MIN_VITAMIN_D,
        le=NutrientsValueLimits.MAX_VITAMIN_D,
    )
    vitamin_e: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_E,
        ge=NutrientsValueLimits.MIN_VITAMIN_E,
        le=NutrientsValueLimits.MAX_VITAMIN_E,
    )
    vitamin_k: Rounded6Float | None = Field(
        alias=NutrientsFields.VITAMIN_K,
        ge=NutrientsValueLimits.MIN_VITAMIN_K,
        le=NutrientsValueLimits.MAX_VITAMIN_K,
    )
    zinc: Rounded6Float | None = Field(
        alias=NutrientsFields.ZINC,
        ge=NutrientsValueLimits.MIN_ZINC,
        le=NutrientsValueLimits.MAX_ZINC,
    )
