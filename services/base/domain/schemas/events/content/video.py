from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content_collection import ContentCollection, ContentFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class VideoCategory(StrEnum):
    VIDEO = DataType.Video
    MOVIE = "movie"
    TV_SHOW = "tv_show"
    LIVESTREAM = "livestream"
    PODCAST = "podcast"
    MUSIC_VIDEO = "music_video"


class VideoIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> str:
        return DataType.Video


class Video(ContentCollection, Event, VideoIdentifier):
    type: Literal[DataType.Video] = Field(alias=ContentFields.TYPE)
    category: VideoCategory = Field(alias=ContentFields.CATEGORY, default=VideoCategory.VIDEO)
