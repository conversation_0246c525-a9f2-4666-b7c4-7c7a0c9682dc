from abc import ABC
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Optional
from uuid import UUID

from pydantic import Field, computed_field, model_validator

from services.base.domain.annotated_types import (
    AssetId,
    NonEmptyStr,
    SerializableAwareDatetime,
    UniqueSequenceStr,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.enums.assets_enums import AssetType
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.metadata import MetadataFields
from services.base.domain.schemas.shared import BaseDataModel


@dataclass
class DocumentValueLimits:
    MaxTagsCount = 64


class Document(BaseDataModel, TypeIdentifier, ABC):
    id: UUID = Field(alias=DocumentLabels.ID)


@dataclass(frozen=True)
class EventMetadataLimits:
    ORIGIN_DEVICE_LENGTH = 64
    SOURCE_DEVICE_LENGTH = 64


@dataclass(frozen=True)
class EventMetadataFields:
    ORIGIN = "origin"
    ORIGIN_DEVICE = "origin_device"
    SOURCE_OS = "source_os"
    SOURCE_SERVICE = "source_service"
    SERVICE = "service"


class DocumentMetadata(BaseDataModel):
    origin: Origin = Field(alias=EventMetadataFields.ORIGIN)
    origin_device: NonEmptyStr | None = Field(
        max_length=EventMetadataLimits.ORIGIN_DEVICE_LENGTH, alias=EventMetadataFields.ORIGIN_DEVICE
    )
    source_os: SourceOS = Field(alias=EventMetadataFields.SOURCE_OS)
    source_service: SourceService = Field(alias=EventMetadataFields.SOURCE_SERVICE)
    service: Service = Field(alias=EventMetadataFields.SERVICE)


class EventMetadata(DocumentMetadata):
    ## TODO REMOVE when all events v2 are gone. Now kept because adjusting every queries based on it is tremendous amount of work
    organization: Organization = Field(alias=MetadataFields.ORGANIZATION)


class TimestampDocument(BaseDataModel):
    timestamp: SerializableAwareDatetime = Field(alias=DocumentLabels.TIMESTAMP)


class TimeIntervalDocument(TimestampDocument):
    end_time: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.END_TIME)

    @model_validator(mode="after")
    def validate_time_interval_range(self):
        if self.end_time:
            if self.timestamp > self.end_time:
                raise ValueError("timestamp has to be less than end_time")
        return self

    @computed_field(alias=DocumentLabels.DURATION)
    @property
    def duration(self) -> float | None:
        if not self.end_time:
            return 0
        secs = (self.end_time - self.timestamp).total_seconds()
        if secs < 0:
            raise ValueError("duration cannot be negative")
        if secs > SECONDS_IN_365_DAYS:
            raise ValueError("maximum duration equals one year")
        return secs


class TimeIntervalDocumentStrict(TimestampDocument):
    end_time: SerializableAwareDatetime = Field(alias=DocumentLabels.END_TIME)

    @model_validator(mode="after")
    def validate_time_interval_range(self):
        if self.timestamp > self.end_time:
            raise ValueError("timestamp has to be less than end_time")
        return self

    @computed_field(alias=DocumentLabels.DURATION)
    @property
    def duration(self) -> float | None:
        secs = (self.end_time - self.timestamp).total_seconds()
        if secs < 0:
            raise ValueError("duration cannot be negative")
        if secs > SECONDS_IN_365_DAYS:
            raise ValueError("maximum duration equals one year")
        return secs


class RBACSchema(BaseDataModel):
    owner_id: UUID = Field(alias=DocumentLabels.OWNER_ID)


class RBACDocument(BaseDataModel):
    rbac: RBACSchema = Field(alias=DocumentLabels.RBAC)


class SystemPropertiesSchema(BaseDataModel):
    created_at: SerializableAwareDatetime = Field(
        alias=DocumentLabels.CREATED_AT, default_factory=lambda: datetime.now(timezone.utc)
    )
    updated_at: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.UPDATED_AT, default=None)
    deleted_at: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.DELETED_AT, default=None)


class SystemPropertiesDocument(BaseDataModel):
    system_properties: SystemPropertiesSchema = Field(
        alias=DocumentLabels.SYSTEM_PROPERTIES, default_factory=SystemPropertiesSchema
    )


class TagsDocument(BaseDataModel):
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)


class AssetReference(BaseDataModel):
    asset_type: AssetType = Field(alias=DocumentLabels.ASSET_TYPE)
    asset_id: AssetId = Field(DocumentLabels.ASSET_ID)
