from typing import Sequence
from uuid import UUID

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.base.domain.schemas.templates.template import Template


class CommonQueryAdjustments:
    @staticmethod
    def add_user_uuid_to_query(user_uuid: UUID, query: Query) -> Query:
        type_queries = []
        for type_query in query.type_queries:
            v3_documents, v2_documents = CommonQueryAdjustments._split_domain_types_based_on_parent(
                domain_types=type_query.domain_types
            )

            if v3_documents:
                bool_builder = BooleanQueryBuilder()
                bool_builder.add_query(type_query.query)
                bool_builder.add_query(CommonLeafQueries.owner_id_value_query(user_uuid))
                type_queries.append(TypeQuery(query=bool_builder.build_and_query(), domain_types=v3_documents))

            if v2_documents:
                bool_builder = BooleanQueryBuilder()
                bool_builder.add_query(type_query.query)
                bool_builder.add_query(CommonLeafQueries.metadata_user_uuid_value_query(user_uuid))
                type_queries.append(TypeQuery(query=bool_builder.build_and_query(), domain_types=v2_documents))

        return Query(type_queries=type_queries)

    @staticmethod
    def _split_domain_types_based_on_parent(
        domain_types: Sequence[type[Document]],
    ) -> tuple[Sequence[type[Document]], Sequence[type[Document]]]:
        v3_documents = []
        v2_documents = []

        for domain_type in domain_types:
            if issubclass(domain_type, Event) or issubclass(domain_type, Template):
                v3_documents.append(domain_type)
            elif issubclass(domain_type, DeprEventModel):
                v2_documents.append(domain_type)
            else:
                raise ShouldNotReachHereException(f"Unexpected domain type: {domain_type}")
        return v3_documents, v2_documents
