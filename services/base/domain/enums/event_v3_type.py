from enum import StrEnum

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event
from services.base.type_resolver import TypeResolver


class EventV3Type(StrEnum):
    # Content
    Audio = DataType.Audio
    Content = DataType.Content
    Interactive = DataType.Interactive
    Image = DataType.Image
    Text = DataType.Text
    Video = DataType.Video

    # Body Metric
    BloodGlucose = DataType.BloodGlucose
    BloodPressure = DataType.BloodPressure
    BodyMetric = DataType.BodyMetric

    # Feeling
    Emotion = DataType.Emotion
    Stress = DataType.Stress

    # Exercise
    Exercise = DataType.Exercise
    Cardio = DataType.Cardio
    Strength = DataType.Strength

    # Nutrition
    Drink = DataType.Drink
    Food = DataType.Food
    Supplement = DataType.Supplement

    # Other
    CoreEvent = DataType.CoreEvent
    Note = DataType.Note
    Symptom = DataType.Symptom
    EventGroup = DataType.EventGroup
    Medication = DataType.Medication
    Sleep = DataType.SleepV3

    def to_event_model(self) -> type[Event]:
        return TypeResolver.get_event(type_id=self)
