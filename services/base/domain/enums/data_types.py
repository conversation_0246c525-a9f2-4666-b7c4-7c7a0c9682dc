from __future__ import annotations

from enum import StrEnum
from typing import Type

from services.base.domain.schemas.events.document_base import Document


class DataType(StrEnum):
    # Content
    Audio = "audio"
    Content = "content"
    Interactive = "interactive"
    Image = "image"
    Text = "text"
    Video = "video"
    # Feeling
    Stress = "stress"
    Emotion = "emotion"
    # Exercise
    Exercise = "exercise"
    Cardio = "cardio"
    Strength = "strength"
    # Body Metric
    BloodGlucose = "blood_glucose"
    BloodPressure = "blood_pressure"
    BodyMetric = "body_metric"
    # Medication
    Medication = "medication"
    SleepV3 = "sleep"

    CoreEvent = "core_event"
    Note = "note"
    Symptom = "symptom"
    EventGroup = "event_group"

    # Nutrition
    Drink = "drink"
    Food = "food"
    Supplement = "supplement"

    # Records
    SleepRecord = "sleep_record"
    # Documents
    EventTemplate = "event_template"
    GroupTemplate = "group_template"
    Plan = "plan"
    UseCase = "use_case"
    # V2
    DiaryEvents = "DiaryEvents"
    HeartRate = "HeartRate"
    Location = "Location"
    RestingHeartRate = "RestingHeartRate"
    ShoppingActivity = "ShoppingActivity"
    Sleep = "Sleep"
    Steps = "Steps"
    # Environment
    AirQuality = "AirQuality"
    Weather = "Weather"
    Pollen = "Pollen"
    # Other
    ExtensionRun = "ExtensionRun"
    ExtensionResult = "ExtensionResult"
    InboxMessage = "InboxMessage"
    UserLogs = "UserLogs"
    UsageStatistics = "UsageStatistics"
    TempPlan = "TempPlan"

    def to_domain_model(self) -> Type[Document]:
        from services.base.type_resolver import TypeResolver

        return TypeResolver.get_document(self)
