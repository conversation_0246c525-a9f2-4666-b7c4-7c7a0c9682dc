from typing import Sequence

from services.base.domain.schemas.extensions.extension_schema import ExtensionSchema
from services.base.domain.seeders.seeder_base import SeederBase
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    CorrelationInput,
    SingleOutcomeCorrelationResult,
)
from services.serverless.apps.trend_insights.app.models.trend_insights_models import (
    TrendInsightsInput,
    TrendInsightsSeriesOutput,
)
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID


class ExtensionSchemaSeeder(SeederBase):
    @staticmethod
    def seed_data() -> Sequence[ExtensionSchema]:
        return [
            ExtensionSchema(
                extension_id=TREND_INSIGHTS_EXTENSION_ID,
                version="1.0.0",
                input_schema=TrendInsightsInput.model_json_schema(),
                output_schema=TrendInsightsSeriesOutput.model_json_schema(),
            ),
            ExtensionSchema(
                extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
                version="1.0.0",
                input_schema=CorrelationInput.model_json_schema(),
                output_schema=SingleOutcomeCorrelationResult.model_json_schema(),
            ),
        ]
