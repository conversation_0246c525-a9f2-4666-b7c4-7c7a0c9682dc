import logging

import sentry_sdk
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from settings.app_config import AppSettings
from settings.app_constants import RUN_ENV_LOCAL
from settings.app_secrets import AppSecrets


class LambdaTelemetryInstrumentor:
    is_initialized = False

    @classmethod
    def initialize(cls, service_name: str, settings: AppSettings, secrets: AppSecrets):
        if settings.RUN_ENV == RUN_ENV_LOCAL:
            logging.info("skipping telemetry initialisation on local")
            return
        logging.info("initializing telemetry")

        logging.info("initializing sentry")
        sentry_sdk.init(
            dsn=secrets.MY_LLIF_APPS_SENTRY_DSN,
            release=settings.APP_VERSION,
            debug=False,
            environment=settings.RUN_ENV,
            send_default_pii=False,
            traces_sample_rate=settings.TELEMETRY_TRACES_SAMPLE_RATE,
            profiles_sample_rate=settings.TELEMETRY_PROFILE_SAMPLE_RATE,
            max_request_body_size=settings.TELEMETRY_REQUEST_BODIES,
            integrations=[AwsLambdaIntegration(), LoggingIntegration(level=logging.INFO, event_level=logging.WARNING)],
            server_name=service_name,
        )
        cls.is_initialized = True
