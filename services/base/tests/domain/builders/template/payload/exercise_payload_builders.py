from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.schemas.events.exercise.cardio import CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthIdentifier
from services.base.domain.schemas.templates.payload.exercise_template_payloads import (
    CardioTemplatePayload,
    ExerciseTemplatePayload,
    StrengthTemplatePayload,
)
from services.base.tests.domain.builders.exercise.cardio_builder import CardioBuilder
from services.base.tests.domain.builders.exercise.exercise_builder import ExerciseBuilder
from services.base.tests.domain.builders.exercise.strength_builder import StrengthBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class ExercisePayloadBuilder(EventPayloadBuilderBase, ExerciseIdentifier):
    def build(self) -> ExerciseTemplatePayload:
        return ExerciseTemplatePayload.map(
            model=ExerciseBuilder()
            .with_name(self._name or PrimitiveTypesGenerator.generate_random_string())
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class StrengthPayloadBuilder(EventPayloadBuilderBase, StrengthIdentifier):
    def build(self) -> StrengthTemplatePayload:
        return StrengthTemplatePayload.map(
            model=StrengthBuilder()
            .with_name(self._name or PrimitiveTypesGenerator.generate_random_string())
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class CardioPayloadBuilder(EventPayloadBuilderBase, CardioIdentifier):
    def build(self) -> CardioTemplatePayload:
        return CardioTemplatePayload.map(
            model=CardioBuilder()
            .with_name(self._name or PrimitiveTypesGenerator.generate_random_string())
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
