from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.schemas.events.content.audio import AudioIdentifier
from services.base.domain.schemas.events.content.content import ContentIdentifier
from services.base.domain.schemas.events.content.image import ImageIdentifier
from services.base.domain.schemas.events.content.interactive import InteractiveIdentifier
from services.base.domain.schemas.events.content.text import TextIdentifier
from services.base.domain.schemas.events.content.video import VideoIdentifier
from services.base.domain.schemas.templates.payload.content_template_payloads import (
    AudioTemplatePayload,
    ContentTemplatePayload,
    ImageTemplatePayload,
    InteractiveTemplatePayload,
    TextTemplatePayload,
    VideoTemplatePayload,
)
from services.base.tests.domain.builders.content.audio_builder import AudioBuilder
from services.base.tests.domain.builders.content.content_builder import ContentBuilder
from services.base.tests.domain.builders.content.image_builder import ImageBuilder
from services.base.tests.domain.builders.content.interactive_builder import InteractiveBuilder
from services.base.tests.domain.builders.content.text_builder import TextBuilder
from services.base.tests.domain.builders.content.video_builder import VideoBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class AudioPayloadBuilder(EventPayloadBuilderBase, AudioIdentifier):

    def build(self) -> AudioTemplatePayload:
        return AudioTemplatePayload.map(
            model=AudioBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class ContentPayloadBuilder(EventPayloadBuilderBase, ContentIdentifier):

    def build(self) -> ContentTemplatePayload:
        return ContentTemplatePayload.map(
            model=ContentBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class ImagePayloadBuilder(EventPayloadBuilderBase, ImageIdentifier):

    def build(self) -> ImageTemplatePayload:
        return ImageTemplatePayload.map(
            model=ImageBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class InteractivePayloadBuilder(EventPayloadBuilderBase, InteractiveIdentifier):

    def build(self) -> InteractiveTemplatePayload:
        return InteractiveTemplatePayload.map(
            model=InteractiveBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class TextPayloadBuilder(EventPayloadBuilderBase, TextIdentifier):

    def build(self) -> TextTemplatePayload:
        return TextTemplatePayload.map(
            model=TextBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class VideoPayloadBuilder(EventPayloadBuilderBase, VideoIdentifier):

    def build(self) -> VideoTemplatePayload:
        return VideoTemplatePayload.map(
            model=VideoBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
