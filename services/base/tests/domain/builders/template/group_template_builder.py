import random
from datetime import datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.templates.group_template import GroupTemplate


class GroupTemplateBuilder:
    def __init__(self):
        self._name: str | None = None
        self._template_ids: Sequence[UUID] | None = None
        self._id: UUID | None = None
        self._owner_id: UUID | None = None
        self._deleted_at: datetime | bool | None = None
        self._archived_at: datetime | bool | None = None

    def build(self):
        self._owner_id = self._owner_id or uuid4()
        archived_at = (
            self._archived_at
            if isinstance(self._archived_at, datetime)
            else (
                None
                if self._archived_at is None or self._archived_at is False
                else random.choice((PrimitiveTypesGenerator.generate_random_aware_datetime(), None))
            )
        )
        return GroupTemplate(
            id=self._id or uuid4(),
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            template_ids=self._template_ids
            or [uuid4() for _ in range(PrimitiveTypesGenerator.generate_random_int(1, 5))],
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            type=DataType.GroupTemplate,
            archived_at=archived_at,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
        )

    def build_n(self, n: int | None = None) -> list[GroupTemplate]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_template_ids(self, template_ids: Sequence[UUID]) -> Self:
        self._template_ids = template_ids
        return self

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self

    def with_deleted_at(self, deleted_at: datetime | bool) -> Self:
        self._deleted_at = deleted_at
        return self

    def with_archived_at(self, archived_at: datetime | bool) -> Self:
        self._archived_at = archived_at
        return self
