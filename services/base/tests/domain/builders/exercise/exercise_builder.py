from typing import Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.exercise.exercise import Exercise, ExerciseCategory, ExerciseIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class ExerciseBuilder(EventBuilderBase, ExerciseIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> Exercise:
        return Exercise(
            type=DataType.Exercise,
            template_id=None,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ExerciseCategory),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(max_value=10, min_value=0),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            group_id=self._group_id,
            id=uuid4(),
            asset_references=self._asset_references,
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Exercise]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
