from typing import Self

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.schemas.events.document_base import EventMetadata


class EventMetadataBuilder:

    def __init__(self):
        self._origin: Origin | None = None

    def build(self) -> EventMetadata:
        origin = self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin)
        return EventMetadata(
            organization=Organization(origin.value),
            origin=origin,
            source_service=PrimitiveTypesGenerator.generate_random_enum(enum_type=SourceService),
            service=PrimitiveTypesGenerator.generate_random_enum(enum_type=Service),
            origin_device=PrimitiveTypesGenerator.generate_random_string(allow_none=True, max_length=40),
            source_os=PrimitiveTypesGenerator.generate_random_enum(SourceOS),
        )

    def with_origin(self, origin: Origin) -> Self:
        self._origin = origin
        return self
