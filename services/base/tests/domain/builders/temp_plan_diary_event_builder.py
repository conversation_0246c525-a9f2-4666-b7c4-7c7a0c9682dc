from typing import Self

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.schemas.diary_events import DiaryEventsConsumablesExtension
from services.base.domain.schemas.temp_plan import TempPlanDiaryEvent
from services.base.domain.value_limits.diary_events import DiaryEventsValueLimit
from services.base.tests.domain.builders.temp_plan_diary_event_metadata_builder import TempPlanDiaryEventMetadataBuilder


class TempPlanDiaryEventBuilder:
    def __init__(self):
        self._type: DiaryEventType | None = None
        self._consumables_extension: DiaryEventsConsumablesExtension | None = None
        self._name: str | None = None

    def build(self) -> TempPlanDiaryEvent:
        return TempPlanDiaryEvent(
            timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(),
            type=self._type or PrimitiveTypesGenerator.generate_random_enum(enum_type=DiaryEventType),
            name=self._name
            or PrimitiveTypesGenerator.generate_random_string(max_length=DiaryEventsValueLimit.MAX_LENGTH),
            metadata=TempPlanDiaryEventMetadataBuilder().build(),
            consumables_extension=self._consumables_extension,
        )

    def with_type(self, type: DiaryEventType) -> Self:
        self._type = type
        return self

    def with_consumable_extension(self, ce: DiaryEventsConsumablesExtension | None) -> Self:
        self._consumables_extension = ce
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self
