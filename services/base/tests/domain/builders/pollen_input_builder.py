from datetime import datetime
from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.models.environment_inputs import (
    PollenInput,
)
from services.base.domain.schemas.environment import EnvironmentMetadata, EnvironmentSystemProperties
from services.base.domain.schemas.pollen import PollenSpecies
from services.base.domain.schemas.shared import CoordinatesModel


class PollenInputBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None
        self._coordinates: CoordinatesModel | None = None

    def build(self) -> PollenInput:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return PollenInput(
            type=DataType.Pollen,
            timestamp=timestamp,
            coordinates=self._coordinates or PrimitiveTypesGenerator.get_random_coordinates(),
            metadata=EnvironmentMetadata(provider="OpenMeteo"),
            system_properties=EnvironmentSystemProperties(
                created_at=PrimitiveTypesGenerator.generate_random_aware_datetime()
            ),
            tree=PollenSpecies(count=PrimitiveTypesGenerator.generate_random_int()),
            weed=PollenSpecies(count=PrimitiveTypesGenerator.generate_random_int()),
            grass=PollenSpecies(count=PrimitiveTypesGenerator.generate_random_int()),
        )

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_coordinates(self, coordinates: CoordinatesModel) -> Self:
        self._coordinates = coordinates
        return self

    def build_n(self, n: int | None = None) -> Sequence[PollenInput]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]
