from typing import Self
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.records.sleep_record import SleepRecord, SleepRecordIdentifier
from services.base.tests.domain.builders.document_metadata_builder import DocumentMetadataBuilder
from services.base.tests.domain.builders.records.record_builder_base import RecordBuilderBase


class SleepRecordBuilder(RecordBuilderBase, SleepRecordIdentifier):
    def __init__(self):
        super().__init__()
        self._stage: SleepStage | None = None

    def build(self) -> SleepRecord:
        return SleepRecord(
            type=DataType.SleepRecord,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=self._timestamp),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            id=uuid4(),
            stage=self._stage or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepStage),
            metadata=DocumentMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
        )

    def with_stage(self, stage: SleepStage) -> Self:
        self._stage = stage
        return self
