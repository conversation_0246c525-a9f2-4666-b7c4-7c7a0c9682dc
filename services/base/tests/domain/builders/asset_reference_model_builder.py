from typing import Optional

from services.base.application.assets import Assets
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.assets_enums import AssetType
from services.base.domain.schemas.shared import AssetReferenceModel


class AssetReferenceModelBuilder:
    def build(self) -> AssetReferenceModel:
        return AssetReferenceModel(
            asset_id=Assets.generate_asset_id(),
            asset_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=AssetType),
        )

    def build_n(self, n: Optional[int] = None) -> list[AssetReferenceModel]:
        return [
            AssetReferenceModel(
                asset_id=Assets.generate_asset_id(),
                asset_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=AssetType),
            )
            for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]
