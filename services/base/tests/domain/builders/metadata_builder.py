from __future__ import annotations

from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata import DataIntegrity, Organization
from services.base.domain.schemas.metadata import Metadata


class MetadataBuilder:
    def __init__(self):
        self._user_uuid: UUID | None = None
        self._organization: Organization | None = None

    def build(self) -> Metadata:
        user_uuid = self._user_uuid or uuid4()
        organization = self._organization or PrimitiveTypesGenerator.generate_random_enum(Organization)
        return Metadata(
            user_uuid=user_uuid,
            organization=organization,
            data_integrity=PrimitiveTypesGenerator.generate_random_enum(DataIntegrity),
        )

    def with_user_uuid(self, user_uuid: UUID) -> MetadataBuilder:
        self._user_uuid = user_uuid
        return self

    def with_organization(self, organization: Organization) -> MetadataBuilder:
        self._organization = organization
        return self
