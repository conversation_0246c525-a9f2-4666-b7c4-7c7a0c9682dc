from unittest.mock import MagicMock

from services.base.api.authentication.request_handling import get_refresh_token
from services.base.application.constants import UserTokenKeys


def test_get_refresh_token_valid_bearer():
    request = MagicMock()
    inp = "test"
    request.cookies = {UserTokenKeys.API_REFRESH_TOKEN: inp}
    payload = get_refresh_token(request)
    assert payload == inp


def test_get_refresh_token_invalid_bearer():
    request = MagicMock()
    request.headers = {}
    request.cookies = {}
    assert get_refresh_token(request) is None
