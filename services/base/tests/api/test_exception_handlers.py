from typing import List
from unittest.mock import MagicMock

from services.base.api.authentication.exceptions import InvalidCredentialsException
from services.base.api.exception_handlers import invalid_credentials_exception_handler
from services.base.application.constants import UserTokenKeys
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator


def test_invalid_credentials_exception_handler():
    # Arrange & Act
    message = PrimitiveTypesGenerator.generate_random_string()
    try:
        raise InvalidCredentialsException(message=message)
    except InvalidCredentialsException as error:
        response = invalid_credentials_exception_handler(_=MagicMock(), exc=error)
        # Assert
        assert response.status_code == 401
        assert response.headers is not None
        headers: List[str] = response.headers.values()
        assert any([UserTokenKeys.API_REFRESH_TOKEN in header for header in headers])
