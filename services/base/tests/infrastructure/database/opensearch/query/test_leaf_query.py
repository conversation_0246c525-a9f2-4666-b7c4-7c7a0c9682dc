import pytest

from services.base.domain.schemas.query.leaf_query import <PERSON><PERSON><PERSON><PERSON><PERSON>, RangeQuery


class TestLeafQuery:

    def test_radius_query_with_wrong_distance_format_should_throw_exception(self):
        with pytest.raises(ValueError):
            RadiusQuery(field_name="field_name", radius="10kn", latitude=20.2, longitude=25.2)

    def test_range_query_with_gte_bigger_than_lte_should_throw_exception(self):
        with pytest.raises(ValueError) as e:
            RangeQuery(field_name="field_name", lte=10, gte=15)
        assert "gte has to be less than lte" in str(e.value)

    def test_range_query_with_no_gte_and_lte_set_should_throw_exception(self):
        with pytest.raises(ValueError) as e:
            RangeQuery(field_name="field_name")
        assert "lte or gte has to be set" in str(e.value)
