import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.air_quality import AirQuality, AirQualityFields
from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsFields
from services.base.domain.schemas.inbox.inbox_message import InboxMessage, InboxMessageFields
from services.base.domain.schemas.query.leaf_query import PatternQuery, RadiusQuery, ValuesQuery
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_refiner import (
    LeafQueryRefiner,
)
from services.base.infrastructure.database.opensearch.query_translator.query_util import QueryUtil


class TestLeafQueryRefiner:
    class TestValuesQueryRefiner:
        def test_values_query_on_text_field_with_keyword_should_be_refined(self):
            values = PrimitiveTypesGenerator.generate_n_random_strings()
            values_query = ValuesQuery(field_name=DiaryEventsFields.NAME, values=values)

            refined_values_query = LeafQueryRefiner.refine(leaf_query=values_query, domain_type=DiaryEvents)

            assert isinstance(refined_values_query, ValuesQuery)
            assert refined_values_query.field_name == "name.keyword"
            assert refined_values_query.values == values_query.values

        def test_values_query_on_keyword_field_should_not_be_refined(self):
            values = PrimitiveTypesGenerator.generate_n_random_strings()
            values_query = ValuesQuery(field_name=DiaryEventsFields.TYPE, values=values)

            refined_values_query = LeafQueryRefiner.refine(leaf_query=values_query, domain_type=DiaryEvents)

            assert refined_values_query == values_query

        def test_values_query_on_text_field_without_keyword_should_throw_exception(self):
            values = PrimitiveTypesGenerator.generate_n_random_strings()
            values_query = ValuesQuery(field_name=DiaryEventsFields.EXPLANATION, values=values)

            with pytest.raises(QueryValidationException) as e:
                LeafQueryRefiner.refine(leaf_query=values_query, domain_type=DiaryEvents)
            assert e.value.args[0] == "ValuesQuery on text field explanation is not supported."

        def test_values_query_on_non_existing_field_should_throw_exception(self):
            values = PrimitiveTypesGenerator.generate_n_random_strings()
            values_query = ValuesQuery(field_name="non_existing", values=values)

            with pytest.raises(ShouldNotReachHereException) as e:
                LeafQueryRefiner.refine(leaf_query=values_query, domain_type=DiaryEvents)
            assert e.value.args[0] == "Field non_existing is not in index mapping."

        def test_index_values_query_should_not_be_refined(self):
            index_values_query = QueryUtil.index_values_query("index_name")

            refined_values_query = LeafQueryRefiner.refine(leaf_query=index_values_query, domain_type=DiaryEvents)
            assert index_values_query == refined_values_query

        def test_boolean_values_query_should_not_be_refined(self):
            boolean_values_query = ValuesQuery(field_name=InboxMessageFields.IS_URGENT, values=["true"])

            refined_values_query = LeafQueryRefiner.refine(leaf_query=boolean_values_query, domain_type=InboxMessage)
            assert boolean_values_query == refined_values_query

        def test_tag_values_query_should_be_refined(self):
            values = PrimitiveTypesGenerator.generate_n_random_strings()
            tag_values_query = ValuesQuery(field_name=DocumentLabels.TAGS, values=values)

            refined_values_query = LeafQueryRefiner.refine(leaf_query=tag_values_query, domain_type=DiaryEvents)
            assert isinstance(refined_values_query, ValuesQuery)
            assert refined_values_query.field_name == f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword"
            assert refined_values_query.values == values

    class TestMatchQueryRefiner:

        def test_match_query_on_text_field_should_not_be_refined(self):
            query = PrimitiveTypesGenerator.generate_random_string()
            match_query = PatternQuery(field_names=[DiaryEventsFields.EXPLANATION], pattern=query)

            refined_query = LeafQueryRefiner.refine(leaf_query=match_query, domain_type=DiaryEvents)
            assert refined_query == match_query

        def test_match_query_on_keyword_field_should_throw_exception(self):
            query = PrimitiveTypesGenerator.generate_random_string()
            match_query = PatternQuery(
                field_names=[DiaryEventsFields.TYPE, DiaryEventsFields.EXPLANATION], pattern=query
            )

            with pytest.raises(QueryValidationException) as e:
                LeafQueryRefiner.refine(leaf_query=match_query, domain_type=DiaryEvents)
            assert e.value.args[0] == f"Match Query on keyword field {DiaryEventsFields.TYPE} is not supported."

    class TestRadiusQueryRefiner:

        def test_radius_query_should_not_be_refined(self):
            random_lat = PrimitiveTypesGenerator.generate_random_float(min_value=-90, max_value=90, decimal_numbers=4)
            random_lon = PrimitiveTypesGenerator.generate_random_float(min_value=-180, max_value=180, decimal_numbers=4)
            query = RadiusQuery(
                field_name=AirQualityFields.COORDINATES, radius="10km", latitude=random_lat, longitude=random_lon
            )

            refined_query = LeafQueryRefiner.refine(leaf_query=query, domain_type=AirQuality)
            assert refined_query == query

        def test_radius_on_non_geo_point_type_should_throw_exception(self):
            random_lat = PrimitiveTypesGenerator.generate_random_float(min_value=-90, max_value=90, decimal_numbers=4)
            random_lon = PrimitiveTypesGenerator.generate_random_float(min_value=-180, max_value=180, decimal_numbers=4)
            field_name = f"{AirQualityFields.METADATA}.provider"
            query = RadiusQuery(field_name=field_name, radius="10km", latitude=random_lat, longitude=random_lon)

            with pytest.raises(QueryValidationException) as e:
                LeafQueryRefiner.refine(leaf_query=query, domain_type=AirQuality)
            assert e.value.args[0] == (
                "Radius query cannot be applied on field type keyword. Only supported type is GeoPoint."
            )
