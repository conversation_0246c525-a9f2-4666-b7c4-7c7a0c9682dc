from uuid import UUID, uuid4

import pytest

from services.base.infrastructure.database.opensearch.doc_id_refiner import DocI<PERSON><PERSON><PERSON><PERSON>
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    ALIAS_LAST_INDEX_POINTER,
)


class TestDocIdHelpers:
    @pytest.mark.parametrize(
        "current_index,expected_first_two_chars",
        [
            (f"event-{ALIAS_FIRST_INDEX_POINTER}", "00"),
            ("event-000001", "01"),
            ("event-000010", "0A"),
            ("event-000100", "64"),
            ("event-000200", "C8"),
            (f"event-{ALIAS_LAST_INDEX_POINTER}", "FF"),
        ],
    )
    def test_refine_doc_id_should_pass(self, current_index: str, expected_first_two_chars: str):
        doc_id = uuid4()
        expected_uuid = UUID(f"{expected_first_two_chars}{doc_id.hex[2:]}")
        assert DocIdRefiner._refine_doc_id(index_name=current_index, doc_id=doc_id) == expected_uuid

    @pytest.mark.parametrize(
        "current_index",
        [
            ("event-000256",),
            ("event-1",),
            ("event-1000",),
            ("event-99234",),
            ("event-ASD234",),
            ("event-100000",),
        ],
    )
    def test_refine_doc_id_should_fail(self, current_index: str):
        doc_id = uuid4()
        with pytest.raises(ValueError):
            DocIdRefiner._refine_doc_id(index_name=current_index, doc_id=doc_id)
