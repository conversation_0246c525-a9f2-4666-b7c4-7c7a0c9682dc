from datetime import datetime, timedelta, timezone

import pytest

from services.base.domain.enums.upload_states import UploadStates
from services.base.infrastructure.database.sql_alchemy.models.upload_state_entity import UploadStateEntity


@pytest.mark.parametrize(
    "updated_at,state,expected_result",
    [
        (datetime.now(timezone.utc) - timedelta(hours=1), UploadStates.EXTRACTED, False),
        (datetime.now(timezone.utc) - timedelta(hours=6), UploadStates.EXTRACTED, True),
        (datetime.now(timezone.utc) - timedelta(hours=1), UploadStates.LOADING, False),
        (datetime.now(timezone.utc) - timedelta(hours=6), UploadStates.LOADING, True),
        (datetime.now(timezone.utc) - timedelta(hours=1), UploadStates.UPLOADED, False),
        (datetime.now(timezone.utc) - timedelta(hours=6), UploadStates.UPLOADED, True),
    ],
)
def test_is_progress_idle(updated_at, state, expected_result):
    assert UploadStateEntity.is_progress_idle(updated_at=updated_at, state=state) == expected_result
