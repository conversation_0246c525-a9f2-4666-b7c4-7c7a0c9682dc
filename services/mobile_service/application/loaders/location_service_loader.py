import logging
from collections import defaultdict
from typing import List, <PERSON>tional, <PERSON><PERSON>
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch
from pydantic import Field

from services.base.application.input_validators.shared import InputTimeIntervalModel
from services.base.application.loaders.location_loader_base import LocationLoaderBase
from services.base.application.loaders.location_loader_utils import LocationLoaderUtils, LocationStatisticsResponse
from services.base.application.message_broker_client import MessageBrokerClient
from services.base.application.utils.metadata import create_metadata
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.constants.value_limits import (
    CoordinatesValueLimit,
    LocationPlaceAddressValueLimit,
    LocationPlaceNameValueLimit,
)
from services.base.domain.enums.activity_type import ActivityType
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.location import (
    Location,
    LocationFields,
    PlaceVisitDetails,
    PlaceVisitD<PERSON>ils<PERSON>ields,
    WaypointDetails,
)
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel
from services.mobile_service.application.loaders.metadata_input import MetadataInputModel


class LocationActivity(BaseDataModel):
    activity_type: ActivityType = Field(alias=LocationFields.ACTIVITY_TYPE, default=None)
    activity_type_probability: float = Field(alias=LocationFields.ACTIVITY_TYPE_PROBABILITY, ge=0, le=1, default=None)


class Place(InputTimeIntervalModel):
    latitude: float = Field(ge=CoordinatesValueLimit.LAT_MIN, le=CoordinatesValueLimit.LAT_MAX)
    longitude: float = Field(ge=CoordinatesValueLimit.LON_MIN, le=CoordinatesValueLimit.LON_MAX)
    name: str = Field(
        alias=PlaceVisitDetailsFields.NAME,
        min_length=LocationPlaceNameValueLimit.MINIMUM,
        max_length=LocationPlaceNameValueLimit.MAXIMUM,
    )
    address: Optional[str] = Field(
        alias=PlaceVisitDetailsFields.ADDRESS,
        min_length=LocationPlaceAddressValueLimit.MINIMUM,
        max_length=LocationPlaceAddressValueLimit.MAXIMUM,
    )
    confidence: Optional[str] = Field(default=None)


class LocationServiceCoordinates(BaseDataModel):
    latitude: float = Field(ge=CoordinatesValueLimit.LAT_MIN, le=CoordinatesValueLimit.LAT_MAX)
    longitude: float = Field(ge=CoordinatesValueLimit.LON_MIN, le=CoordinatesValueLimit.LON_MAX)
    heading: Optional[float] = Field(default=None)
    speed: Optional[float] = Field(default=None)
    accuracy: Optional[float] = Field(ge=0, default=None)
    altitude: Optional[float] = Field(default=None)
    altitude_accuracy: Optional[float] = Field(alias="altitudeAccuracy", default=None)


class LocationServiceData(InputTimeIntervalModel):
    data: Optional[LocationServiceCoordinates] = Field(alias="coords", default=None)
    place: Optional[Place] = Field(default=None)
    activity: Optional[LocationActivity] = Field(default=None)


class LocationServiceLoader(LocationLoaderBase):
    PROVIDER = Provider.LLIF

    def __init__(
        self,
        user_uuid: UUID,
        metadata: MetadataInputModel,
        message_broker_client: MessageBrokerClient,
        data_type: DataType = DataType.Location,
        data: Optional[List[LocationServiceData]] = None,
        client: Optional[OpenSearch] = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.PROVIDER = metadata.organization
        self.input_data = data
        self.metadata = metadata
        self._message_broker_client = message_broker_client

    def _parse_data_to_entry(self, entry_list: List[LocationServiceData]) -> Location:
        """Parses data to output location entry"""
        starting_entry = entry_list[0]
        ending_entry = entry_list[-1]
        starting_datetime = starting_entry.timestamp
        ending_datetime = ending_entry.end_time or ending_entry.timestamp
        (
            location_details,
            statistics,
            place_details,
            activity_type,
            activity_type_probability,
        ) = self._parse_location_values(entry_list)

        output_location = Location(
            timestamp=starting_datetime,
            start_coordinates=location_details[0].coordinates,
            end_coordinates=location_details[-1].coordinates,
            average_coordinates=CoordinatesModel(
                latitude=statistics.latitude_sum / statistics.latitude_count,
                longitude=statistics.longitude_sum / statistics.longitude_count,
            ),
            start_altitude=location_details[0].altitude,
            end_altitude=location_details[-1].altitude,
            average_altitude=(
                statistics.altitude_sum / statistics.altitude_count if statistics.altitude_count > 0 else None
            ),
            waypoint_details=location_details if LocationLoaderUtils.should_details_be_included(statistics) else None,
            place_visit_details=place_details,
            duration=int((ending_datetime - starting_datetime).total_seconds()) if ending_datetime else None,
            end_time=ending_datetime,
            activity_type=activity_type,
            activity_type_probability=activity_type_probability,
            metadata=self._parsed_metadata,
        )
        return output_location

    def _parse_location_values(
        self, entry_list: List[LocationServiceData]
    ) -> Tuple[
        List[WaypointDetails], LocationStatisticsResponse, PlaceVisitDetails, Optional[ActivityType], Optional[float]
    ]:
        statistics = LocationStatisticsResponse()
        location_detail_list: List[WaypointDetails] = []
        place_visit = None
        activity_map = defaultdict(int)

        for entry in entry_list:
            latitude, longitude, altitude = entry.data.latitude, entry.data.longitude, entry.data.altitude
            statistics = LocationLoaderUtils.calculate_statistics(statistics, latitude, longitude, altitude)
            if entry.activity:
                activity_map[entry.activity.activity_type] += 1
            try:
                timestamp = entry.timestamp
                end_time = entry.end_time
                location_detail_list.append(
                    WaypointDetails(
                        coordinates=CoordinatesModel(latitude=latitude, longitude=longitude),
                        timestamp=timestamp,
                        end_time=end_time,
                        duration=int((end_time - timestamp).total_seconds()) if end_time else None,
                        altitude=altitude,
                        velocity=entry.data.speed,
                        heading=entry.data.heading,
                        vertical_accuracy=entry.data.altitude_accuracy,
                        horizontal_accuracy=entry.data.accuracy,
                    )
                )
            except Exception as e:  # pylint:disable=broad-except
                logging.warning("Could not process location detail entry %s, skipping. %s", entry, e)
                continue

            if place := entry.place:
                try:
                    place_visit = PlaceVisitDetails(
                        name=place.name,
                        address=place.address,
                        confidence=place.confidence,
                    )

                except Exception as e:  # pylint:disable=broad-except
                    logging.warning("Could not process location detail entry %s, skipping. %s", entry, e)
                    continue

        activity_type = None
        activity_type_probability = None

        if activity_map:
            # Get the major activity for the entries list
            try:
                # Returns the key of the value with the highest count
                activity_type = max(activity_map, key=activity_map.get)

                activity_type_probability = round(activity_map[activity_type] / sum(activity_map.values()), 4)
            except Exception as error:
                logging.error("Could not load activity from activity map %s, error: %s", activity_map, error)

        return location_detail_list, statistics, place_visit, activity_type, activity_type_probability

    def process_data(self) -> None:
        if not self.input_data:
            return

        self._parsed_metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            **self.metadata.model_dump(),
        )
        super()._process_input_data(
            input_entries=self.input_data,
            load_aggregation_interval=self.LOAD_AGGREGATION_INTERVAL,
        )
