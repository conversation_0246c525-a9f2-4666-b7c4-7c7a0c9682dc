import logging
import random
from datetime import datetime, timedelta, timezone
from typing import List, Optional, <PERSON><PERSON>
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch
from pydantic import Field

from services.base.application.input_validators.shared import InputTimeIntervalModel
from services.base.application.loaders.loader_base import LoaderBase
from services.base.application.utils.metadata import create_metadata
from services.base.application.utils.time import get_datetime_difference
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC, SECONDS_IN_HOUR
from services.base.domain.constants.value_limits import RestingHeartRateValueLimit
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.metadata import Metadata
from services.base.domain.schemas.resting_heart_rate import (
    RestingHeartRate,
    RestingHeartRateBpmDetailFields,
    RestingHeartRateDetail,
)
from services.base.infrastructure.database.opensearch.wrappers.client import get_default_os_client
from services.mobile_service.application.loaders.metadata_input import MetadataInputModel
from settings.app_constants import DEMO1_UUID


class RestingHeartRateData(InputTimeIntervalModel):
    value: float = Field(
        alias=RestingHeartRateBpmDetailFields.VALUE,
        ge=RestingHeartRateValueLimit.MINIMUM,
        le=RestingHeartRateValueLimit.MAXIMUM,
    )
    confidence: Optional[float] = Field(
        alias=RestingHeartRateBpmDetailFields.CONFIDENCE,
        default=None,
        ge=RestingHeartRateValueLimit.MINIMUM,
    )


class RestingHeartRateLoader(LoaderBase):
    LOAD_AGGREGATION_INTERVAL = timedelta(seconds=SECONDS_IN_HOUR)
    _source = "resting_heart_rate"

    def __init__(
        self,
        user_uuid: UUID,
        metadata: MetadataInputModel,
        data_type: DataType = DataType.RestingHeartRate,
        data: Optional[List[RestingHeartRateData]] = None,
        client: Optional[OpenSearch] = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.PROVIDER = metadata.organization
        self.input_data = data
        self.metadata = metadata

    def process_data(self) -> None:
        if not self.input_data:
            return

        self._process_resting_heart_rate_data(self.input_data)

    def _parse_data_to_entry(self, metadata: Metadata, entry_list: List[RestingHeartRateData]) -> RestingHeartRate:
        starting_entry = entry_list[0]
        ending_entry = entry_list[-1]

        starting_datetime = starting_entry.timestamp
        ending_datetime = ending_entry.end_time or ending_entry.timestamp

        output_rhr_detail, bpm_avg, bpm_min, bpm_max = self._parse_rhr_values(entry_list)

        output_resting_heart_rate = RestingHeartRate(
            timestamp=starting_datetime,
            bpm_avg=bpm_avg,
            bpm_max=bpm_max,
            bpm_min=bpm_min,
            duration=int((ending_datetime - starting_datetime).total_seconds()) if ending_datetime else None,
            rhr_detail=output_rhr_detail,
            end_time=ending_datetime,
            metadata=metadata,
        )
        return output_resting_heart_rate

    def _parse_rhr_values(
        self,
        entries: List[RestingHeartRateData],
    ) -> Tuple[
        List[RestingHeartRateDetail],
        float,
        float,
        float,
    ]:
        output_list: List[RestingHeartRateDetail] = []
        value_list: List[float] = []

        for entry in entries:
            value_list.append(entry.value)
            output_list.append(
                RestingHeartRateDetail(
                    value=entry.value,
                    timestamp=entry.timestamp,
                    confidence=entry.confidence,
                )
            )

        bpm_avg = sum(value_list) / len(output_list)
        bpm_min = min(value_list)
        bpm_max = max(value_list)
        return output_list, bpm_avg, bpm_min, bpm_max

    def _process_resting_heart_rate_data(self, input_data: List[RestingHeartRateData]):
        metadata: Metadata = create_metadata(
            user_uuid=self.user_uuid,
            **self.metadata.model_dump(),
        )
        input_data.sort(key=lambda entry: entry.timestamp)
        data = (entry for entry in input_data)
        to_be_committed_entries: List[str] = []
        bulk_entries: List[RestingHeartRateData] = []
        cached_entry: Optional[RestingHeartRateData] = None

        for data_entry in data:
            try:
                cached_entry = data_entry if not cached_entry else cached_entry

                time_difference = get_datetime_difference(
                    cached_entry.timestamp,
                    data_entry.timestamp,
                )

                if time_difference < self.LOAD_AGGREGATION_INTERVAL:
                    bulk_entries.append(data_entry)
                    continue

                output_resting_heart_rate: RestingHeartRate = self._parse_data_to_entry(
                    metadata=metadata, entry_list=bulk_entries
                )
                to_be_committed_entries.append(output_resting_heart_rate.model_dump_json(by_alias=True))

                del bulk_entries[:]
                cached_entry = data_entry
                bulk_entries.append(cached_entry)
            except Exception as error:  # pylint:disable=broad-except
                logging.exception("Error processing entry: %s, got error: %s", data_entry, repr(error))
                del bulk_entries[:]
                cached_entry = None
            self._commit_if_limit_reached(to_be_committed_entries)
        try:
            if bulk_entries:
                output_resting_heart_rate: RestingHeartRate = self._parse_data_to_entry(
                    entry_list=bulk_entries, metadata=metadata
                )
                to_be_committed_entries.append(output_resting_heart_rate.model_dump_json(by_alias=True))
            self._commit(to_be_committed_entries)
        except Exception as error:  # pylint:disable=broad-except
            logging.exception("Error processing entries: %s", repr(error))


def load_data(loader):
    loader.process_data()


if __name__ == "__main__":
    now = datetime.now(timezone.utc)
    up_to = 50
    data = [
        RestingHeartRateData(
            timestamp=now - timedelta(hours=t + 1),
            end_time=now - timedelta(hours=t),
            value=int(random.uniform(40, 190)),
        )
        for t in range(0, up_to)
    ]
    loader = RestingHeartRateLoader(
        user_uuid=DEMO1_UUID,
        data_type=DataType.RestingHeartRate,
        client=get_default_os_client(),
        data=data,
        fallback_timezone=ZoneInfo("UTC"),
        metadata=MetadataInputModel(organization=Organization.APPLE),
    )
    load_data(loader=loader)
