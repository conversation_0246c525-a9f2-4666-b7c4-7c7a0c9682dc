from datetime import timedelta
from typing import Optional, Union

from starlette import status
from starlette.responses import RedirectResponse, Response

from services.base.api.authentication.token_handling import generate_refresh_token
from services.base.application.constants import UserTokenKeys
from services.user_service.api.response_models.member_user_sign_in_response import MemberUserSignInResponse
from settings.app_config import settings


def generate_sign_in_response(
    sign_in_response_model: MemberUserSignInResponse,
    redirect_uri: Optional[str] = None,
    refresh_token: Optional[str] = None,
) -> Union[Response, RedirectResponse]:
    """Creates login response for the successfully authorized user and returns new credentials.
    Setting new refresh token cookie is omitted if new account it only being linked for existing user."""
    # Create response
    response = (
        RedirectResponse(
            url=redirect_uri if redirect_uri else settings.AFTER_LOGIN_IOS_REDIRECT_URL,
            status_code=status.HTTP_302_FOUND,
        )
        if redirect_uri
        else Response(content=sign_in_response_model.model_dump_json(), media_type="application/json")
    )
    api_refresh_token = refresh_token or generate_refresh_token(
        user_uuid=sign_in_response_model.user.user_uuid,
        time_delta=timedelta(days=settings.API_REFRESH_TOKEN_EXPIRATION_TIME_DAYS),
    )
    response.set_cookie(
        key=UserTokenKeys.API_REFRESH_TOKEN,
        value=api_refresh_token,
        secure=settings.USE_SSL,
        httponly=True,
        samesite="lax",
    )
    return response
