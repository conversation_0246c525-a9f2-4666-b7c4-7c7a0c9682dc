import logging

from starlette.requests import Request

from services.base.application.exceptions import BadRequestException
from services.user_service.domain.providers.apple.constants import AppleUtilConstants


def validate_apple_request_origin(
    request: Request,
):
    referer = request.headers.get("referer", None)
    if not referer or referer not in [AppleUtilConstants.DOMAIN.value]:
        logging.exception(f"Denied request from unknown origin: client: {request.client}, headers: {request.headers}")
        raise BadRequestException(message="Invalid request origin.")
