from typing import List, Optional
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

from pydantic import Field, field_validator

from services.base.domain.enums.settings.options import AgeOption, LocationOption
from services.base.domain.enums.settings.units_system import UnitsSystem
from services.base.domain.enums.units.energy import EnergyUnits
from services.base.domain.schemas.shared import BaseDataModel


class MemberUserSettingsLocation(BaseDataModel):
    street_number: Optional[str] = Field(min_length=1, default=None)
    route: Optional[str] = Field(min_length=1, default=None)
    state: Optional[str] = Field(min_length=1, default=None)
    locality: Optional[str] = Field(min_length=1, default=None)
    country: Optional[str] = Field(min_length=1, default=None)
    postal_code: Optional[str] = Field(min_length=1, default=None)
    lat: Optional[float] = Field(default=None, ge=-180, le=180)
    lng: Optional[float] = Field(default=None, ge=-180, le=180)
    option: LocationOption = LocationOption.MAILING_ADDRESS


class MemberUserSettingsIdentity(BaseDataModel):
    name: Optional[str] = Field(min_length=1, default=None)
    email: Optional[str] = Field(min_length=1, default=None)
    sex: Optional[str] = Field(min_length=1, default=None)


class MemberUserSettingsBirth(BaseDataModel):
    dob: Optional[str] = Field(min_length=1, default=None)
    option: AgeOption = AgeOption.FULL_DATE


# UNITS
class MemberUserSettingsUnits(BaseDataModel):
    energy: EnergyUnits = EnergyUnits.CALORIES


class MemberUserSettingsProfile(BaseDataModel):
    location: MemberUserSettingsLocation = MemberUserSettingsLocation()
    identity: MemberUserSettingsIdentity = MemberUserSettingsIdentity()
    age: MemberUserSettingsBirth = MemberUserSettingsBirth()
    show_stepper: bool = True


class MemberUserSettingsPrivacy(BaseDataModel):
    business: bool = False
    individual: bool = False
    privacy_policy: bool = False
    use_policy: bool = False


class MemberUserSettingsFavorites(BaseDataModel):
    fav_graphs: Optional[List[str]] = Field(min_length=1, default=None)


class MemberUserSettingsGeneral(BaseDataModel):
    units: MemberUserSettingsUnits = MemberUserSettingsUnits()
    units_system: Optional[UnitsSystem] = None
    timezone: ZoneInfo = Field(default=ZoneInfo("UTC"))

    @field_validator("timezone", mode="before")
    def validate_timezone(cls, v):
        try:
            return ZoneInfo(str(v))
        except ZoneInfoNotFoundError:
            raise ValueError(f"invalid timezone: {v}")
