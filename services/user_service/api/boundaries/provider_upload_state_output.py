from typing import Optional

from services.base.domain.enums.provider import SupportedDataProviders
from services.base.domain.enums.upload_states import UploadStates
from services.base.domain.schemas.shared import BaseDataModel


class ProviderUploadStateOutput(BaseDataModel):
    provider: SupportedDataProviders
    state: UploadStates
    in_progress: bool
    loading_progress: Optional[int] = None
    user_filename: Optional[str] = None
    timestamp: str
