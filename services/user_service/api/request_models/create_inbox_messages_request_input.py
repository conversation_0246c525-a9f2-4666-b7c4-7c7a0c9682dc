from typing import List

from pydantic import Field

from services.base.application.input_validators.shared import InputTimestampModel
from services.base.domain.annotated_types import HTML
from services.base.domain.schemas.inbox.context_models import GenericContext
from services.base.domain.schemas.inbox.inbox_message import InboxMessageFields
from services.base.domain.schemas.shared import BaseDataModel


class CreateInboxMessagesRequestInputItem(InputTimestampModel):
    title: str = Field(min_length=1, alias=InboxMessageFields.TITLE)
    message: HTML = Field(min_length=1, alias=InboxMessageFields.MESSAGE)
    is_urgent: bool = Field(alias=InboxMessageFields.IS_URGENT)
    context: GenericContext = Field(alias=InboxMessageFields.CONTEXT)


class CreateInboxMessagesRequestInput(BaseDataModel):
    values: List[CreateInboxMessagesRequestInputItem] = Field(min_length=1)
