# syntax = docker/dockerfile:1.2
FROM python:3.13-slim-bookworm AS user_service_base

RUN rm -f /etc/apt/apt.conf.d/docker-clean
RUN --mount=type=cache,sharing=locked,target=/var/cache/apt \
    --mount=type=cache,sharing=locked,target=/var/lib/apt \
    apt-get update && \
    apt-get dist-upgrade -yq && \
    apt-get install -yq \
    libpq-dev \
    python3-dev \
    build-essential \
    tidy
RUN rm -rf /var/lib/apt/lists/*

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONPATH /app_code
ENV IS_CONTAINERIZED true
ENV APP_VERSION $APP_VERSION
ENV BUILD_VERSION $BUILD_VERSION

RUN --mount=type=cache,target=/root/.cache/pip pip install --upgrade uv
COPY services/base/requirements.txt ./base_requirements.txt
COPY services/user_service/requirements.txt ./user_requirements.txt
# Combine requirements files, removing duplicates
RUN cat base_requirements.txt user_requirements.txt | sort -u > requirements.txt
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

WORKDIR /app_code

# Remote stage creates user with limited permissions
FROM user_service_base AS server

COPY ./pyproject.toml ./pyproject.toml
COPY ./Makefile ./Makefile
COPY ./infrastructure ./infrastructure
COPY ./settings ./settings
COPY ./services/base ./services/base
COPY ./services/user_service ./services/user_service
# TODO: remove
COPY ./services/serverless ./services/serverless

RUN groupadd -g 9995 appuser && \
    useradd -mu 1000 -g appuser appuser

RUN chown -R appuser: /app_code
USER appuser

# Development stage adds tools necessary for local development and testing
FROM user_service_base AS local

# Copy over dev requirements file
COPY ./requirements-dev.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements-dev.txt

COPY ./pyproject.toml ./pyproject.toml
COPY ./Makefile ./Makefile
COPY ./infrastructure ./infrastructure
COPY ./settings ./settings
COPY ./services/base ./services/base
COPY ./services/user_service ./services/user_service
# TODO: remove
COPY ./services/serverless ./services/serverless