import random
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from uuid import UUID, uuid4

import pytest

from services.base.application.database.models.filter_types import UserUUIDTermsFilter
from services.base.application.database.models.filters import Filters
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.repository.temp_plan_repository import TempPlanRepository
from services.base.domain.schemas.temp_plan import TempPlan
from services.base.tests.domain.builders.temp_plan_builder import Temp<PERSON>lan<PERSON>uilder, TempPlanMetadataBuilder
from services.user_service.application.use_cases.remind_plan_summary_use_case import RemindPlanSummaryUseCase
from services.user_service.dependency_bootstrapper import DependencyBootstrapper


class TestRemindUserActivityUseCase:
    @dataclass
    class TestRemindUserActivity:
        now: datetime
        stale_plans: list[TempPlan]
        upcoming_daily_plans: list[TempPlan]
        user_id: UUID

    @pytest.fixture()
    async def remind_plans_test_input(self, temp_plan_repository: TempPlanRepository):
        user_id = uuid4()
        now = datetime.now(timezone.utc).replace(microsecond=0)
        max_time_delta_to_next_day = (
            datetime.now(timezone.utc).replace(hour=0, minute=0, microsecond=0, second=0) + timedelta(days=1) - now
        )

        stale_plans = []
        upcoming_daily_plans = []
        future_plans = []

        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5)):
            interval = PrimitiveTypesGenerator.generate_random_int(min_value=100, max_value=1_000_000)
            stale_plans.append(
                TempPlanBuilder()
                .with_metadata(
                    metadata=TempPlanMetadataBuilder()
                    .with_user_uuid(user_uuid=user_id)
                    .with_archived_at(random.choice((now, None)))
                    .build()
                )
                .with_scheduled_at(scheduled_at=now - PrimitiveTypesGenerator.generate_random_timedelta())
                .with_interval(interval=interval)
                .build()
            )

            upcoming_daily_plans.append(
                TempPlanBuilder()
                .with_metadata(
                    metadata=TempPlanMetadataBuilder()
                    .with_user_uuid(user_uuid=user_id)
                    .with_archived_at(random.choice((now, None)))
                    .build()
                )
                .with_scheduled_at(
                    scheduled_at=now
                    + PrimitiveTypesGenerator.generate_random_timedelta(max_timedelta=max_time_delta_to_next_day)
                )
                .with_interval(interval=interval)
                .build()
            )

            future_plans.append(
                TempPlanBuilder()
                .with_metadata(
                    metadata=TempPlanMetadataBuilder()
                    .with_user_uuid(user_uuid=user_id)
                    .with_archived_at(random.choice((now, None)))
                    .build()
                )
                .with_scheduled_at(
                    scheduled_at=now
                    + +PrimitiveTypesGenerator.generate_random_timedelta(
                        max_timedelta=max_time_delta_to_next_day + timedelta(seconds=interval)
                    )
                )
                .with_interval(interval=interval)
                .build()
            )

        stale_plans = await temp_plan_repository.insert(plans=stale_plans, force_strong_consistency=True)
        upcoming_daily_plans = await temp_plan_repository.insert(
            plans=upcoming_daily_plans, force_strong_consistency=True
        )
        future_plans = await temp_plan_repository.insert(plans=future_plans, force_strong_consistency=True)

        filters = Filters()
        filters.must_filters.with_filters([UserUUIDTermsFilter(value=[str(user_id)])])

        yield self.TestRemindUserActivity(
            user_id=user_id,
            stale_plans=[plan for plan in stale_plans if not plan.metadata.archived_at],
            upcoming_daily_plans=[plan for plan in upcoming_daily_plans if not plan.metadata.archived_at],
            now=now,
        )

        await temp_plan_repository.delete_by_id(
            ids=[p.id for p in [*stale_plans, *upcoming_daily_plans, *future_plans]]
        )

    async def test_remind_plan_summary_use_case_lookup_and_filter_plans(
        self, dependency_bootstrapper: DependencyBootstrapper, remind_plans_test_input: TestRemindUserActivity
    ):
        # Arrange
        remind_use_case = dependency_bootstrapper.get(interface=RemindPlanSummaryUseCase)

        expected_input_stale_plans = [
            p for p in remind_plans_test_input.stale_plans if (p.is_confirmation_required and p.is_urgent)
        ]
        expected_input_upcoming_daily_plans = [
            p for p in remind_plans_test_input.upcoming_daily_plans if (p.is_confirmation_required and p.is_urgent)
        ]

        # Act
        plans = await remind_use_case.lookup_plans(user_id=remind_plans_test_input.user_id)
        assert plans == sorted(plans, key=lambda p: p.next_scheduled_at)

        stale_plans, upcoming_daily_plans = remind_use_case.filter_late_and_daily_upcoming_daily_plans(
            plans=plans, trigger_datetime=remind_plans_test_input.now
        )

        # Assert
        assert len(stale_plans) == len(expected_input_stale_plans)
        assert len(upcoming_daily_plans) == len(expected_input_upcoming_daily_plans)

        assert stale_plans == sorted(expected_input_stale_plans, key=lambda p: p.next_scheduled_at)
        assert upcoming_daily_plans == sorted(expected_input_upcoming_daily_plans, key=lambda p: p.next_scheduled_at)

    async def test_remind_plan_summary_use_case_get_messages(
        self,
        dependency_bootstrapper: DependencyBootstrapper,
    ):
        stale_plans = TempPlanBuilder().build_n()
        upcoming_daily_plans = TempPlanBuilder().build_n()

        use_case = dependency_bootstrapper.get(interface=RemindPlanSummaryUseCase)
        messages = use_case._get_plan_messages(
            device_tokens=["test"],
            late_plans=stale_plans,
            upcoming_daily_plans=upcoming_daily_plans,
            now=datetime.now(timezone.utc),
        )

        assert len(messages) == len([i for i in (stale_plans, upcoming_daily_plans) if i])

    async def test_remind_plan_summary_use_case_get_stale_messages(
        self,
        dependency_bootstrapper: DependencyBootstrapper,
    ):
        stale_plans = TempPlanBuilder().build_n()

        use_case = dependency_bootstrapper.get(interface=RemindPlanSummaryUseCase)
        message = use_case._get_late_plan_message(
            device_tokens=["test"], late_plans=stale_plans, now=datetime.now(timezone.utc)
        )

        assert len(message.body.split("\n")) == len(stale_plans)

    async def test_remind_plan_summary_use_case_get_upcoming_messages(
        self,
        dependency_bootstrapper: DependencyBootstrapper,
    ):
        upcoming_daily_plans = TempPlanBuilder().build_n()

        use_case = dependency_bootstrapper.get(interface=RemindPlanSummaryUseCase)
        message = use_case._get_upcoming_daily_plan_messages(
            device_tokens=["test"], upcoming_daily_plans=upcoming_daily_plans
        )

        assert len(message.body.split("\n")) == len(upcoming_daily_plans)

    @pytest.mark.parametrize(
        "now, scheduled_at, expected_output",
        [
            (datetime(2023, 1, 31, 15, 0), datetime(2023, 1, 31, 12, 0), "3 hours"),
            (datetime(2023, 1, 31, 15, 0), datetime(2023, 1, 31, 14, 0), "1 hour"),
            (datetime(2023, 2, 2, 12, 0), datetime(2023, 1, 31, 12, 0), "2 days"),
            (datetime(2023, 2, 2, 12, 0), datetime(2023, 2, 1, 12, 0), "1 day"),
        ],
    )
    def test_calculate_plan_lateness_message(self, now, scheduled_at, expected_output):
        assert (
            RemindPlanSummaryUseCase.calculate_plan_lateness_message(now=now, scheduled_at=scheduled_at)
            == expected_output
        )

    @pytest.mark.parametrize(
        "plan, now, expected_output",
        [
            (
                TempPlanBuilder()
                .with_name(name="name")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=1, tzinfo=timezone.utc))
                .build(),
                datetime(year=2023, month=1, day=1, hour=1, minute=1, tzinfo=timezone.utc),
                "\u2022\tname is 1 minute late",
            ),
            (
                TempPlanBuilder()
                .with_name(name="name")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=1, tzinfo=timezone.utc))
                .build(),
                datetime(year=2023, month=1, day=1, hour=1, minute=20, tzinfo=timezone.utc),
                "\u2022\tname is 20 minutes late",
            ),
            (
                TempPlanBuilder()
                .with_name(name="name")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=1, tzinfo=timezone.utc))
                .build(),
                datetime(year=2023, month=1, day=1, hour=2, tzinfo=timezone.utc),
                "\u2022\tname is 1 hour late",
            ),
            (
                TempPlanBuilder()
                .with_name(name="test")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=1, tzinfo=timezone.utc))
                .build(),
                datetime(year=2023, month=1, day=1, hour=15, tzinfo=timezone.utc),
                "\u2022\ttest is 14 hours late",
            ),
            (
                TempPlanBuilder()
                .with_name(name="name")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=1, tzinfo=timezone.utc))
                .build(),
                datetime(year=2023, month=1, day=2, hour=2, tzinfo=timezone.utc),
                "\u2022\tname is 1 day late",
            ),
            (
                TempPlanBuilder()
                .with_name(name="test")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=1, tzinfo=timezone.utc))
                .build(),
                datetime(year=2023, month=1, day=3, hour=2, tzinfo=timezone.utc),
                "\u2022\ttest is 2 days late",
            ),
        ],
    )
    def test_generate_late_plan_message(self, plan, now, expected_output):
        assert RemindPlanSummaryUseCase.generate_late_plan_message(now=now, plan=plan) == expected_output

    @pytest.mark.parametrize(
        "plan, expected_output",
        [
            (
                TempPlanBuilder()
                .with_name(name="name")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=2, tzinfo=timezone.utc))
                .build(),
                "\u2022\tname at 02:00",
            ),
            (
                TempPlanBuilder()
                .with_name(name="test")
                .with_scheduled_at(scheduled_at=datetime(year=2023, month=1, day=1, hour=13, tzinfo=timezone.utc))
                .build(),
                "\u2022\ttest at 13:00",
            ),
        ],
    )
    def test_generate_upcoming_daily_plan_message(self, plan, expected_output):
        assert RemindPlanSummaryUseCase.generate_upcoming_daily_plan_message(plan=plan) == expected_output

    def test_generate_upcoming_daily_plans_title(self):
        plans = TempPlanBuilder().build_n()
        count = len(plans)
        assert (
            RemindPlanSummaryUseCase.generate_upcoming_daily_plans_title(upcoming_plans=plans)
            == f"Today, you have {count} plan{'' if count == 1 else 's'}"
        )

    def test_generate_late_plans_title(self):
        plans = TempPlanBuilder().build_n()
        count = len(plans)
        assert (
            RemindPlanSummaryUseCase.generate_late_plans_title(late_plans=plans)
            == f"You have {count} late plan{'' if count == 1 else 's'}"
        )
