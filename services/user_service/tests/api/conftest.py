from typing import Callable

import pytest
from starlette.testclient import Test<PERSON>lient

from services.user_service.main import app


def get_path_operation(method: str, test_client: TestClient) -> Callable:
    return {
        "GET": test_client.get,
        "POST": test_client.post,
        "PATCH": test_client.patch,
        "DELETE": test_client.delete,
    }[method]


@pytest.fixture(scope="session")
def test_client():
    return TestClient(app)
