from typing import List
from uuid import UUID

from services.base.application.async_use_case_base import AsyncUse<PERSON>aseBase
from services.base.domain.repository.notification_inbox_repository import NotificationInboxRepository
from services.base.domain.schemas.inbox.member_user_notification_inbox import MemberUserNotificationInbox
from services.user_service.application.use_cases.notification_inbox.input_boundaries.create_notification_inbox_input_boundary import (
    CreateNotificationInboxBoundary,
)


class CreateNotificationUseCase(AsyncUseCaseBase):
    def __init__(self, notification_repository: NotificationInboxRepository):
        self.notification_repository = notification_repository

    async def execute_async(
        self,
        user_uuid: UUID,
        input_boundary: CreateNotificationInboxBoundary,
    ) -> List[MemberUserNotificationInbox]:
        notifications: List[MemberUserNotificationInbox] = self.convert_from_ib_to_domain(
            user_uuid=user_uuid, input_boundary=input_boundary
        )
        return await self.notification_repository.upsert(notifications)

    @staticmethod
    def convert_from_ib_to_domain(
        user_uuid: UUID, input_boundary: CreateNotificationInboxBoundary
    ) -> List[MemberUserNotificationInbox]:
        return [
            MemberUserNotificationInbox(
                user_uuid=user_uuid,
                timestamp=notification.timestamp,
                title=notification.title,
                message=notification.message,
                type=notification.type,
            )
            for notification in input_boundary.values
        ]
