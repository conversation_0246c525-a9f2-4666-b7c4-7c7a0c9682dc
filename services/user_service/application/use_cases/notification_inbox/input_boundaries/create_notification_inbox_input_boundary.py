from typing import List

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.enums.notification import NotificationType
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.value_limits.notification_inbox import NotificationInboxValueLimit


class CreateNotificationInbox(BaseDataModel):
    title: str = Field(
        min_length=NotificationInboxValueLimit.MIN_TITLE, max_length=NotificationInboxValueLimit.MAX_TITLE
    )
    message: str = Field(
        min_length=NotificationInboxValueLimit.MIN_MESSAGE, max_length=NotificationInboxValueLimit.MAX_MESSAGE
    )
    timestamp: SerializableAwareDatetime
    type: NotificationType


class CreateNotificationInboxBoundary(BaseDataModel):
    values: List[CreateNotificationInbox] = Field(min_length=1)
