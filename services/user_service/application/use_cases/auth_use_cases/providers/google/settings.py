from typing import Dict, Optional

from services.base.application.constants import OAuth2Keys
from services.user_service.domain.providers.google.constants import GoogleOAuthKeys
from services.user_service.domain.settings import (
    OAuth2ProviderSettingsWrapper,
    OAuth2SettingsTemplate,
)
from settings.app_config import settings
from settings.app_secrets import secrets


class GoogleOAuth2SettingsTemplate(OAuth2SettingsTemplate):
    AUTH_URL: str = "https://accounts.google.com/o/oauth2/v2/auth"
    TOKEN_URL: str = "https://www.googleapis.com/oauth2/v4/token"
    AUTH_URL_PARAMS: Dict[OAuth2Keys | GoogleOAuthKeys, Optional[str]]
    TOKEN_URL_PARAMS: Dict[OAuth2Keys | GoogleOAuthKeys, Optional[str]]


class GoogleOAuth2Settings(OAuth2ProviderSettingsWrapper):
    @staticmethod
    def get_web_settings() -> GoogleOAuth2SettingsTemplate:
        return GoogleOAuth2SettingsTemplate(
            AUTH_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.GOOGLE_OAUTH2_WEB_CLIENT_ID,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.STATE: None,
                OAuth2Keys.SCOPE: None,
                OAuth2Keys.RESPONSE_TYPE: "code",
                GoogleOAuthKeys.ACCESS_TYPE: None,
                GoogleOAuthKeys.PROMPT: None,
                GoogleOAuthKeys.INCLUDE_GRANTED_SCOPES: None,
            },
            TOKEN_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.GOOGLE_OAUTH2_WEB_CLIENT_ID,
                OAuth2Keys.CLIENT_SECRET: secrets.GOOGLE_OAUTH2_WEB_CLIENT_SECRET,
                OAuth2Keys.CODE: None,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.GRANT_TYPE: "authorization_code",
                OAuth2Keys.STATE: None,
            },
        )

    @staticmethod
    def get_iOS_settings() -> GoogleOAuth2SettingsTemplate:
        return GoogleOAuth2SettingsTemplate(
            AUTH_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.GOOGLE_OAUTH2_IOS_CLIENT_ID,
                OAuth2Keys.CODE_CHALLENGE: secrets.GOOGLE_OAUTH2_CODE_CHALLENGE,
                OAuth2Keys.CODE_CHALLENGE_METHOD: "S256",
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.SCOPE: None,
                OAuth2Keys.STATE: None,
                OAuth2Keys.RESPONSE_TYPE: "code",
                GoogleOAuthKeys.ACCESS_TYPE: None,
                GoogleOAuthKeys.PROMPT: None,
                GoogleOAuthKeys.INCLUDE_GRANTED_SCOPES: None,
            },
            TOKEN_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.GOOGLE_OAUTH2_IOS_CLIENT_ID,
                OAuth2Keys.CODE_VERIFIER: secrets.GOOGLE_OAUTH2_CODE_VERIFIER,
                OAuth2Keys.CODE: None,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.GRANT_TYPE: "authorization_code",
                OAuth2Keys.STATE: None,
            },
        )
