from typing import Dict, Optional

from services.base.application.constants import OAuth2Keys
from services.user_service.domain.settings import (
    OAuth2ProviderSettingsWrapper,
    OAuth2SettingsTemplate,
)
from settings.app_config import settings
from settings.app_secrets import secrets


class AmazonOAuth2SettingsTemplate(OAuth2SettingsTemplate):
    AUTH_URL: str = "https://www.amazon.com/ap/oa"
    TOKEN_URL: str = "https://api.amazon.com/auth/o2/token"
    AUTH_URL_PARAMS: Dict[OAuth2Keys, Optional[str]]
    TOKEN_URL_PARAMS: Dict[OAuth2Keys, Optional[str]]


class AmazonOAuth2Settings(OAuth2ProviderSettingsWrapper):
    @staticmethod
    def get_web_settings() -> AmazonOAuth2SettingsTemplate:
        return AmazonOAuth2SettingsTemplate(
            AUTH_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.AMAZON_OAUTH2_WEB_CLIENT_ID,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.STATE: None,
                OAuth2Keys.SCOPE: None,
                OAuth2Keys.RESPONSE_TYPE: "code",
            },
            TOKEN_URL_PARAMS={
                OAuth2Keys.CLIENT_ID: settings.AMAZON_OAUTH2_WEB_CLIENT_ID,
                OAuth2Keys.CLIENT_SECRET: secrets.AMAZON_OAUTH2_WEB_CLIENT_SECRET,
                OAuth2Keys.CODE: None,
                OAuth2Keys.REDIRECT_URI: None,
                OAuth2Keys.GRANT_TYPE: "authorization_code",
                OAuth2Keys.STATE: None,
            },
        )
