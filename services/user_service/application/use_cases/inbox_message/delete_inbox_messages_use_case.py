from typing import List
from uuid import UUID

from services.base.application.async_use_case_base import AsyncUse<PERSON>aseB<PERSON>
from services.base.application.exceptions import InvalidPrivilegesException
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.schemas.identity import Identity


class DeleteInboxMessagesUseCase(AsyncUseCaseBase):
    def __init__(self, inbox_message_repository: InboxMessageRepository):
        self._inbox_message_repository = inbox_message_repository

    async def execute_async(
        self,
        identity: Identity,
        message_ids: List[UUID],
    ) -> List[UUID]:
        messages = await self._inbox_message_repository.search_by_id(ids=message_ids)

        for msg in messages:
            if msg.destination != identity:
                raise InvalidPrivilegesException(message="You need to be owner of all provided messages")

        return await self._inbox_message_repository.delete_by_id(ids=[message.id for message in messages])
